import React, { useState } from 'react'
import { Helmet } from 'react-helmet-async'
import { motion } from 'framer-motion'
import {
  InformationCircleIcon,
  CurrencyDollarIcon,
  ShieldCheckIcon,
  MapPinIcon,
  SunIcon,
  HeartIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline'
import AnimatedSection from '@/components/AnimatedSection'

const TravelTips: React.FC = () => {
  const [activeTab, setActiveTab] = useState('planning')

  const tabs = [
    { id: 'planning', label: 'Planning', icon: MapPinIcon },
    { id: 'budget', label: 'Budget', icon: CurrencyDollarIcon },
    { id: 'safety', label: 'Safety', icon: ShieldCheckIcon },
    { id: 'health', label: 'Health', icon: HeartIcon },
    { id: 'culture', label: 'Culture', icon: InformationCircleIcon },
    { id: 'weather', label: 'Weather', icon: SunIcon }
  ]

  const tipsData = {
    planning: {
      title: 'Planning Your Trip',
      tips: [
        {
          title: 'Best Time to Visit',
          content: 'December to March for west/south coasts, April to September for east coast. Avoid monsoon seasons.',
          icon: '📅'
        },
        {
          title: 'Visa Requirements',
          content: 'Most visitors need an Electronic Travel Authorization (ETA). Apply online before travel.',
          icon: '📋'
        },
        {
          title: 'Duration',
          content: 'Minimum 7-10 days recommended. 2-3 weeks ideal for comprehensive exploration.',
          icon: '⏰'
        },
        {
          title: 'Transportation',
          content: 'Trains for scenic routes, buses for budget travel, tuk-tuks for short distances, private drivers for comfort.',
          icon: '🚂'
        }
      ]
    },
    budget: {
      title: 'Budget Guidelines',
      tips: [
        {
          title: 'Daily Budget',
          content: 'Budget: $20-30, Mid-range: $50-80, Luxury: $150+ per day including accommodation and meals.',
          icon: '💰'
        },
        {
          title: 'Accommodation',
          content: 'Guesthouses: $10-25, Hotels: $30-80, Luxury resorts: $100-500+ per night.',
          icon: '🏨'
        },
        {
          title: 'Food Costs',
          content: 'Local meals: $2-5, Restaurant meals: $8-15, Fine dining: $25-50 per person.',
          icon: '🍽️'
        },
        {
          title: 'Money Tips',
          content: 'Carry cash (LKR), ATMs widely available, credit cards accepted in hotels and restaurants.',
          icon: '💳'
        }
      ]
    },
    safety: {
      title: 'Safety & Security',
      tips: [
        {
          title: 'General Safety',
          content: 'Sri Lanka is generally safe. Use common sense, avoid isolated areas at night, keep valuables secure.',
          icon: '🛡️'
        },
        {
          title: 'Transportation Safety',
          content: 'Choose reputable transport operators, wear seatbelts, be cautious with tuk-tuks in traffic.',
          icon: '🚗'
        },
        {
          title: 'Water Safety',
          content: 'Strong currents on some beaches. Swim at lifeguarded beaches, follow local advice.',
          icon: '🏊‍♂️'
        },
        {
          title: 'Emergency Numbers',
          content: 'Police: 119, Fire/Ambulance: 110, Tourist Police: 1912, Tourist Hotline: 1912.',
          icon: '📞'
        }
      ]
    },
    health: {
      title: 'Health & Medical',
      tips: [
        {
          title: 'Vaccinations',
          content: 'Routine vaccines up to date. Consider Hepatitis A/B, Typhoid, Japanese Encephalitis for some areas.',
          icon: '💉'
        },
        {
          title: 'Malaria Prevention',
          content: 'Low risk in most tourist areas. Use mosquito repellent, especially during dawn/dusk.',
          icon: '🦟'
        },
        {
          title: 'Food & Water',
          content: 'Drink bottled water, eat at busy restaurants, avoid street food initially until acclimatized.',
          icon: '🥤'
        },
        {
          title: 'Medical Care',
          content: 'Good private hospitals in Colombo and major cities. Travel insurance strongly recommended.',
          icon: '🏥'
        }
      ]
    },
    culture: {
      title: 'Cultural Etiquette',
      tips: [
        {
          title: 'Temple Visits',
          content: 'Remove shoes and hats, dress modestly, no photos of Buddha statues, maintain respectful silence.',
          icon: '🏛️'
        },
        {
          title: 'Dress Code',
          content: 'Conservative dress, especially at religious sites. Cover shoulders and knees, avoid tight clothing.',
          icon: '👕'
        },
        {
          title: 'Social Customs',
          content: 'Use right hand for eating/greeting, remove shoes when entering homes, respect elders.',
          icon: '🤝'
        },
        {
          title: 'Photography',
          content: 'Ask permission before photographing people, no photos at military installations or checkpoints.',
          icon: '📸'
        }
      ]
    },
    weather: {
      title: 'Weather & Climate',
      tips: [
        {
          title: 'Monsoon Seasons',
          content: 'Southwest monsoon (May-September), Northeast monsoon (October-January). Plan accordingly.',
          icon: '🌧️'
        },
        {
          title: 'Temperature',
          content: 'Coastal areas: 26-30°C, Hill country: 15-20°C, Hot and humid year-round in lowlands.',
          icon: '🌡️'
        },
        {
          title: 'What to Pack',
          content: 'Light cotton clothes, rain jacket, warm layers for hills, sun protection, comfortable shoes.',
          icon: '🎒'
        },
        {
          title: 'Seasonal Activities',
          content: 'Whale watching (Dec-Apr), Surfing varies by coast, Tea country best in dry season.',
          icon: '🏄‍♂️'
        }
      ]
    }
  }

  const essentialItems = [
    'Passport & visa documents',
    'Travel insurance',
    'Sunscreen (SPF 30+)',
    'Insect repellent',
    'First aid kit',
    'Comfortable walking shoes',
    'Light rain jacket',
    'Power adapter (Type D/G)',
    'Portable charger',
    'Cash in small denominations'
  ]

  return (
    <>
      <Helmet>
        <title>Travel Tips - Sri Lanka Tourist Guide</title>
        <meta name="description" content="Essential travel tips and advice for visiting Sri Lanka safely and comfortably." />
      </Helmet>

      {/* Hero Section */}
      <section className="bg-gradient-to-br from-green-50 to-blue-50 section-padding">
        <div className="container-max">
          <AnimatedSection animation="fadeIn" className="text-center mb-12">
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
              Travel <span className="text-gradient">Tips</span>
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Everything you need to know for a safe, comfortable, and memorable trip to Sri Lanka.
            </p>
          </AnimatedSection>
        </div>
      </section>

      {/* Tabbed Content */}
      <section className="section-padding bg-white">
        <div className="container-max">
          {/* Tab Navigation */}
          <AnimatedSection animation="slideUp" className="mb-12">
            <div className="flex flex-wrap justify-center gap-2 mb-8">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  type="button"
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                    activeTab === tab.id
                      ? 'bg-primary-600 text-white shadow-lg'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  <tab.icon className="h-5 w-5 mr-2" />
                  {tab.label}
                </button>
              ))}
            </div>
          </AnimatedSection>

          {/* Tab Content */}
          <AnimatedSection animation="fadeIn" key={activeTab}>
            <div className="max-w-4xl mx-auto">
              <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">
                {tipsData[activeTab as keyof typeof tipsData].title}
              </h2>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {tipsData[activeTab as keyof typeof tipsData].tips.map((tip, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    className="card p-6"
                  >
                    <div className="flex items-start">
                      <div className="text-3xl mr-4 flex-shrink-0">{tip.icon}</div>
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-2">
                          {tip.title}
                        </h3>
                        <p className="text-gray-600 leading-relaxed">
                          {tip.content}
                        </p>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </AnimatedSection>
        </div>
      </section>

      {/* Essential Packing List */}
      <section className="section-padding bg-gray-50">
        <div className="container-max">
          <AnimatedSection animation="slideUp" className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Essential Packing List
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Don't forget these important items for your Sri Lankan adventure.
            </p>
          </AnimatedSection>

          <AnimatedSection animation="stagger" staggerDelay={0.1}>
            <div className="max-w-3xl mx-auto">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {essentialItems.map((item, index) => (
                  <motion.div
                    key={index}
                    whileHover={{ scale: 1.02 }}
                    className="flex items-center p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-200"
                  >
                    <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                      <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <span className="text-gray-700 font-medium">{item}</span>
                  </motion.div>
                ))}
              </div>
            </div>
          </AnimatedSection>
        </div>
      </section>

      {/* Emergency Information */}
      <section className="section-padding bg-red-50">
        <div className="container-max">
          <AnimatedSection animation="slideUp" className="text-center mb-12">
            <div className="flex items-center justify-center mb-4">
              <ExclamationTriangleIcon className="h-8 w-8 text-red-600 mr-2" />
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
                Emergency Information
              </h2>
            </div>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Important contacts and information for emergencies during your stay.
            </p>
          </AnimatedSection>

          <AnimatedSection animation="stagger" staggerDelay={0.2}>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {[
                { service: 'Police', number: '119', icon: '🚔' },
                { service: 'Fire & Ambulance', number: '110', icon: '🚑' },
                { service: 'Tourist Police', number: '1912', icon: '👮‍♂️' },
                { service: 'Tourist Hotline', number: '1912', icon: '📞' }
              ].map((emergency, index) => (
                <motion.div
                  key={index}
                  whileHover={{ y: -5 }}
                  className="bg-white rounded-lg p-6 text-center shadow-md hover:shadow-lg transition-all duration-200"
                >
                  <div className="text-4xl mb-3">{emergency.icon}</div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    {emergency.service}
                  </h3>
                  <p className="text-2xl font-bold text-red-600">
                    {emergency.number}
                  </p>
                </motion.div>
              ))}
            </div>
          </AnimatedSection>
        </div>
      </section>

      {/* Call to Action */}
      <section className="section-padding bg-gradient-to-r from-green-600 to-blue-600 text-white">
        <div className="container-max text-center">
          <AnimatedSection animation="fadeIn">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Ready for Your Sri Lankan Adventure?
            </h2>
            <p className="text-xl mb-8 opacity-90">
              With these tips in mind, you're all set for an amazing journey through Sri Lanka.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="bg-white text-green-600 hover:bg-gray-100 font-medium py-3 px-8 rounded-lg transition-colors"
              >
                Download Travel Guide
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="border-2 border-white text-white hover:bg-white hover:text-green-600 font-medium py-3 px-8 rounded-lg transition-colors"
              >
                Contact Support
              </motion.button>
            </div>
          </AnimatedSection>
        </div>
      </section>
    </>
  )
}

export default TravelTips
