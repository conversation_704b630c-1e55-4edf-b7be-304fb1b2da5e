import{r as n,g as l,j as e,m as t}from"./animations-Dls6NBzi.js";import{S as c,H as p}from"./index-Dv-lgoNZ.js";import{L as s}from"./router-DklJj9iW.js";import{u as o}from"./useGSAP-X_TK1LFc.js";import{d}from"./destinations-CZhMEJGZ.js";import"./vendor-BtP0CW_r.js";l.registerPlugin(c);const g={"@context":"https://schema.org","@type":"TravelAgency",name:"Sri Lanka Tourist Guide",description:"Comprehensive travel guide for Sri Lanka featuring destinations, activities, cultural insights, and practical travel tips for 2025",url:"https://srilankaguide.com",logo:"https://srilankaguide.com/logo.png",sameAs:["https://facebook.com/srilankaguide","https://instagram.com/srilankaguide","https://twitter.com/srilankaguide"],areaServed:{"@type":"Country",name:"Sri Lanka"},hasOfferCatalog:{"@type":"OfferCatalog",name:"Sri Lanka Travel Destinations",itemListElement:d.slice(0,5).map((a,r)=>({"@type":"TouristDestination",position:r+1,name:a.name,description:a.shortDescription,geo:{"@type":"GeoCoordinates",latitude:a.coordinates.lat,longitude:a.coordinates.lng}}))}},b=()=>{const a=n.useRef(null),r=o("fadeIn",.2),m=o("scaleIn",.1);return n.useEffect(()=>(a.current&&l.to(a.current,{yPercent:-50,ease:"none",scrollTrigger:{trigger:a.current,start:"top bottom",end:"bottom top",scrub:!0}}),()=>{c.getAll().forEach(i=>i.kill())}),[]),e.jsxs(e.Fragment,{children:[e.jsxs(p,{children:[e.jsx("title",{children:"Sri Lanka Travel Guide 2025 - Best Destinations, Activities & Tips | Pearl of Indian Ocean"}),e.jsx("meta",{name:"description",content:"Comprehensive Sri Lanka travel guide 2025 featuring top destinations like Sigiriya, Kandy, Ella, cultural sites, wildlife safaris, beaches, travel tips, and practical information for your perfect Sri Lankan adventure."}),e.jsx("meta",{name:"keywords",content:"Sri Lanka travel guide 2025, Sri Lanka tourism, best places visit Sri Lanka, Sigiriya, Kandy, Ella, Galle Fort, Sri Lanka destinations, Sri Lanka activities, Sri Lanka culture, travel tips Sri Lanka"}),e.jsx("meta",{property:"og:type",content:"website"}),e.jsx("meta",{property:"og:url",content:"https://srilankaguide.com/"}),e.jsx("meta",{property:"og:title",content:"Sri Lanka Travel Guide 2025 - Discover the Pearl of Indian Ocean"}),e.jsx("meta",{property:"og:description",content:"Explore Sri Lanka's stunning destinations, rich culture, and unforgettable experiences with our comprehensive travel guide featuring top attractions, activities, and insider tips."}),e.jsx("meta",{property:"og:image",content:"https://srilankaguide.com/images/sri-lanka-hero.jpg"}),e.jsx("meta",{property:"og:image:width",content:"1200"}),e.jsx("meta",{property:"og:image:height",content:"630"}),e.jsx("meta",{property:"og:site_name",content:"Sri Lanka Tourist Guide"}),e.jsx("meta",{property:"og:locale",content:"en_US"}),e.jsx("meta",{property:"twitter:card",content:"summary_large_image"}),e.jsx("meta",{property:"twitter:url",content:"https://srilankaguide.com/"}),e.jsx("meta",{property:"twitter:title",content:"Sri Lanka Travel Guide 2025 - Discover the Pearl of Indian Ocean"}),e.jsx("meta",{property:"twitter:description",content:"Explore Sri Lanka's stunning destinations, rich culture, and unforgettable experiences with our comprehensive travel guide."}),e.jsx("meta",{property:"twitter:image",content:"https://srilankaguide.com/images/sri-lanka-hero.jpg"}),e.jsx("meta",{name:"robots",content:"index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1"}),e.jsx("meta",{name:"author",content:"Sri Lanka Tourist Guide"}),e.jsx("meta",{name:"publisher",content:"Sri Lanka Tourist Guide"}),e.jsx("meta",{name:"language",content:"English"}),e.jsx("meta",{name:"geo.region",content:"LK"}),e.jsx("meta",{name:"geo.country",content:"Sri Lanka"}),e.jsx("meta",{name:"geo.placename",content:"Sri Lanka"}),e.jsx("link",{rel:"canonical",href:"https://srilankaguide.com/"}),e.jsx("script",{type:"application/ld+json",children:JSON.stringify(g)})]}),e.jsxs("section",{className:"relative min-h-screen flex items-center overflow-hidden",children:[e.jsx("div",{ref:a,className:"absolute inset-0 hero-gradient",style:{backgroundImage:"linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(147, 51, 234, 0.1) 100%)"}}),e.jsx("div",{className:"container-max section-padding relative z-10",children:e.jsxs("div",{className:"text-center",children:[e.jsxs(t.h1,{initial:{opacity:0,y:50,scale:.9},animate:{opacity:1,y:0,scale:1},transition:{duration:1,ease:"easeOut"},className:"text-4xl md:text-6xl lg:text-7xl font-bold text-gray-900 mb-6",children:["Sri Lanka Travel Guide 2025:"," ",e.jsx("span",{className:"text-gradient bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"Pearl of the Indian Ocean"})]}),e.jsx(t.p,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.3},className:"text-xl md:text-2xl text-gray-600 mb-8 max-w-4xl mx-auto leading-relaxed",children:"Discover Sri Lanka's best destinations, cultural treasures, wildlife adventures, and pristine beaches. Your complete guide to exploring ancient temples, UNESCO World Heritage sites, tea plantations, and unforgettable experiences in 2025."}),e.jsxs(t.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.6},className:"flex flex-col sm:flex-row gap-4 justify-center",children:[e.jsx(s,{to:"/destinations",className:"btn-primary transform hover:scale-105 transition-transform",children:"Explore Destinations"}),e.jsx(s,{to:"/activities",className:"btn-secondary transform hover:scale-105 transition-transform",children:"Discover Activities"})]})]})}),e.jsx("div",{className:"absolute top-20 left-10 w-20 h-20 bg-blue-200 rounded-full opacity-20 animate-bounce",style:{animationDelay:"0s"}}),e.jsx("div",{className:"absolute top-40 right-20 w-16 h-16 bg-purple-200 rounded-full opacity-20 animate-bounce",style:{animationDelay:"1s"}}),e.jsx("div",{className:"absolute bottom-40 left-20 w-12 h-12 bg-yellow-200 rounded-full opacity-20 animate-bounce",style:{animationDelay:"2s"}})]}),e.jsx("section",{className:"section-padding bg-gray-50","aria-labelledby":"sri-lanka-facts",children:e.jsxs("div",{className:"container-max",children:[e.jsxs("div",{className:"text-center mb-12",children:[e.jsx("h2",{id:"sri-lanka-facts",className:"text-3xl md:text-4xl font-bold text-gray-900 mb-4",children:"Sri Lanka by the Numbers"}),e.jsx("p",{className:"text-lg text-gray-600 max-w-2xl mx-auto",children:"Discover what makes Sri Lanka one of the world's most diverse and fascinating travel destinations"})]}),e.jsxs("div",{ref:m,className:"grid grid-cols-2 md:grid-cols-4 gap-8 text-center",children:[e.jsxs("div",{className:"gsap-scale-in",children:[e.jsx("div",{className:"text-4xl md:text-5xl font-bold text-primary-600 mb-2",children:"8"}),e.jsx("div",{className:"text-gray-600 font-medium",children:"UNESCO World Heritage Sites"}),e.jsx("div",{className:"text-sm text-gray-500 mt-1",children:"Including Sigiriya & Kandy"})]}),e.jsxs("div",{className:"gsap-scale-in",children:[e.jsx("div",{className:"text-4xl md:text-5xl font-bold text-primary-600 mb-2",children:"26"}),e.jsx("div",{className:"text-gray-600 font-medium",children:"National Parks & Reserves"}),e.jsx("div",{className:"text-sm text-gray-500 mt-1",children:"Wildlife & Nature Protection"})]}),e.jsxs("div",{className:"gsap-scale-in",children:[e.jsx("div",{className:"text-4xl md:text-5xl font-bold text-primary-600 mb-2",children:"1,340"}),e.jsx("div",{className:"text-gray-600 font-medium",children:"Kilometers of Coastline"}),e.jsx("div",{className:"text-sm text-gray-500 mt-1",children:"Pristine Beaches & Surfing"})]}),e.jsxs("div",{className:"gsap-scale-in",children:[e.jsx("div",{className:"text-4xl md:text-5xl font-bold text-primary-600 mb-2",children:"2,500"}),e.jsx("div",{className:"text-gray-600 font-medium",children:"Years of Recorded History"}),e.jsx("div",{className:"text-sm text-gray-500 mt-1",children:"Ancient Civilization"})]})]})]})}),e.jsx("section",{className:"section-padding bg-white",children:e.jsxs("div",{className:"container-max",children:[e.jsxs("div",{className:"text-center mb-16",children:[e.jsx(t.h2,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-3xl md:text-5xl font-bold text-gray-900 mb-6",children:"Why Visit Sri Lanka?"}),e.jsx(t.p,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.2},viewport:{once:!0},className:"text-lg md:text-xl text-gray-600 max-w-3xl mx-auto",children:"Sri Lanka offers an incredible diversity of experiences in a compact island nation, making it the perfect destination for every type of traveler."})]}),e.jsxs("div",{ref:r,className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[e.jsxs("div",{className:"gsap-fade-in card p-8 text-center group hover:shadow-2xl transition-all duration-300",children:[e.jsx("div",{className:"text-6xl mb-6 group-hover:scale-110 transition-transform duration-300",children:"🏖️"}),e.jsx("h3",{className:"text-2xl font-semibold mb-4 text-gray-900",children:"Beautiful Beaches"}),e.jsx("p",{className:"text-gray-600 leading-relaxed",children:"Pristine coastlines with golden sands and crystal-clear waters perfect for relaxation, surfing, and water sports along both east and west coasts."})]}),e.jsxs("div",{className:"gsap-fade-in card p-8 text-center group hover:shadow-2xl transition-all duration-300",children:[e.jsx("div",{className:"text-6xl mb-6 group-hover:scale-110 transition-transform duration-300",children:"🏛️"}),e.jsx("h3",{className:"text-2xl font-semibold mb-4 text-gray-900",children:"Rich Heritage"}),e.jsx("p",{className:"text-gray-600 leading-relaxed",children:"Ancient temples, historical sites, and UNESCO World Heritage locations showcasing 2,500 years of continuous civilization and cultural heritage."})]}),e.jsxs("div",{className:"gsap-fade-in card p-8 text-center group hover:shadow-2xl transition-all duration-300",children:[e.jsx("div",{className:"text-6xl mb-6 group-hover:scale-110 transition-transform duration-300",children:"🌿"}),e.jsx("h3",{className:"text-2xl font-semibold mb-4 text-gray-900",children:"Natural Wonders"}),e.jsx("p",{className:"text-gray-600 leading-relaxed",children:"Lush rainforests, tea plantations, wildlife parks, and breathtaking landscapes offering incredible biodiversity in a compact area."})]})]})]})}),e.jsx("section",{className:"section-padding bg-gradient-to-br from-blue-50 to-purple-50",children:e.jsxs("div",{className:"container-max",children:[e.jsxs("div",{className:"text-center mb-16",children:[e.jsx(t.h2,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-3xl md:text-5xl font-bold text-gray-900 mb-6",children:"Featured Destinations"}),e.jsx(t.p,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.2},viewport:{once:!0},className:"text-lg text-gray-600 max-w-2xl mx-auto",children:"Discover some of Sri Lanka's most iconic and breathtaking destinations"})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:d.slice(0,3).map((i,x)=>e.jsxs(t.div,{initial:{opacity:0,y:50},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:x*.2},viewport:{once:!0},className:"card overflow-hidden group",children:[e.jsxs("div",{className:"relative h-64 bg-gradient-to-br from-blue-400 to-purple-500 overflow-hidden",children:[e.jsx("div",{className:"absolute inset-0 bg-black bg-opacity-20 group-hover:bg-opacity-10 transition-all duration-300"}),e.jsx("div",{className:"absolute bottom-4 left-4 text-white",children:e.jsx("span",{className:"inline-block px-3 py-1 bg-white bg-opacity-20 rounded-full text-sm font-medium mb-2",children:i.category})})]}),e.jsxs("div",{className:"p-6",children:[e.jsx("h3",{className:"text-xl font-semibold mb-2 text-gray-900",children:i.name}),e.jsx("p",{className:"text-gray-600 mb-4 line-clamp-3",children:i.shortDescription}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("span",{className:"text-sm text-gray-500",children:[i.province," Province"]}),e.jsx(s,{to:`/destinations/${i.id}`,className:"text-primary-600 hover:text-primary-700 font-medium text-sm",children:"Learn More →"})]})]})]},i.id))}),e.jsx("div",{className:"text-center mt-12",children:e.jsx(s,{to:"/destinations",className:"btn-primary",children:"View All Destinations"})})]})}),e.jsx("section",{className:"section-padding bg-gradient-to-r from-primary-600 to-purple-600 text-white",children:e.jsxs("div",{className:"container-max text-center",children:[e.jsx(t.h2,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-3xl md:text-4xl font-bold mb-6",children:"Ready to Start Your Sri Lankan Adventure?"}),e.jsx(t.p,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.2},viewport:{once:!0},className:"text-xl mb-8 max-w-2xl mx-auto opacity-90",children:"Plan your perfect trip with our comprehensive guides, insider tips, and local recommendations."}),e.jsxs(t.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.4},viewport:{once:!0},className:"flex flex-col sm:flex-row gap-4 justify-center",children:[e.jsx(s,{to:"/travel-tips",className:"bg-white text-primary-600 hover:bg-gray-100 font-medium py-3 px-8 rounded-lg transition-colors",children:"Get Travel Tips"}),e.jsx(s,{to:"/contact",className:"border-2 border-white text-white hover:bg-white hover:text-primary-600 font-medium py-3 px-8 rounded-lg transition-colors",children:"Contact Us"})]})]})})]})};export{b as default};
