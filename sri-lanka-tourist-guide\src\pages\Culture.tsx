import React from 'react'
import { Helmet } from 'react-helmet-async'
import { motion } from 'framer-motion'
import AnimatedSection from '@/components/AnimatedSection'

const Culture: React.FC = () => {
  const culturalAspects = [
    {
      title: 'Buddhism & Spirituality',
      description: 'Sri Lanka is home to one of the oldest Buddhist traditions in the world, with over 2,500 years of continuous practice.',
      icon: '🏛️',
      highlights: ['Sacred Tooth Relic', 'Ancient Temples', 'Meditation Practices', 'Buddhist Philosophy']
    },
    {
      title: 'Traditional Arts',
      description: 'Rich artistic traditions including classical dance, drumming, mask making, and intricate handicrafts.',
      icon: '🎭',
      highlights: ['Kandyan Dance', 'Traditional Drums', 'Wooden Masks', 'Batik Art']
    },
    {
      title: 'Festivals & Celebrations',
      description: 'Vibrant festivals throughout the year celebrating religious and cultural traditions with colorful processions.',
      icon: '🎉',
      highlights: ['Esala Perahera', 'Vesak Festival', 'Sinhala New Year', 'Diwali Celebrations']
    },
    {
      title: 'Cuisine & Spices',
      description: 'A unique culinary tradition influenced by Indian, Dutch, Portuguese, and British cultures with aromatic spices.',
      icon: '🍛',
      highlights: ['Rice & Curry', 'Hoppers', 'Ceylon Tea', 'Spice Gardens']
    },
    {
      title: 'Languages & Literature',
      description: 'Rich literary traditions in Sinhala and Tamil, with ancient texts and modern literature.',
      icon: '📚',
      highlights: ['Ancient Chronicles', 'Poetry Traditions', 'Folk Tales', 'Modern Literature']
    },
    {
      title: 'Architecture',
      description: 'Stunning architectural heritage from ancient kingdoms to colonial influences and modern designs.',
      icon: '🏗️',
      highlights: ['Ancient Stupas', 'Colonial Buildings', 'Traditional Houses', 'Modern Architecture']
    }
  ]

  const traditions = [
    {
      name: 'Ayurveda',
      description: 'Ancient healing system using natural herbs and treatments',
      image: '🌿'
    },
    {
      name: 'Gem Mining',
      description: 'Traditional methods of mining precious stones',
      image: '💎'
    },
    {
      name: 'Tea Culture',
      description: 'World-renowned Ceylon tea cultivation and ceremonies',
      image: '🍃'
    },
    {
      name: 'Fishing Traditions',
      description: 'Unique stilt fishing and traditional boat making',
      image: '🎣'
    }
  ]

  return (
    <>
      <Helmet>
        <title>Culture - Sri Lanka Tourist Guide</title>
        <meta name="description" content="Learn about Sri Lanka's rich cultural heritage and traditions spanning over 2,500 years." />
      </Helmet>

      {/* Hero Section */}
      <section className="bg-gradient-to-br from-orange-50 to-red-50 section-padding">
        <div className="container-max">
          <AnimatedSection animation="fadeIn" className="text-center mb-12">
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
              Sri Lankan <span className="text-gradient">Culture</span>
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Discover the rich tapestry of Sri Lankan culture, where ancient traditions
              blend seamlessly with modern life, creating a unique and vibrant heritage.
            </p>
          </AnimatedSection>
        </div>
      </section>

      {/* Cultural Aspects */}
      <section className="section-padding bg-white">
        <div className="container-max">
          <AnimatedSection animation="slideUp" className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Cultural Heritage
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Explore the diverse aspects of Sri Lankan culture that have been preserved
              and celebrated for generations.
            </p>
          </AnimatedSection>

          <AnimatedSection animation="stagger" staggerDelay={0.2}>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {culturalAspects.map((aspect, index) => (
                <motion.div
                  key={index}
                  whileHover={{ y: -5 }}
                  className="card p-6 text-center group"
                >
                  <div className="text-5xl mb-4 group-hover:scale-110 transition-transform duration-300">
                    {aspect.icon}
                  </div>
                  <h3 className="text-xl font-semibold mb-3 text-gray-900">
                    {aspect.title}
                  </h3>
                  <p className="text-gray-600 mb-4">
                    {aspect.description}
                  </p>
                  <div className="flex flex-wrap gap-2 justify-center">
                    {aspect.highlights.map((highlight, idx) => (
                      <span
                        key={idx}
                        className="inline-block px-3 py-1 bg-orange-100 text-orange-700 text-sm rounded-full"
                      >
                        {highlight}
                      </span>
                    ))}
                  </div>
                </motion.div>
              ))}
            </div>
          </AnimatedSection>
        </div>
      </section>

      {/* Traditional Practices */}
      <section className="section-padding bg-gray-50">
        <div className="container-max">
          <AnimatedSection animation="slideUp" className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Living Traditions
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Experience traditional practices that continue to thrive in modern Sri Lanka.
            </p>
          </AnimatedSection>

          <AnimatedSection animation="stagger" staggerDelay={0.15}>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {traditions.map((tradition, index) => (
                <motion.div
                  key={index}
                  whileHover={{ scale: 1.05 }}
                  className="bg-white rounded-lg p-6 text-center shadow-md hover:shadow-lg transition-all duration-300"
                >
                  <div className="text-4xl mb-4">{tradition.image}</div>
                  <h3 className="text-lg font-semibold mb-2 text-gray-900">
                    {tradition.name}
                  </h3>
                  <p className="text-gray-600 text-sm">
                    {tradition.description}
                  </p>
                </motion.div>
              ))}
            </div>
          </AnimatedSection>
        </div>
      </section>

      {/* Cultural Timeline */}
      <section className="section-padding bg-white">
        <div className="container-max">
          <AnimatedSection animation="slideUp" className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Cultural Timeline
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Journey through the major periods that shaped Sri Lankan culture.
            </p>
          </AnimatedSection>

          <AnimatedSection animation="stagger" staggerDelay={0.3}>
            <div className="relative">
              {/* Timeline line */}
              <div className="absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-gradient-to-b from-orange-400 to-red-400"></div>

              <div className="space-y-12">
                {[
                  { period: '543 BCE', title: 'Arrival of Buddhism', description: 'Prince Vijaya arrives, Buddhism introduced' },
                  { period: '377-1017 CE', title: 'Anuradhapura Kingdom', description: 'Golden age of Buddhist civilization' },
                  { period: '1017-1235 CE', title: 'Polonnaruwa Era', description: 'Medieval period of great architectural achievements' },
                  { period: '1505-1948', title: 'Colonial Period', description: 'Portuguese, Dutch, and British influences' },
                  { period: '1948-Present', title: 'Modern Sri Lanka', description: 'Independence and cultural renaissance' }
                ].map((era, index) => (
                  <div
                    key={index}
                    className={`flex items-center ${index % 2 === 0 ? 'flex-row' : 'flex-row-reverse'}`}
                  >
                    <div className={`w-1/2 ${index % 2 === 0 ? 'pr-8 text-right' : 'pl-8'}`}>
                      <div className="card p-6">
                        <div className="text-orange-600 font-bold text-lg mb-2">{era.period}</div>
                        <h3 className="text-xl font-semibold mb-2 text-gray-900">{era.title}</h3>
                        <p className="text-gray-600">{era.description}</p>
                      </div>
                    </div>
                    <div className="relative z-10">
                      <div className="w-4 h-4 bg-orange-500 rounded-full border-4 border-white shadow-lg"></div>
                    </div>
                    <div className="w-1/2"></div>
                  </div>
                ))}
              </div>
            </div>
          </AnimatedSection>
        </div>
      </section>

      {/* Call to Action */}
      <section className="section-padding bg-gradient-to-r from-orange-600 to-red-600 text-white">
        <div className="container-max text-center">
          <AnimatedSection animation="fadeIn">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Experience Sri Lankan Culture
            </h2>
            <p className="text-xl mb-8 opacity-90">
              Immerse yourself in the rich traditions and vibrant culture of Sri Lanka.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="bg-white text-orange-600 hover:bg-gray-100 font-medium py-3 px-8 rounded-lg transition-colors"
              >
                Cultural Tours
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="border-2 border-white text-white hover:bg-white hover:text-orange-600 font-medium py-3 px-8 rounded-lg transition-colors"
              >
                Festival Calendar
              </motion.button>
            </div>
          </AnimatedSection>
        </div>
      </section>
    </>
  )
}

export default Culture
