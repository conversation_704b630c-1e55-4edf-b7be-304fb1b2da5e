import{r as s,R as se}from"./animations-Dls6NBzi.js";import{a as ue}from"./vendor-BtP0CW_r.js";var ht=ue();/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function B(){return B=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},B.apply(this,arguments)}var R;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(R||(R={}));const J="popstate";function ce(e){e===void 0&&(e={});function t(r,a){let{pathname:l,search:i,hash:u}=r.location;return $("",{pathname:l,search:i,hash:u},a.state&&a.state.usr||null,a.state&&a.state.key||"default")}function n(r,a){return typeof a=="string"?a:T(a)}return he(t,n,null,e)}function v(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function Q(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function fe(){return Math.random().toString(36).substr(2,8)}function z(e,t){return{usr:e.state,key:e.key,idx:t}}function $(e,t,n,r){return n===void 0&&(n=null),B({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?U(t):t,{state:n,key:t&&t.key||r||fe()})}function T(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&n!=="?"&&(t+=n.charAt(0)==="?"?n:"?"+n),r&&r!=="#"&&(t+=r.charAt(0)==="#"?r:"#"+r),t}function U(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function he(e,t,n,r){r===void 0&&(r={});let{window:a=document.defaultView,v5Compat:l=!1}=r,i=a.history,u=R.Pop,o=null,f=h();f==null&&(f=0,i.replaceState(B({},i.state,{idx:f}),""));function h(){return(i.state||{idx:null}).idx}function c(){u=R.Pop;let d=h(),x=d==null?null:d-f;f=d,o&&o({action:u,location:m.location,delta:x})}function p(d,x){u=R.Push;let E=$(m.location,d,x);f=h()+1;let C=z(E,f),w=m.createHref(E);try{i.pushState(C,"",w)}catch(L){if(L instanceof DOMException&&L.name==="DataCloneError")throw L;a.location.assign(w)}l&&o&&o({action:u,location:m.location,delta:1})}function y(d,x){u=R.Replace;let E=$(m.location,d,x);f=h();let C=z(E,f),w=m.createHref(E);i.replaceState(C,"",w),l&&o&&o({action:u,location:m.location,delta:0})}function g(d){let x=a.location.origin!=="null"?a.location.origin:a.location.href,E=typeof d=="string"?d:T(d);return E=E.replace(/ $/,"%20"),v(x,"No window.location.(origin|href) available to create URL for href: "+E),new URL(E,x)}let m={get action(){return u},get location(){return e(a,i)},listen(d){if(o)throw new Error("A history only accepts one active listener");return a.addEventListener(J,c),o=d,()=>{a.removeEventListener(J,c),o=null}},createHref(d){return t(a,d)},createURL:g,encodeLocation(d){let x=g(d);return{pathname:x.pathname,search:x.search,hash:x.hash}},push:p,replace:y,go(d){return i.go(d)}};return m}var A;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(A||(A={}));function de(e,t,n){return n===void 0&&(n="/"),pe(e,t,n)}function pe(e,t,n,r){let a=typeof t=="string"?U(t):t,l=F(a.pathname||"/",n);if(l==null)return null;let i=Y(e);me(i);let u=null;for(let o=0;u==null&&o<i.length;++o){let f=Le(l);u=Re(i[o],f)}return u}function Y(e,t,n,r){t===void 0&&(t=[]),n===void 0&&(n=[]),r===void 0&&(r="");let a=(l,i,u)=>{let o={relativePath:u===void 0?l.path||"":u,caseSensitive:l.caseSensitive===!0,childrenIndex:i,route:l};o.relativePath.startsWith("/")&&(v(o.relativePath.startsWith(r),'Absolute route path "'+o.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),o.relativePath=o.relativePath.slice(r.length));let f=b([r,o.relativePath]),h=n.concat(o);l.children&&l.children.length>0&&(v(l.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+f+'".')),Y(l.children,t,h,f)),!(l.path==null&&!l.index)&&t.push({path:f,score:we(f,l.index),routesMeta:h})};return e.forEach((l,i)=>{var u;if(l.path===""||!((u=l.path)!=null&&u.includes("?")))a(l,i);else for(let o of Z(l.path))a(l,i,o)}),t}function Z(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,a=n.endsWith("?"),l=n.replace(/\?$/,"");if(r.length===0)return a?[l,""]:[l];let i=Z(r.join("/")),u=[];return u.push(...i.map(o=>o===""?l:[l,o].join("/"))),a&&u.push(...i),u.map(o=>e.startsWith("/")&&o===""?"/":o)}function me(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:Pe(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}const ve=/^:[\w-]+$/,ge=3,ye=2,xe=1,Ce=10,Ee=-2,K=e=>e==="*";function we(e,t){let n=e.split("/"),r=n.length;return n.some(K)&&(r+=Ee),t&&(r+=ye),n.filter(a=>!K(a)).reduce((a,l)=>a+(ve.test(l)?ge:l===""?xe:Ce),r)}function Pe(e,t){return e.length===t.length&&e.slice(0,-1).every((r,a)=>r===t[a])?e[e.length-1]-t[t.length-1]:0}function Re(e,t,n){let{routesMeta:r}=e,a={},l="/",i=[];for(let u=0;u<r.length;++u){let o=r[u],f=u===r.length-1,h=l==="/"?t:t.slice(l.length)||"/",c=be({path:o.relativePath,caseSensitive:o.caseSensitive,end:f},h),p=o.route;if(!c)return null;Object.assign(a,c.params),i.push({params:a,pathname:b([l,c.pathname]),pathnameBase:Ie(b([l,c.pathnameBase])),route:p}),c.pathnameBase!=="/"&&(l=b([l,c.pathnameBase]))}return i}function be(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=Se(e.path,e.caseSensitive,e.end),a=t.match(n);if(!a)return null;let l=a[0],i=l.replace(/(.)\/+$/,"$1"),u=a.slice(1);return{params:r.reduce((f,h,c)=>{let{paramName:p,isOptional:y}=h;if(p==="*"){let m=u[c]||"";i=l.slice(0,l.length-m.length).replace(/(.)\/+$/,"$1")}const g=u[c];return y&&!g?f[p]=void 0:f[p]=(g||"").replace(/%2F/g,"/"),f},{}),pathname:l,pathnameBase:i,pattern:e}}function Se(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!0),Q(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let r=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(i,u,o)=>(r.push({paramName:u,isOptional:o!=null}),o?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),a+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?a+="\\/*$":e!==""&&e!=="/"&&(a+="(?:(?=\\/|$))"),[new RegExp(a,t?void 0:"i"),r]}function Le(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return Q(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function F(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}function Ue(e,t){t===void 0&&(t="/");let{pathname:n,search:r="",hash:a=""}=typeof e=="string"?U(e):e;return{pathname:n?n.startsWith("/")?n:Oe(n,t):t,search:Ne(r),hash:Te(a)}}function Oe(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(a=>{a===".."?n.length>1&&n.pop():a!=="."&&n.push(a)}),n.length>1?n.join("/"):"/"}function k(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the ")+("`to."+n+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function Be(e){return e.filter((t,n)=>n===0||t.route.path&&t.route.path.length>0)}function M(e,t){let n=Be(e);return t?n.map((r,a)=>a===n.length-1?r.pathname:r.pathnameBase):n.map(r=>r.pathnameBase)}function D(e,t,n,r){r===void 0&&(r=!1);let a;typeof e=="string"?a=U(e):(a=B({},e),v(!a.pathname||!a.pathname.includes("?"),k("?","pathname","search",a)),v(!a.pathname||!a.pathname.includes("#"),k("#","pathname","hash",a)),v(!a.search||!a.search.includes("#"),k("#","search","hash",a)));let l=e===""||a.pathname==="",i=l?"/":a.pathname,u;if(i==null)u=n;else{let c=t.length-1;if(!r&&i.startsWith("..")){let p=i.split("/");for(;p[0]==="..";)p.shift(),c-=1;a.pathname=p.join("/")}u=c>=0?t[c]:"/"}let o=Ue(a,u),f=i&&i!=="/"&&i.endsWith("/"),h=(l||i===".")&&n.endsWith("/");return!o.pathname.endsWith("/")&&(f||h)&&(o.pathname+="/"),o}const b=e=>e.join("/").replace(/\/\/+/g,"/"),Ie=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),Ne=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,Te=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function je(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const ee=["post","put","patch","delete"];new Set(ee);const ke=["get",...ee];new Set(ke);/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function I(){return I=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},I.apply(this,arguments)}const V=s.createContext(null),$e=s.createContext(null),S=s.createContext(null),j=s.createContext(null),P=s.createContext({outlet:null,matches:[],isDataRoute:!1}),te=s.createContext(null);function _e(e,t){let{relative:n}=t===void 0?{}:t;O()||v(!1);let{basename:r,navigator:a}=s.useContext(S),{hash:l,pathname:i,search:u}=ae(e,{relative:n}),o=i;return r!=="/"&&(o=i==="/"?r:b([r,i])),a.createHref({pathname:o,search:u,hash:l})}function O(){return s.useContext(j)!=null}function N(){return O()||v(!1),s.useContext(j).location}function ne(e){s.useContext(S).static||s.useLayoutEffect(e)}function re(){let{isDataRoute:e}=s.useContext(P);return e?He():We()}function We(){O()||v(!1);let e=s.useContext(V),{basename:t,future:n,navigator:r}=s.useContext(S),{matches:a}=s.useContext(P),{pathname:l}=N(),i=JSON.stringify(M(a,n.v7_relativeSplatPath)),u=s.useRef(!1);return ne(()=>{u.current=!0}),s.useCallback(function(f,h){if(h===void 0&&(h={}),!u.current)return;if(typeof f=="number"){r.go(f);return}let c=D(f,JSON.parse(i),l,h.relative==="path");e==null&&t!=="/"&&(c.pathname=c.pathname==="/"?t:b([t,c.pathname])),(h.replace?r.replace:r.push)(c,h.state,h)},[t,r,i,l,e])}function dt(){let{matches:e}=s.useContext(P),t=e[e.length-1];return t?t.params:{}}function ae(e,t){let{relative:n}=t===void 0?{}:t,{future:r}=s.useContext(S),{matches:a}=s.useContext(P),{pathname:l}=N(),i=JSON.stringify(M(a,r.v7_relativeSplatPath));return s.useMemo(()=>D(e,JSON.parse(i),l,n==="path"),[e,i,l,n])}function Fe(e,t){return Me(e,t)}function Me(e,t,n,r){O()||v(!1);let{navigator:a}=s.useContext(S),{matches:l}=s.useContext(P),i=l[l.length-1],u=i?i.params:{};i&&i.pathname;let o=i?i.pathnameBase:"/";i&&i.route;let f=N(),h;if(t){var c;let d=typeof t=="string"?U(t):t;o==="/"||(c=d.pathname)!=null&&c.startsWith(o)||v(!1),h=d}else h=f;let p=h.pathname||"/",y=p;if(o!=="/"){let d=o.replace(/^\//,"").split("/");y="/"+p.replace(/^\//,"").split("/").slice(d.length).join("/")}let g=de(e,{pathname:y}),m=Ae(g&&g.map(d=>Object.assign({},d,{params:Object.assign({},u,d.params),pathname:b([o,a.encodeLocation?a.encodeLocation(d.pathname).pathname:d.pathname]),pathnameBase:d.pathnameBase==="/"?o:b([o,a.encodeLocation?a.encodeLocation(d.pathnameBase).pathname:d.pathnameBase])})),l,n,r);return t&&m?s.createElement(j.Provider,{value:{location:I({pathname:"/",search:"",hash:"",state:null,key:"default"},h),navigationType:R.Pop}},m):m}function De(){let e=Xe(),t=je(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,a={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return s.createElement(s.Fragment,null,s.createElement("h2",null,"Unexpected Application Error!"),s.createElement("h3",{style:{fontStyle:"italic"}},t),n?s.createElement("pre",{style:a},n):null,null)}const Ve=s.createElement(De,null);class Je extends s.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,n){return n.location!==t.location||n.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:n.error,location:n.location,revalidation:t.revalidation||n.revalidation}}componentDidCatch(t,n){console.error("React Router caught the following error during render",t,n)}render(){return this.state.error!==void 0?s.createElement(P.Provider,{value:this.props.routeContext},s.createElement(te.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function ze(e){let{routeContext:t,match:n,children:r}=e,a=s.useContext(V);return a&&a.static&&a.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(a.staticContext._deepestRenderedBoundaryId=n.route.id),s.createElement(P.Provider,{value:t},r)}function Ae(e,t,n,r){var a;if(t===void 0&&(t=[]),n===void 0&&(n=null),r===void 0&&(r=null),e==null){var l;if(!n)return null;if(n.errors)e=n.matches;else if((l=r)!=null&&l.v7_partialHydration&&t.length===0&&!n.initialized&&n.matches.length>0)e=n.matches;else return null}let i=e,u=(a=n)==null?void 0:a.errors;if(u!=null){let h=i.findIndex(c=>c.route.id&&(u==null?void 0:u[c.route.id])!==void 0);h>=0||v(!1),i=i.slice(0,Math.min(i.length,h+1))}let o=!1,f=-1;if(n&&r&&r.v7_partialHydration)for(let h=0;h<i.length;h++){let c=i[h];if((c.route.HydrateFallback||c.route.hydrateFallbackElement)&&(f=h),c.route.id){let{loaderData:p,errors:y}=n,g=c.route.loader&&p[c.route.id]===void 0&&(!y||y[c.route.id]===void 0);if(c.route.lazy||g){o=!0,f>=0?i=i.slice(0,f+1):i=[i[0]];break}}}return i.reduceRight((h,c,p)=>{let y,g=!1,m=null,d=null;n&&(y=u&&c.route.id?u[c.route.id]:void 0,m=c.route.errorElement||Ve,o&&(f<0&&p===0?(Qe("route-fallback"),g=!0,d=null):f===p&&(g=!0,d=c.route.hydrateFallbackElement||null)));let x=t.concat(i.slice(0,p+1)),E=()=>{let C;return y?C=m:g?C=d:c.route.Component?C=s.createElement(c.route.Component,null):c.route.element?C=c.route.element:C=h,s.createElement(ze,{match:c,routeContext:{outlet:h,matches:x,isDataRoute:n!=null},children:C})};return n&&(c.route.ErrorBoundary||c.route.errorElement||p===0)?s.createElement(Je,{location:n.location,revalidation:n.revalidation,component:m,error:y,children:E(),routeContext:{outlet:null,matches:x,isDataRoute:!0}}):E()},null)}var le=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(le||{}),ie=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(ie||{});function Ke(e){let t=s.useContext(V);return t||v(!1),t}function qe(e){let t=s.useContext($e);return t||v(!1),t}function Ge(e){let t=s.useContext(P);return t||v(!1),t}function oe(e){let t=Ge(),n=t.matches[t.matches.length-1];return n.route.id||v(!1),n.route.id}function Xe(){var e;let t=s.useContext(te),n=qe(),r=oe();return t!==void 0?t:(e=n.errors)==null?void 0:e[r]}function He(){let{router:e}=Ke(le.UseNavigateStable),t=oe(ie.UseNavigateStable),n=s.useRef(!1);return ne(()=>{n.current=!0}),s.useCallback(function(a,l){l===void 0&&(l={}),n.current&&(typeof a=="number"?e.navigate(a):e.navigate(a,I({fromRouteId:t},l)))},[e,t])}const q={};function Qe(e,t,n){q[e]||(q[e]=!0)}function Ye(e,t){e==null||e.v7_startTransition,e==null||e.v7_relativeSplatPath}function pt(e){let{to:t,replace:n,state:r,relative:a}=e;O()||v(!1);let{future:l,static:i}=s.useContext(S),{matches:u}=s.useContext(P),{pathname:o}=N(),f=re(),h=D(t,M(u,l.v7_relativeSplatPath),o,a==="path"),c=JSON.stringify(h);return s.useEffect(()=>f(JSON.parse(c),{replace:n,state:r,relative:a}),[f,c,a,n,r]),null}function Ze(e){v(!1)}function et(e){let{basename:t="/",children:n=null,location:r,navigationType:a=R.Pop,navigator:l,static:i=!1,future:u}=e;O()&&v(!1);let o=t.replace(/^\/*/,"/"),f=s.useMemo(()=>({basename:o,navigator:l,static:i,future:I({v7_relativeSplatPath:!1},u)}),[o,u,l,i]);typeof r=="string"&&(r=U(r));let{pathname:h="/",search:c="",hash:p="",state:y=null,key:g="default"}=r,m=s.useMemo(()=>{let d=F(h,o);return d==null?null:{location:{pathname:d,search:c,hash:p,state:y,key:g},navigationType:a}},[o,h,c,p,y,g,a]);return m==null?null:s.createElement(S.Provider,{value:f},s.createElement(j.Provider,{children:n,value:m}))}function mt(e){let{children:t,location:n}=e;return Fe(_(t),n)}new Promise(()=>{});function _(e,t){t===void 0&&(t=[]);let n=[];return s.Children.forEach(e,(r,a)=>{if(!s.isValidElement(r))return;let l=[...t,a];if(r.type===s.Fragment){n.push.apply(n,_(r.props.children,l));return}r.type!==Ze&&v(!1),!r.props.index||!r.props.children||v(!1);let i={id:r.props.id||l.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(i.children=_(r.props.children,l)),n.push(i)}),n}/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function W(){return W=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},W.apply(this,arguments)}function tt(e,t){if(e==null)return{};var n={},r=Object.keys(e),a,l;for(l=0;l<r.length;l++)a=r[l],!(t.indexOf(a)>=0)&&(n[a]=e[a]);return n}function nt(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function rt(e,t){return e.button===0&&(!t||t==="_self")&&!nt(e)}const at=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],lt="6";try{window.__reactRouterVersion=lt}catch{}const it="startTransition",G=se[it];function vt(e){let{basename:t,children:n,future:r,window:a}=e,l=s.useRef();l.current==null&&(l.current=ce({window:a,v5Compat:!0}));let i=l.current,[u,o]=s.useState({action:i.action,location:i.location}),{v7_startTransition:f}=r||{},h=s.useCallback(c=>{f&&G?G(()=>o(c)):o(c)},[o,f]);return s.useLayoutEffect(()=>i.listen(h),[i,h]),s.useEffect(()=>Ye(r),[r]),s.createElement(et,{basename:t,children:n,location:u.location,navigationType:u.action,navigator:i,future:r})}const ot=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",st=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,gt=s.forwardRef(function(t,n){let{onClick:r,relative:a,reloadDocument:l,replace:i,state:u,target:o,to:f,preventScrollReset:h,viewTransition:c}=t,p=tt(t,at),{basename:y}=s.useContext(S),g,m=!1;if(typeof f=="string"&&st.test(f)&&(g=f,ot))try{let C=new URL(window.location.href),w=f.startsWith("//")?new URL(C.protocol+f):new URL(f),L=F(w.pathname,y);w.origin===C.origin&&L!=null?f=L+w.search+w.hash:m=!0}catch{}let d=_e(f,{relative:a}),x=ut(f,{replace:i,state:u,target:o,preventScrollReset:h,relative:a,viewTransition:c});function E(C){r&&r(C),C.defaultPrevented||x(C)}return s.createElement("a",W({},p,{href:g||d,onClick:m||l?r:E,ref:n,target:o}))});var X;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(X||(X={}));var H;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(H||(H={}));function ut(e,t){let{target:n,replace:r,state:a,preventScrollReset:l,relative:i,viewTransition:u}=t===void 0?{}:t,o=re(),f=N(),h=ae(e,{relative:i});return s.useCallback(c=>{if(rt(c,n)){c.preventDefault();let p=r!==void 0?r:T(f)===T(h);o(e,{replace:p,state:a,preventScrollReset:l,relative:i,viewTransition:u})}},[f,o,h,r,a,n,e,l,i,u])}export{vt as B,gt as L,pt as N,mt as R,Ze as a,dt as b,ht as r,N as u};
