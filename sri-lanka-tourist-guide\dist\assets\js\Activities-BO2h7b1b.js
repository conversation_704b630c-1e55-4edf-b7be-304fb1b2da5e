import{r as d,j as e,m as n}from"./animations-Dls6NBzi.js";import{H as b}from"./index-DoUPJfA4.js";import{u as v}from"./useGSAP-KRu8-WuZ.js";import{F as j}from"./MagnifyingGlassIcon-DvDOtkAW.js";import{F as w}from"./CurrencyDollarIcon-BeUMafu9.js";import{F as N}from"./MapPinIcon-DxqRjeFE.js";import{F as k}from"./ClockIcon-Bop423fN.js";import"./vendor-BtP0CW_r.js";import"./router-DklJj9iW.js";const S=[{id:"whale-watching-mirissa",name:"Whale Watching in Mirissa",description:"Experience the thrill of spotting blue whales, sperm whales, and dolphins in their natural habitat off the southern coast of Sri Lanka.",category:"wildlife",location:"Mirissa",duration:"3-4 hours",difficulty:"easy",bestTime:"November to April",price:{min:25,max:50,currency:"USD"},includes:["Boat transportation","Life jackets","Light refreshments","Professional guide"],requirements:["No age restrictions","Basic swimming ability recommended","Motion sickness medication if prone to seasickness"],images:["/images/activities/whale-watching-1.jpg","/images/activities/whale-watching-2.jpg"],highlights:["Blue whale sightings (largest animals on Earth)","Dolphin pods","Sperm whale encounters","Ocean sunrise experience"]},{id:"safari-yala",name:"Yala National Park Safari",description:"Explore Sri Lanka's most famous national park, home to the highest density of leopards in the world, along with elephants, sloth bears, and diverse bird species.",category:"wildlife",location:"Yala National Park",duration:"6-8 hours",difficulty:"easy",bestTime:"February to July",price:{min:40,max:80,currency:"USD"},includes:["4WD safari vehicle","Professional tracker/guide","Park entrance fees","Packed lunch","Binoculars"],requirements:["Early morning departure (5:30 AM)","Comfortable clothing in earth tones","Sun protection"],images:["/images/activities/yala-safari-1.jpg","/images/activities/yala-safari-2.jpg"],highlights:["Sri Lankan leopard sightings","Asian elephant herds","Sloth bear encounters","Over 200 bird species","Ancient rock formations"]},{id:"white-water-rafting",name:"White Water Rafting - Kelani River",description:"Navigate through exciting rapids and enjoy the scenic beauty of the Kelani River valley on this thrilling white water rafting adventure.",category:"adventure",location:"Kitulgala",duration:"4-5 hours",difficulty:"moderate",bestTime:"May to December",price:{min:30,max:60,currency:"USD"},includes:["Safety equipment (helmet, life jacket)","Professional rafting guide","Transportation to/from starting point","Safety briefing","Certificate of completion"],requirements:["Minimum age 12 years","Good swimming ability","Physical fitness","No serious medical conditions"],images:["/images/activities/rafting-1.jpg","/images/activities/rafting-2.jpg"],highlights:["Grade 2-3 rapids","Scenic rainforest views","Team building experience","Adrenaline rush","Professional photography available"]},{id:"cooking-class",name:"Traditional Sri Lankan Cooking Class",description:"Learn to prepare authentic Sri Lankan dishes using traditional spices and cooking methods in a local family setting.",category:"food",location:"Various locations",duration:"3-4 hours",difficulty:"easy",bestTime:"Year-round",price:{min:20,max:40,currency:"USD"},includes:["All ingredients and spices","Recipe cards to take home","Full meal (what you cook)","Tea/coffee","Market visit (some classes)"],requirements:["No cooking experience necessary","Inform about dietary restrictions","Comfortable clothing"],images:["/images/activities/cooking-class-1.jpg","/images/activities/cooking-class-2.jpg"],highlights:["Learn about Sri Lankan spices","Traditional cooking methods","Family-style dining experience","Cultural exchange","Take recipes home"]},{id:"hot-air-balloon",name:"Hot Air Balloon Ride",description:"Soar above the cultural triangle and ancient cities, enjoying breathtaking aerial views of temples, forests, and rural landscapes.",category:"adventure",location:"Dambulla/Sigiriya area",duration:"3-4 hours (1 hour flight)",difficulty:"easy",bestTime:"April to September",price:{min:150,max:250,currency:"USD"},includes:["Hot air balloon flight","Pre-flight briefing","Champagne breakfast","Flight certificate","Transportation to/from launch site"],requirements:["Minimum age 5 years","Good physical condition","Early morning departure (5:00 AM)","Weather dependent"],images:["/images/activities/balloon-1.jpg","/images/activities/balloon-2.jpg"],highlights:["Aerial views of Sigiriya Rock","Ancient temple complexes from above","Sunrise flight experience","Champagne celebration","Unique photography opportunities"]}],E=()=>{var h;const[a,m]=d.useState(""),[t,r]=d.useState("all"),[s,u]=d.useState("all"),p=v("fadeIn",.1),x=["all","adventure","cultural","wildlife","water-sports","wellness","food"],g=[{value:"all",label:"All Prices"},{value:"budget",label:"Under $30"},{value:"mid",label:"$30 - $100"},{value:"luxury",label:"Over $100"}],l=d.useMemo(()=>S.filter(i=>{const f=i.name.toLowerCase().includes(a.toLowerCase())||i.description.toLowerCase().includes(a.toLowerCase()),y=t==="all"||i.category===t;let o=!0;if(s!=="all"){const c=i.price.min;switch(s){case"budget":o=c<30;break;case"mid":o=c>=30&&c<=100;break;case"luxury":o=c>100;break}}return f&&y&&o}),[a,t,s]);return e.jsxs(e.Fragment,{children:[e.jsxs(b,{children:[e.jsx("title",{children:"Activities - Sri Lanka Tourist Guide"}),e.jsx("meta",{name:"description",content:"Discover exciting activities and experiences in Sri Lanka."})]}),e.jsx("section",{className:"bg-gradient-to-br from-green-50 to-blue-50 section-padding",children:e.jsxs("div",{className:"container-max",children:[e.jsxs(n.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8},className:"text-center mb-12",children:[e.jsxs("h1",{className:"text-4xl md:text-6xl font-bold text-gray-900 mb-6",children:["Exciting ",e.jsx("span",{className:"text-gradient",children:"Activities"})]}),e.jsx("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:"From thrilling adventures to cultural experiences, discover the best activities that Sri Lanka has to offer for every type of traveler."})]}),e.jsx(n.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.2},className:"bg-white rounded-2xl shadow-lg p-6 mb-12",children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs("div",{className:"relative",children:[e.jsx(j,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400"}),e.jsx("input",{type:"text",placeholder:"Search activities...",value:a,onChange:i=>m(i.target.value),className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"})]}),e.jsx("select",{value:t,onChange:i=>r(i.target.value),"aria-label":"Filter by category",className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent",children:x.map(i=>e.jsx("option",{value:i,children:i==="all"?"All Categories":i.charAt(0).toUpperCase()+i.slice(1).replace("-"," ")},i))}),e.jsx("select",{value:s,onChange:i=>u(i.target.value),"aria-label":"Filter by price range",className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent",children:g.map(i=>e.jsx("option",{value:i.value,children:i.label},i.value))})]})})]})}),e.jsx("section",{className:"section-padding bg-white",children:e.jsx("div",{className:"container-max",children:l.length===0?e.jsxs(n.div,{initial:{opacity:0},animate:{opacity:1},className:"text-center py-16",children:[e.jsx("div",{className:"text-6xl mb-4",children:"🔍"}),e.jsx("h3",{className:"text-2xl font-semibold text-gray-900 mb-2",children:"No activities found"}),e.jsx("p",{className:"text-gray-600",children:"Try adjusting your search criteria"})]}):e.jsxs(e.Fragment,{children:[e.jsxs(n.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"flex items-center justify-between mb-8",children:[e.jsxs("h2",{className:"text-2xl font-semibold text-gray-900",children:[l.length," activit",l.length!==1?"ies":"y"," found"]}),e.jsxs("div",{className:"text-sm text-gray-500",children:[t!=="all"&&`${t.replace("-"," ")} activities`,s!=="all"&&` • ${(h=g.find(i=>i.value===s))==null?void 0:h.label}`]})]}),e.jsx("div",{ref:p,className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:l.map(i=>e.jsx("div",{className:"gsap-fade-in",children:e.jsx(C,{activity:i})},i.id))})]})})})]})},C=({activity:a})=>{const m=r=>{switch(r){case"easy":return"text-green-600 bg-green-100";case"moderate":return"text-yellow-600 bg-yellow-100";case"challenging":return"text-red-600 bg-red-100";default:return"text-gray-600 bg-gray-100"}},t=r=>{switch(r){case"adventure":return"🏔️";case"cultural":return"🏛️";case"wildlife":return"🦁";case"water-sports":return"🏄‍♂️";case"wellness":return"🧘‍♀️";case"food":return"🍽️";default:return"🎯"}};return e.jsxs(n.div,{whileHover:{y:-5},transition:{duration:.3},className:"card overflow-hidden group",children:[e.jsxs("div",{className:"relative h-64 bg-gradient-to-br from-green-400 to-blue-500 overflow-hidden",children:[e.jsx("div",{className:"absolute inset-0 bg-black bg-opacity-20 group-hover:bg-opacity-10 transition-all duration-300"}),e.jsx("div",{className:"absolute top-4 left-4",children:e.jsxs("span",{className:"inline-flex items-center px-3 py-1 bg-white bg-opacity-90 rounded-full text-sm font-medium text-gray-900",children:[e.jsx("span",{className:"mr-1",children:t(a.category)}),a.category.replace("-"," ")]})}),e.jsx("div",{className:"absolute top-4 right-4",children:e.jsx("span",{className:`inline-block px-3 py-1 rounded-full text-xs font-medium ${m(a.difficulty)}`,children:a.difficulty})}),e.jsx("div",{className:"absolute bottom-4 right-4",children:e.jsxs("span",{className:"inline-flex items-center bg-white bg-opacity-90 text-gray-900 text-sm font-semibold px-3 py-1 rounded-full",children:[e.jsx(w,{className:"h-4 w-4 mr-1"}),a.price.min,"-",a.price.max]})})]}),e.jsxs("div",{className:"p-6",children:[e.jsx("h3",{className:"text-xl font-semibold mb-2 text-gray-900 group-hover:text-primary-600 transition-colors",children:a.name}),e.jsx("p",{className:"text-gray-600 mb-4 line-clamp-2",children:a.description}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 text-sm text-gray-500 mb-4",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(N,{className:"h-4 w-4 mr-1"}),a.location]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(k,{className:"h-4 w-4 mr-1"}),a.duration]})]}),e.jsx("div",{className:"mb-4",children:e.jsxs("div",{className:"flex flex-wrap gap-1",children:[a.highlights.slice(0,2).map((r,s)=>e.jsx("span",{className:"inline-block px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded",children:r},s)),a.highlights.length>2&&e.jsxs("span",{className:"inline-block px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded",children:["+",a.highlights.length-2," more"]})]})}),e.jsxs("div",{className:"text-sm text-gray-500 mb-4",children:[e.jsx("strong",{children:"Best time:"})," ",a.bestTime]}),e.jsx("button",{type:"button",className:"w-full bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-4 rounded-lg transition-colors",children:"Book Activity"})]})]})};export{E as default};
