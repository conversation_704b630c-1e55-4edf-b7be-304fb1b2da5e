import{r as m,g as n,j as x}from"./animations-Dls6NBzi.js";import{S as l}from"./index-DoUPJfA4.js";n.registerPlugin(l);const A=({children:y,animation:e="fadeIn",delay:r=0,duration:s=.8,trigger:f="viewport",className:u="",staggerDelay:i=.1})=>{const c=m.useRef(null);return m.useEffect(()=>{if(!c.current)return;const t=c.current,a=e==="stagger",p=a?t.children:t,o={fadeIn:{from:{opacity:0,y:30},to:{opacity:1,y:0}},slideUp:{from:{opacity:0,y:50},to:{opacity:1,y:0}},slideLeft:{from:{opacity:0,x:-50},to:{opacity:1,x:0}},slideRight:{from:{opacity:0,x:50},to:{opacity:1,x:0}},scaleIn:{from:{opacity:0,scale:.8},to:{opacity:1,scale:1}},stagger:{from:{opacity:0,y:30},to:{opacity:1,y:0}}}[e];return f==="immediate"?n.fromTo(p,o.from,{...o.to,duration:s,delay:r,ease:"power2.out",stagger:a?i:0}):n.fromTo(p,o.from,{...o.to,duration:s,delay:r,ease:"power2.out",stagger:a?i:0,scrollTrigger:{trigger:t,start:"top 80%",end:"bottom 20%",toggleActions:"play none none reverse"}}),()=>{l.getAll().forEach(g=>{g.trigger===t&&g.kill()})}},[e,r,s,f,i]),x.jsx("div",{ref:c,className:u,children:y})};export{A};
