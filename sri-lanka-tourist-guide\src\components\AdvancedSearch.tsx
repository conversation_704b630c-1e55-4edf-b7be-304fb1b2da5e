import React, { useState, useEffect, useMemo } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  MagnifyingGlassIcon,
  FunnelIcon,
  XMarkIcon,
  MapPinIcon,
  ClockIcon
} from '@heroicons/react/24/outline'
import { destinations } from '@/data/destinations'
import { activities } from '@/data/activities'

interface SearchResult {
  id: string
  type: 'destination' | 'activity'
  name: string
  description: string
  category: string
  location?: string
  highlights: string[]
  url: string
}

interface AdvancedSearchProps {
  onResultSelect?: (result: SearchResult) => void
  className?: string
}

const AdvancedSearch: React.FC<AdvancedSearchProps> = ({ onResultSelect, className = '' }) => {
  const [searchTerm, setSearchTerm] = useState('')
  const [showFilters, setShowFilters] = useState(false)
  const [selectedType, setSelectedType] = useState<'all' | 'destinations' | 'activities'>('all')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [selectedProvince, setSelectedProvince] = useState('all')

  const [results, setResults] = useState<SearchResult[]>([])
  const [showResults, setShowResults] = useState(false)

  // Create searchable data
  const searchData = useMemo(() => {
    const destinationData: SearchResult[] = destinations.map(dest => ({
      id: dest.id,
      type: 'destination' as const,
      name: dest.name,
      description: dest.description,
      category: dest.category,
      location: `${dest.province} Province`,
      highlights: dest.highlights,
      url: `/destinations/${dest.id}`
    }))

    const activityData: SearchResult[] = activities.map(activity => ({
      id: activity.id,
      type: 'activity' as const,
      name: activity.name,
      description: activity.description,
      category: activity.category,
      location: activity.location,
      highlights: activity.highlights || [],
      url: `/activities#${activity.id}`
    }))

    return [...destinationData, ...activityData]
  }, [])

  // Filter and search logic
  useEffect(() => {
    if (!searchTerm.trim() && selectedType === 'all' && selectedCategory === 'all') {
      setResults([])
      setShowResults(false)
      return
    }

    let filtered = searchData

    // Filter by type
    if (selectedType !== 'all') {
      filtered = filtered.filter(item => 
        selectedType === 'destinations' ? item.type === 'destination' : item.type === 'activity'
      )
    }

    // Filter by category
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(item => item.category === selectedCategory)
    }

    // Search by term
    if (searchTerm.trim()) {
      const term = searchTerm.toLowerCase()
      filtered = filtered.filter(item =>
        item.name.toLowerCase().includes(term) ||
        item.description.toLowerCase().includes(term) ||
        item.location?.toLowerCase().includes(term) ||
        item.highlights.some(highlight => highlight.toLowerCase().includes(term))
      )
    }

    // Sort by relevance (exact name matches first, then partial matches)
    filtered.sort((a, b) => {
      if (!searchTerm.trim()) return 0
      
      const term = searchTerm.toLowerCase()
      const aNameMatch = a.name.toLowerCase().includes(term)
      const bNameMatch = b.name.toLowerCase().includes(term)
      
      if (aNameMatch && !bNameMatch) return -1
      if (!aNameMatch && bNameMatch) return 1
      
      return a.name.localeCompare(b.name)
    })

    setResults(filtered.slice(0, 10)) // Limit to 10 results
    setShowResults(filtered.length > 0 || searchTerm.trim().length > 0)
  }, [searchTerm, selectedType, selectedCategory, searchData])

  const categories = [
    'all', 'cultural', 'nature', 'beach', 'adventure', 'city', 'water-sports', 'wildlife', 'wellness'
  ]

  const provinces = [
    'all', 'Western', 'Central', 'Southern', 'Northern', 'Eastern', 'North Western', 
    'North Central', 'Uva', 'Sabaragamuwa'
  ]

  const handleResultClick = (result: SearchResult) => {
    if (onResultSelect) {
      onResultSelect(result)
    }
    setShowResults(false)
    setSearchTerm('')
  }

  const highlightText = (text: string, term: string) => {
    if (!term.trim()) return text
    
    const regex = new RegExp(`(${term})`, 'gi')
    const parts = text.split(regex)
    
    return parts.map((part, index) => 
      regex.test(part) ? (
        <mark key={index} className="bg-yellow-200 text-yellow-900 px-1 rounded">
          {part}
        </mark>
      ) : part
    )
  }

  return (
    <div className={`relative ${className}`}>
      {/* Search Input */}
      <div className="relative">
        <MagnifyingGlassIcon className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
        <input
          type="text"
          placeholder="Search destinations, activities, or experiences..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          onFocus={() => setShowResults(true)}
          className="w-full pl-12 pr-12 py-4 text-lg border-2 border-gray-200 rounded-xl focus:outline-none focus:border-primary-500 focus:ring-2 focus:ring-primary-200 transition-all duration-300"
        />
        
        {/* Filter Toggle */}
        <button
          onClick={() => setShowFilters(!showFilters)}
          className={`absolute right-4 top-1/2 transform -translate-y-1/2 p-2 rounded-lg transition-colors ${
            showFilters ? 'bg-primary-100 text-primary-600' : 'text-gray-400 hover:text-gray-600'
          }`}
        >
          <FunnelIcon className="h-5 w-5" />
        </button>
      </div>

      {/* Advanced Filters */}
      <AnimatePresence>
        {showFilters && (
          <motion.div
            initial={{ opacity: 0, y: -10, height: 0 }}
            animate={{ opacity: 1, y: 0, height: 'auto' }}
            exit={{ opacity: 0, y: -10, height: 0 }}
            className="mt-4 p-6 bg-white border border-gray-200 rounded-xl shadow-lg"
          >
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Advanced Filters</h3>
              <button
                onClick={() => setShowFilters(false)}
                className="p-1 hover:bg-gray-100 rounded-full transition-colors"
              >
                <XMarkIcon className="h-5 w-5 text-gray-500" />
              </button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* Type Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Type</label>
                <select
                  value={selectedType}
                  onChange={(e) => setSelectedType(e.target.value as any)}
                  className="w-full p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                >
                  <option value="all">All Types</option>
                  <option value="destinations">Destinations</option>
                  <option value="activities">Activities</option>
                </select>
              </div>

              {/* Category Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Category</label>
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                >
                  {categories.map(category => (
                    <option key={category} value={category}>
                      {category === 'all' ? 'All Categories' : category.charAt(0).toUpperCase() + category.slice(1)}
                    </option>
                  ))}
                </select>
              </div>

              {/* Province Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Province</label>
                <select
                  value={selectedProvince}
                  onChange={(e) => setSelectedProvince(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                >
                  {provinces.map(province => (
                    <option key={province} value={province}>
                      {province === 'all' ? 'All Provinces' : `${province} Province`}
                    </option>
                  ))}
                </select>
              </div>

              {/* Clear Filters */}
              <div className="flex items-end">
                <button
                  onClick={() => {
                    setSelectedType('all')
                    setSelectedCategory('all')
                    setSelectedProvince('all')
                  }}
                  className="w-full px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
                >
                  Clear Filters
                </button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Search Results */}
      <AnimatePresence>
        {showResults && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 10 }}
            className="absolute top-full left-0 right-0 mt-2 bg-white border border-gray-200 rounded-xl shadow-xl z-50 max-h-96 overflow-y-auto"
          >
            {results.length > 0 ? (
              <div className="p-2">
                {results.map((result) => (
                  <motion.button
                    key={`${result.type}-${result.id}`}
                    onClick={() => handleResultClick(result)}
                    className="w-full text-left p-4 hover:bg-gray-50 rounded-lg transition-colors"
                    whileHover={{ x: 5 }}
                  >
                    <div className="flex items-start space-x-3">
                      <div className={`p-2 rounded-lg ${
                        result.type === 'destination' ? 'bg-blue-100 text-blue-600' : 'bg-green-100 text-green-600'
                      }`}>
                        {result.type === 'destination' ? (
                          <MapPinIcon className="h-4 w-4" />
                        ) : (
                          <ClockIcon className="h-4 w-4" />
                        )}
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <h4 className="text-lg font-semibold text-gray-900 mb-1">
                          {highlightText(result.name, searchTerm)}
                        </h4>
                        <p className="text-sm text-gray-600 mb-2 line-clamp-2">
                          {highlightText(result.description.slice(0, 120) + '...', searchTerm)}
                        </p>
                        <div className="flex items-center space-x-4 text-xs text-gray-500">
                          <span className="capitalize">{result.type}</span>
                          <span>•</span>
                          <span className="capitalize">{result.category}</span>
                          {result.location && (
                            <>
                              <span>•</span>
                              <span>{result.location}</span>
                            </>
                          )}
                        </div>
                      </div>
                    </div>
                  </motion.button>
                ))}
              </div>
            ) : searchTerm.trim() ? (
              <div className="p-8 text-center">
                <div className="text-4xl mb-4">🔍</div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">No results found</h3>
                <p className="text-gray-600">Try adjusting your search terms or filters</p>
              </div>
            ) : null}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Backdrop to close results */}
      {showResults && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setShowResults(false)}
        />
      )}
    </div>
  )
}

export default AdvancedSearch
