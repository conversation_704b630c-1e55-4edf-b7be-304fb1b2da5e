import{r as d,j as e,m as i}from"./animations-Dls6NBzi.js";import{H as y}from"./index-Bxt4fWYT.js";import{a as j}from"./activities-C_PVl3tZ.js";import{u as v}from"./useGSAP-CAvLNQrn.js";import{F as N}from"./MagnifyingGlassIcon-DvDOtkAW.js";import{F as w}from"./CurrencyDollarIcon-BeUMafu9.js";import{F as C}from"./MapPinIcon-DxqRjeFE.js";import{F}from"./ClockIcon-Bop423fN.js";import"./vendor-BtP0CW_r.js";import"./router-Bcnr4W6T.js";const D=()=>{var g;const[a,m]=d.useState(""),[r,l]=d.useState("all"),[t,u]=d.useState("all"),p=v("fadeIn",.1),h=["all","adventure","cultural","wildlife","water-sports","wellness","food"],x=[{value:"all",label:"All Prices"},{value:"budget",label:"Under $30"},{value:"mid",label:"$30 - $100"},{value:"luxury",label:"Over $100"}],n=d.useMemo(()=>j.filter(s=>{const b=s.name.toLowerCase().includes(a.toLowerCase())||s.description.toLowerCase().includes(a.toLowerCase()),f=r==="all"||s.category===r;let c=!0;if(t!=="all"){const o=s.price.min;switch(t){case"budget":c=o<30;break;case"mid":c=o>=30&&o<=100;break;case"luxury":c=o>100;break}}return b&&f&&c}),[a,r,t]);return e.jsxs(e.Fragment,{children:[e.jsxs(y,{children:[e.jsx("title",{children:"Activities - Sri Lanka Tourist Guide"}),e.jsx("meta",{name:"description",content:"Discover exciting activities and experiences in Sri Lanka."})]}),e.jsx("section",{className:"bg-gradient-to-br from-green-50 to-blue-50 section-padding",children:e.jsxs("div",{className:"container-max",children:[e.jsxs(i.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8},className:"text-center mb-12",children:[e.jsxs("h1",{className:"text-4xl md:text-6xl font-bold text-gray-900 mb-6",children:["Exciting ",e.jsx("span",{className:"text-gradient",children:"Activities"})]}),e.jsx("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:"From thrilling adventures to cultural experiences, discover the best activities that Sri Lanka has to offer for every type of traveler."})]}),e.jsx(i.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.2},className:"bg-white rounded-2xl shadow-lg p-6 mb-12",children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs("div",{className:"relative",children:[e.jsx(N,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400"}),e.jsx("input",{type:"text",placeholder:"Search activities...",value:a,onChange:s=>m(s.target.value),className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"})]}),e.jsx("select",{value:r,onChange:s=>l(s.target.value),"aria-label":"Filter by category",className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent",children:h.map(s=>e.jsx("option",{value:s,children:s==="all"?"All Categories":s.charAt(0).toUpperCase()+s.slice(1).replace("-"," ")},s))}),e.jsx("select",{value:t,onChange:s=>u(s.target.value),"aria-label":"Filter by price range",className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent",children:x.map(s=>e.jsx("option",{value:s.value,children:s.label},s.value))})]})})]})}),e.jsx("section",{className:"section-padding bg-white",children:e.jsx("div",{className:"container-max",children:n.length===0?e.jsxs(i.div,{initial:{opacity:0},animate:{opacity:1},className:"text-center py-16",children:[e.jsx("div",{className:"text-6xl mb-4",children:"🔍"}),e.jsx("h3",{className:"text-2xl font-semibold text-gray-900 mb-2",children:"No activities found"}),e.jsx("p",{className:"text-gray-600",children:"Try adjusting your search criteria"})]}):e.jsxs(e.Fragment,{children:[e.jsxs(i.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"flex items-center justify-between mb-8",children:[e.jsxs("h2",{className:"text-2xl font-semibold text-gray-900",children:[n.length," activit",n.length!==1?"ies":"y"," found"]}),e.jsxs("div",{className:"text-sm text-gray-500",children:[r!=="all"&&`${r.replace("-"," ")} activities`,t!=="all"&&` • ${(g=x.find(s=>s.value===t))==null?void 0:g.label}`]})]}),e.jsx("div",{ref:p,className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:n.map(s=>e.jsx("div",{className:"gsap-fade-in",children:e.jsx(k,{activity:s})},s.id))})]})})})]})},k=({activity:a})=>{const m=l=>{switch(l){case"easy":return"text-green-600 bg-green-100";case"moderate":return"text-yellow-600 bg-yellow-100";case"challenging":return"text-red-600 bg-red-100";default:return"text-gray-600 bg-gray-100"}},r=l=>{switch(l){case"adventure":return"🏔️";case"cultural":return"🏛️";case"wildlife":return"🦁";case"water-sports":return"🏄‍♂️";case"wellness":return"🧘‍♀️";case"food":return"🍽️";default:return"🎯"}};return e.jsxs(i.div,{whileHover:{y:-5},transition:{duration:.3},className:"card overflow-hidden group",children:[e.jsxs("div",{className:"relative h-64 bg-gradient-to-br from-green-400 to-blue-500 overflow-hidden",children:[e.jsx("div",{className:"absolute inset-0 bg-black bg-opacity-20 group-hover:bg-opacity-10 transition-all duration-300"}),e.jsx("div",{className:"absolute top-4 left-4",children:e.jsxs("span",{className:"inline-flex items-center px-3 py-1 bg-white bg-opacity-90 rounded-full text-sm font-medium text-gray-900",children:[e.jsx("span",{className:"mr-1",children:r(a.category)}),a.category.replace("-"," ")]})}),e.jsx("div",{className:"absolute top-4 right-4",children:e.jsx("span",{className:`inline-block px-3 py-1 rounded-full text-xs font-medium ${m(a.difficulty)}`,children:a.difficulty})}),e.jsx("div",{className:"absolute bottom-4 right-4",children:e.jsxs("span",{className:"inline-flex items-center bg-white bg-opacity-90 text-gray-900 text-sm font-semibold px-3 py-1 rounded-full",children:[e.jsx(w,{className:"h-4 w-4 mr-1"}),a.price.min,"-",a.price.max]})})]}),e.jsxs("div",{className:"p-6",children:[e.jsx("h3",{className:"text-xl font-semibold mb-2 text-gray-900 group-hover:text-primary-600 transition-colors",children:a.name}),e.jsx("p",{className:"text-gray-600 mb-4 line-clamp-2",children:a.description}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 text-sm text-gray-500 mb-4",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(C,{className:"h-4 w-4 mr-1"}),a.location]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(F,{className:"h-4 w-4 mr-1"}),a.duration]})]}),e.jsx("div",{className:"mb-4",children:e.jsxs("div",{className:"flex flex-wrap gap-1",children:[a.highlights.slice(0,2).map((l,t)=>e.jsx("span",{className:"inline-block px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded",children:l},t)),a.highlights.length>2&&e.jsxs("span",{className:"inline-block px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded",children:["+",a.highlights.length-2," more"]})]})}),e.jsxs("div",{className:"text-sm text-gray-500 mb-4",children:[e.jsx("strong",{children:"Best time:"})," ",a.bestTime]}),e.jsx("button",{type:"button",className:"w-full bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-4 rounded-lg transition-colors",children:"Book Activity"})]})]})};export{D as default};
