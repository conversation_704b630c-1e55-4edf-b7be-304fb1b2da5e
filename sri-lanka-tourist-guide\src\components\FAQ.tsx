import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { ChevronDownIcon } from '@heroicons/react/24/outline'

interface FAQItem {
  question: string
  answer: string
  category: string
}

interface FAQProps {
  title?: string
  faqs: FAQItem[]
  showStructuredData?: boolean
}

const FAQ: React.FC<FAQProps> = ({ 
  title = "Frequently Asked Questions", 
  faqs, 
  showStructuredData = true 
}) => {
  const [openItems, setOpenItems] = useState<number[]>([])

  const toggleItem = (index: number) => {
    setOpenItems(prev => 
      prev.includes(index) 
        ? prev.filter(i => i !== index)
        : [...prev, index]
    )
  }

  // Structured data for FAQ
  const faqStructuredData = {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": faqs.map(faq => ({
      "@type": "Question",
      "name": faq.question,
      "acceptedAnswer": {
        "@type": "Answer",
        "text": faq.answer
      }
    }))
  }

  return (
    <section className="py-16 bg-white">
      <div className="container-max">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            {title}
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Get answers to the most common questions about traveling to Sri Lanka
          </p>
        </div>

        <div className="max-w-4xl mx-auto">
          {faqs.map((faq, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="mb-4"
            >
              <button
                onClick={() => toggleItem(index)}
                className="w-full text-left p-6 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500"
                aria-expanded={openItems.includes(index)}
              >
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-gray-900 pr-4">
                    {faq.question}
                  </h3>
                  <motion.div
                    animate={{ rotate: openItems.includes(index) ? 180 : 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    <ChevronDownIcon className="h-5 w-5 text-gray-500 flex-shrink-0" />
                  </motion.div>
                </div>
              </button>
              
              <AnimatePresence>
                {openItems.includes(index) && (
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: 'auto', opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    transition={{ duration: 0.3 }}
                    className="overflow-hidden"
                  >
                    <div className="p-6 pt-0">
                      <div className="text-gray-600 leading-relaxed whitespace-pre-line">
                        {faq.answer}
                      </div>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>
          ))}
        </div>

        {/* Structured Data */}
        {showStructuredData && (
          <script
            type="application/ld+json"
            dangerouslySetInnerHTML={{
              __html: JSON.stringify(faqStructuredData)
            }}
          />
        )}
      </div>
    </section>
  )
}

export default FAQ

// Common Sri Lanka Travel FAQs
export const sriLankaTravelFAQs: FAQItem[] = [
  {
    question: "Do I need a visa to visit Sri Lanka in 2025?",
    answer: "Yes, most visitors need an Electronic Travel Authorization (ETA). You can apply online at eta.gov.lk. The cost is USD 50 for a 30-day tourist visa. Processing takes 24-72 hours. Your passport must be valid for at least 6 months from your arrival date.",
    category: "visa"
  },
  {
    question: "What is the best time to visit Sri Lanka?",
    answer: "Sri Lanka has two monsoon seasons, so the best time depends on your destination:\n\n• West & South coasts (Colombo, Galle, Mirissa): December to March\n• East coast (Arugam Bay, Trincomalee): April to September\n• Hill country (Kandy, Ella, Nuwara Eliya): December to April (but pleasant year-round)\n• Cultural Triangle (Sigiriya, Anuradhapura): December to April",
    category: "planning"
  },
  {
    question: "How much money do I need for a trip to Sri Lanka?",
    answer: "Daily budget varies by travel style:\n\n• Budget backpacker: $25-40/day\n• Mid-range traveler: $60-100/day\n• Luxury traveler: $200-500+/day\n\nThis includes accommodation, meals, transportation, and activities. Sri Lanka offers excellent value for money compared to other Asian destinations.",
    category: "budget"
  },
  {
    question: "Is Sri Lanka safe for tourists?",
    answer: "Yes, Sri Lanka is generally very safe for tourists. The country has a low crime rate and locals are friendly and helpful. Standard travel precautions apply:\n\n• Use reputable transportation\n• Don't display expensive items\n• Be cautious swimming (strong currents on some beaches)\n• Follow local advice and government travel advisories",
    category: "safety"
  },
  {
    question: "What should I wear in Sri Lanka?",
    answer: "Dress modestly, especially when visiting temples:\n\n• Temples: Cover shoulders and knees, remove shoes and hats\n• General: Light, breathable fabrics due to tropical climate\n• Beaches: Normal swimwear is acceptable\n• Hill country: Bring layers as it can get cool\n• Avoid revealing clothing in public areas",
    category: "culture"
  },
  {
    question: "How do I get around Sri Lanka?",
    answer: "Multiple transportation options:\n\n• Trains: Scenic routes, especially Colombo-Kandy-Ella\n• Buses: Extensive network, very affordable\n• Tuk-tuks: Short distances, negotiate fare beforehand\n• Private driver: Most comfortable, $40-80/day\n• Rental car: Possible but challenging traffic\n• Domestic flights: Limited routes, mainly to Jaffna",
    category: "transportation"
  },
  {
    question: "What vaccinations do I need for Sri Lanka?",
    answer: "No vaccinations are mandatory for most travelers. Recommended:\n\n• Routine vaccines (MMR, DPT, flu, COVID-19)\n• Hepatitis A and B\n• Typhoid\n• Japanese Encephalitis (if visiting rural areas)\n• Malaria prophylaxis (low risk, consult doctor)\n\nConsult a travel medicine specialist 4-6 weeks before travel.",
    category: "health"
  },
  {
    question: "Can I drink tap water in Sri Lanka?",
    answer: "It's recommended to drink bottled or filtered water. Tap water quality varies and may cause stomach upset for visitors. Bottled water is widely available and inexpensive. Avoid ice in drinks unless you're sure it's made from purified water.",
    category: "health"
  },
  {
    question: "What currency is used in Sri Lanka?",
    answer: "Sri Lankan Rupee (LKR) is the local currency. USD and EUR are easily exchanged. ATMs are widely available in cities and tourist areas. Credit cards are accepted in hotels, restaurants, and shops, but carry cash for small vendors, tuk-tuks, and entrance fees.",
    category: "money"
  },
  {
    question: "How long should I spend in Sri Lanka?",
    answer: "Recommended duration:\n\n• 7-10 days: Highlights tour (Colombo, Kandy, Sigiriya, Galle)\n• 2 weeks: Comprehensive tour including hill country and beaches\n• 3-4 weeks: In-depth exploration including east coast and cultural sites\n• 1 month+: Slow travel, volunteering, or extended stays",
    category: "planning"
  }
]
