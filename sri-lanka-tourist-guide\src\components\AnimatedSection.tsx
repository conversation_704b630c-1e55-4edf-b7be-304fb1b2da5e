import React, { useEffect, useRef } from 'react'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'

gsap.registerPlugin(ScrollTrigger)

interface AnimatedSectionProps {
  children: React.ReactNode
  animation?: 'fadeIn' | 'slideUp' | 'slideLeft' | 'slideRight' | 'scaleIn' | 'stagger'
  delay?: number
  duration?: number
  trigger?: 'viewport' | 'immediate'
  className?: string
  staggerDelay?: number
}

const AnimatedSection: React.FC<AnimatedSectionProps> = ({
  children,
  animation = 'fadeIn',
  delay = 0,
  duration = 0.8,
  trigger = 'viewport',
  className = '',
  staggerDelay = 0.1
}) => {
  const ref = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (!ref.current) return

    const element = ref.current
    const isStagger = animation === 'stagger'
    const targets = isStagger ? element.children : element

    // Animation configurations
    const animations = {
      fadeIn: {
        from: { opacity: 0, y: 30 },
        to: { opacity: 1, y: 0 }
      },
      slideUp: {
        from: { opacity: 0, y: 50 },
        to: { opacity: 1, y: 0 }
      },
      slideLeft: {
        from: { opacity: 0, x: -50 },
        to: { opacity: 1, x: 0 }
      },
      slideRight: {
        from: { opacity: 0, x: 50 },
        to: { opacity: 1, x: 0 }
      },
      scaleIn: {
        from: { opacity: 0, scale: 0.8 },
        to: { opacity: 1, scale: 1 }
      },
      stagger: {
        from: { opacity: 0, y: 30 },
        to: { opacity: 1, y: 0 }
      }
    }

    const config = animations[animation]
    
    if (trigger === 'immediate') {
      // Immediate animation
      gsap.fromTo(targets, config.from, {
        ...config.to,
        duration,
        delay,
        ease: 'power2.out',
        stagger: isStagger ? staggerDelay : 0
      })
    } else {
      // Scroll-triggered animation
      gsap.fromTo(targets, config.from, {
        ...config.to,
        duration,
        delay,
        ease: 'power2.out',
        stagger: isStagger ? staggerDelay : 0,
        scrollTrigger: {
          trigger: element,
          start: 'top 80%',
          end: 'bottom 20%',
          toggleActions: 'play none none reverse'
        }
      })
    }

    // Cleanup
    return () => {
      ScrollTrigger.getAll().forEach(trigger => {
        if (trigger.trigger === element) {
          trigger.kill()
        }
      })
    }
  }, [animation, delay, duration, trigger, staggerDelay])

  return (
    <div ref={ref} className={className}>
      {children}
    </div>
  )
}

export default AnimatedSection
