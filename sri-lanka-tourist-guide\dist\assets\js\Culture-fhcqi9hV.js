import{j as e,m as s}from"./animations-Dls6NBzi.js";import{H as d}from"./index-Dv-lgoNZ.js";import{A as t}from"./AnimatedSection-DtEgkEn2.js";import"./vendor-BtP0CW_r.js";import"./router-DklJj9iW.js";const u=()=>{const r=[{title:"Buddhism & Spirituality",description:"Sri Lanka is home to one of the oldest Buddhist traditions in the world, with over 2,500 years of continuous practice.",icon:"🏛️",highlights:["Sacred Tooth Relic","Ancient Temples","Meditation Practices","Buddhist Philosophy"]},{title:"Traditional Arts",description:"Rich artistic traditions including classical dance, drumming, mask making, and intricate handicrafts.",icon:"🎭",highlights:["Kandyan Dance","Traditional Drums","Wooden Masks","Batik Art"]},{title:"Festivals & Celebrations",description:"Vibrant festivals throughout the year celebrating religious and cultural traditions with colorful processions.",icon:"🎉",highlights:["Esala Perahera","Vesak Festival","Sinhala New Year","Diwali Celebrations"]},{title:"Cuisine & Spices",description:"A unique culinary tradition influenced by Indian, Dutch, Portuguese, and British cultures with aromatic spices.",icon:"🍛",highlights:["Rice & Curry","Hoppers","Ceylon Tea","Spice Gardens"]},{title:"Languages & Literature",description:"Rich literary traditions in Sinhala and Tamil, with ancient texts and modern literature.",icon:"📚",highlights:["Ancient Chronicles","Poetry Traditions","Folk Tales","Modern Literature"]},{title:"Architecture",description:"Stunning architectural heritage from ancient kingdoms to colonial influences and modern designs.",icon:"🏗️",highlights:["Ancient Stupas","Colonial Buildings","Traditional Houses","Modern Architecture"]}],n=[{name:"Ayurveda",description:"Ancient healing system using natural herbs and treatments",image:"🌿"},{name:"Gem Mining",description:"Traditional methods of mining precious stones",image:"💎"},{name:"Tea Culture",description:"World-renowned Ceylon tea cultivation and ceremonies",image:"🍃"},{name:"Fishing Traditions",description:"Unique stilt fishing and traditional boat making",image:"🎣"}];return e.jsxs(e.Fragment,{children:[e.jsxs(d,{children:[e.jsx("title",{children:"Culture - Sri Lanka Tourist Guide"}),e.jsx("meta",{name:"description",content:"Learn about Sri Lanka's rich cultural heritage and traditions spanning over 2,500 years."})]}),e.jsx("section",{className:"bg-gradient-to-br from-orange-50 to-red-50 section-padding",children:e.jsx("div",{className:"container-max",children:e.jsxs(t,{animation:"fadeIn",className:"text-center mb-12",children:[e.jsxs("h1",{className:"text-4xl md:text-6xl font-bold text-gray-900 mb-6",children:["Sri Lankan ",e.jsx("span",{className:"text-gradient",children:"Culture"})]}),e.jsx("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:"Discover the rich tapestry of Sri Lankan culture, where ancient traditions blend seamlessly with modern life, creating a unique and vibrant heritage."})]})})}),e.jsx("section",{className:"section-padding bg-white",children:e.jsxs("div",{className:"container-max",children:[e.jsxs(t,{animation:"slideUp",className:"text-center mb-16",children:[e.jsx("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-6",children:"Cultural Heritage"}),e.jsx("p",{className:"text-lg text-gray-600 max-w-2xl mx-auto",children:"Explore the diverse aspects of Sri Lankan culture that have been preserved and celebrated for generations."})]}),e.jsx(t,{animation:"stagger",staggerDelay:.2,children:e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:r.map((i,a)=>e.jsxs(s.div,{whileHover:{y:-5},className:"card p-6 text-center group",children:[e.jsx("div",{className:"text-5xl mb-4 group-hover:scale-110 transition-transform duration-300",children:i.icon}),e.jsx("h3",{className:"text-xl font-semibold mb-3 text-gray-900",children:i.title}),e.jsx("p",{className:"text-gray-600 mb-4",children:i.description}),e.jsx("div",{className:"flex flex-wrap gap-2 justify-center",children:i.highlights.map((l,o)=>e.jsx("span",{className:"inline-block px-3 py-1 bg-orange-100 text-orange-700 text-sm rounded-full",children:l},o))})]},a))})})]})}),e.jsx("section",{className:"section-padding bg-gray-50",children:e.jsxs("div",{className:"container-max",children:[e.jsxs(t,{animation:"slideUp",className:"text-center mb-16",children:[e.jsx("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-6",children:"Living Traditions"}),e.jsx("p",{className:"text-lg text-gray-600 max-w-2xl mx-auto",children:"Experience traditional practices that continue to thrive in modern Sri Lanka."})]}),e.jsx(t,{animation:"stagger",staggerDelay:.15,children:e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:n.map((i,a)=>e.jsxs(s.div,{whileHover:{scale:1.05},className:"bg-white rounded-lg p-6 text-center shadow-md hover:shadow-lg transition-all duration-300",children:[e.jsx("div",{className:"text-4xl mb-4",children:i.image}),e.jsx("h3",{className:"text-lg font-semibold mb-2 text-gray-900",children:i.name}),e.jsx("p",{className:"text-gray-600 text-sm",children:i.description})]},a))})})]})}),e.jsx("section",{className:"section-padding bg-white",children:e.jsxs("div",{className:"container-max",children:[e.jsxs(t,{animation:"slideUp",className:"text-center mb-16",children:[e.jsx("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-6",children:"Cultural Timeline"}),e.jsx("p",{className:"text-lg text-gray-600 max-w-2xl mx-auto",children:"Journey through the major periods that shaped Sri Lankan culture."})]}),e.jsx(t,{animation:"stagger",staggerDelay:.3,children:e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-gradient-to-b from-orange-400 to-red-400"}),e.jsx("div",{className:"space-y-12",children:[{period:"543 BCE",title:"Arrival of Buddhism",description:"Prince Vijaya arrives, Buddhism introduced"},{period:"377-1017 CE",title:"Anuradhapura Kingdom",description:"Golden age of Buddhist civilization"},{period:"1017-1235 CE",title:"Polonnaruwa Era",description:"Medieval period of great architectural achievements"},{period:"1505-1948",title:"Colonial Period",description:"Portuguese, Dutch, and British influences"},{period:"1948-Present",title:"Modern Sri Lanka",description:"Independence and cultural renaissance"}].map((i,a)=>e.jsxs("div",{className:`flex items-center ${a%2===0?"flex-row":"flex-row-reverse"}`,children:[e.jsx("div",{className:`w-1/2 ${a%2===0?"pr-8 text-right":"pl-8"}`,children:e.jsxs("div",{className:"card p-6",children:[e.jsx("div",{className:"text-orange-600 font-bold text-lg mb-2",children:i.period}),e.jsx("h3",{className:"text-xl font-semibold mb-2 text-gray-900",children:i.title}),e.jsx("p",{className:"text-gray-600",children:i.description})]})}),e.jsx("div",{className:"relative z-10",children:e.jsx("div",{className:"w-4 h-4 bg-orange-500 rounded-full border-4 border-white shadow-lg"})}),e.jsx("div",{className:"w-1/2"})]},a))})]})})]})}),e.jsx("section",{className:"section-padding bg-gradient-to-r from-orange-600 to-red-600 text-white",children:e.jsx("div",{className:"container-max text-center",children:e.jsxs(t,{animation:"fadeIn",children:[e.jsx("h2",{className:"text-3xl md:text-4xl font-bold mb-6",children:"Experience Sri Lankan Culture"}),e.jsx("p",{className:"text-xl mb-8 opacity-90",children:"Immerse yourself in the rich traditions and vibrant culture of Sri Lanka."}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[e.jsx(s.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"bg-white text-orange-600 hover:bg-gray-100 font-medium py-3 px-8 rounded-lg transition-colors",children:"Cultural Tours"}),e.jsx(s.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"border-2 border-white text-white hover:bg-white hover:text-orange-600 font-medium py-3 px-8 rounded-lg transition-colors",children:"Festival Calendar"})]})]})})})]})};export{u as default};
