import{j as e,m as s}from"./animations-Dls6NBzi.js";import{H as c}from"./index-Bxt4fWYT.js";import{A as i}from"./AnimatedSection-Cqts2t8n.js";import"./vendor-BtP0CW_r.js";import"./router-Bcnr4W6T.js";const d=[{id:"prehistoric",name:"Prehistoric Era",period:"125,000 - 500 BCE",description:"The earliest human settlements in Sri Lanka, with evidence of Balangoda Man and early hunter-gatherer communities.",keyEvents:["Arrival of Balangoda Man (125,000 years ago)","Development of early tools and cave paintings","Transition to agriculture (3000 BCE)","Early irrigation systems"],culturalImpact:["Foundation of agricultural practices","Early artistic expressions in caves","Development of irrigation knowledge","Establishment of settled communities"],monuments:["Fa Hien Cave","Batadombalena Cave","Pahiyangala Cave"]},{id:"anuradhapura-kingdom",name:"Anuradhapura Kingdom",period:"377 BCE - 1017 CE",description:"The first major Sinhalese kingdom and the golden age of ancient Sri Lankan civilization, marked by the introduction of Buddhism and magnificent architectural achievements.",keyEvents:["Founding by King <PERSON><PERSON><PERSON> (377 BCE)","Introduction of Buddhism by Mahinda (247 BCE)","Planting of Sri Maha Bodhi tree (236 BCE)","Construction of great stupas","Development of hydraulic civilization"],culturalImpact:["Establishment of Theravada Buddhism","Development of Sinhalese script and literature","Advanced hydraulic engineering","Monastic education system","Artistic and architectural innovations"],monuments:["Sri Maha Bodhi Tree","Ruwanwelisaya Stupa","Jetavanaramaya","Abhayagiri Monastery","Thuparamaya"]},{id:"polonnaruwa-kingdom",name:"Polonnaruwa Kingdom",period:"1055 - 1232 CE",description:"The second ancient capital representing the medieval golden age of Sinhalese civilization with remarkable architectural and artistic achievements.",keyEvents:["Establishment as capital by Vijayabahu I (1055)","Reign of Parakramabahu I (1153-1186)","Construction of Parakrama Samudra","Artistic renaissance period","Decline due to invasions"],culturalImpact:["Peak of Sinhalese sculpture and architecture","Advanced irrigation and urban planning","Cultural synthesis with South Indian influences","Development of classical Sinhalese literature","Refinement of Buddhist art"],monuments:["Gal Vihara rock sculptures","Parakrama Samudra reservoir","Royal Palace complex","Lankatilaka Temple","Rankoth Vehera"]}],m=[{id:"vesak",name:"Vesak Festival",description:"The most important Buddhist festival celebrating the birth, enlightenment, and death of Buddha. The entire country is illuminated with colorful lanterns and decorations.",when:"May (full moon day)",where:"Nationwide",significance:"Commemorates the three most important events in Buddha's life",activities:["Lantern displays and illuminations","Free food distribution (dansalas)","Religious observances and meditation","Pandol (decorative archways)","Temple visits and offerings"],images:["/images/culture/vesak-1.jpg","/images/culture/vesak-2.jpg"]},{id:"esala-perahera",name:"Kandy Esala Perahera",description:"One of Asia's grandest festivals featuring a spectacular procession of elephants, dancers, drummers, and fire performers honoring the Sacred Tooth Relic.",when:"July/August (10 nights)",where:"Kandy",significance:"Honors the Sacred Tooth Relic of Buddha",activities:["Elephant processions","Traditional Kandyan dancing","Fire dancing and acrobatics","Drumming performances","Religious ceremonies"],images:["/images/culture/perahera-1.jpg","/images/culture/perahera-2.jpg"]},{id:"sinhala-tamil-new-year",name:"Sinhala and Tamil New Year",description:"The traditional New Year celebrated by both Sinhalese and Tamil communities, marking the end of harvest season and beginning of new agricultural cycle.",when:"April 13-14",where:"Nationwide",significance:"Celebrates new beginnings and family unity",activities:["Traditional games and sports","Preparation of traditional sweets","Oil lamp lighting ceremonies","Family gatherings and visits","Astrological observations"],images:["/images/culture/new-year-1.jpg","/images/culture/new-year-2.jpg"]}],h=[{id:"kandyan-dance",name:"Kandyan Dance",description:"The classical dance form of Sri Lanka characterized by rapid movements, acrobatic feats, and elaborate costumes, traditionally performed to honor the gods.",origin:"Ancient Kandyan Kingdom rituals and ceremonies",modernPractice:"Performed at cultural shows, festivals, and special occasions",culturalSignificance:"Preserves ancient religious and cultural traditions",examples:["Ves dance (most sacred form)","Naiyandi dance","Uddekki dance","Pantheru dance"]},{id:"batik-art",name:"Batik Art",description:"Traditional fabric art using wax-resist dyeing technique to create intricate patterns and designs on cloth, often depicting Sri Lankan motifs.",origin:"Influenced by Indonesian and Indian traditions, adapted locally",modernPractice:"Contemporary artists create modern designs while preserving traditional techniques",culturalSignificance:"Represents Sri Lankan artistic heritage and craftsmanship",examples:["Traditional elephant motifs","Buddhist symbols","Floral and nature patterns","Contemporary abstract designs"]},{id:"wood-carving",name:"Traditional Wood Carving",description:"Intricate wood carving art form used in temple decoration, furniture, and architectural elements, showcasing exceptional craftsmanship.",origin:"Ancient temple and palace decoration traditions",modernPractice:"Continues in temple restoration and contemporary furniture making",culturalSignificance:"Preserves ancient artistic techniques and religious symbolism",examples:["Temple door carvings","Decorative pillars","Traditional furniture","Masks and sculptures"]}],g=[{id:"rice-and-curry",name:"Rice and Curry",description:"The staple meal of Sri Lanka consisting of rice served with multiple curry dishes, vegetables, and accompaniments.",type:"main-dish",ingredients:["Rice","Various curries","Coconut","Spices","Vegetables"],culturalSignificance:"Central to Sri Lankan food culture and daily life",regions:["Nationwide"]},{id:"hoppers",name:"Hoppers (Appa)",description:"Bowl-shaped pancakes made from fermented rice flour and coconut milk, often served with egg or honey.",type:"main-dish",ingredients:["Rice flour","Coconut milk","Palm toddy","Eggs"],culturalSignificance:"Traditional breakfast dish with Portuguese colonial influences",regions:["Nationwide","especially coastal areas"]},{id:"kottu-roti",name:"Kottu Roti",description:"Chopped roti bread stir-fried with vegetables, meat, and spices, creating a rhythmic chopping sound during preparation.",type:"main-dish",ingredients:["Roti bread","Vegetables","Meat/eggs","Spices"],culturalSignificance:"Popular street food representing modern Sri Lankan cuisine",regions:["Nationwide","urban areas"]}],y=()=>{const l=[{title:"Buddhism & Spirituality",description:"Sri Lanka is home to one of the oldest Buddhist traditions in the world, with over 2,500 years of continuous practice.",icon:"🏛️",highlights:["Sacred Tooth Relic","Ancient Temples","Meditation Practices","Buddhist Philosophy"]},{title:"Traditional Arts",description:"Rich artistic traditions including classical dance, drumming, mask making, and intricate handicrafts.",icon:"🎭",highlights:["Kandyan Dance","Traditional Drums","Wooden Masks","Batik Art"]},{title:"Festivals & Celebrations",description:"Vibrant festivals throughout the year celebrating religious and cultural traditions with colorful processions.",icon:"🎉",highlights:["Esala Perahera","Vesak Festival","Sinhala New Year","Diwali Celebrations"]},{title:"Cuisine & Spices",description:"A unique culinary tradition influenced by Indian, Dutch, Portuguese, and British cultures with aromatic spices.",icon:"🍛",highlights:["Rice & Curry","Hoppers","Ceylon Tea","Spice Gardens"]},{title:"Languages & Literature",description:"Rich literary traditions in Sinhala and Tamil, with ancient texts and modern literature.",icon:"📚",highlights:["Ancient Chronicles","Poetry Traditions","Folk Tales","Modern Literature"]},{title:"Architecture",description:"Stunning architectural heritage from ancient kingdoms to colonial influences and modern designs.",icon:"🏗️",highlights:["Ancient Stupas","Colonial Buildings","Traditional Houses","Modern Architecture"]}],o=[{name:"Ayurveda",description:"Ancient healing system using natural herbs and treatments",image:"🌿"},{name:"Gem Mining",description:"Traditional methods of mining precious stones",image:"💎"},{name:"Tea Culture",description:"World-renowned Ceylon tea cultivation and ceremonies",image:"🍃"},{name:"Fishing Traditions",description:"Unique stilt fishing and traditional boat making",image:"🎣"}];return e.jsxs(e.Fragment,{children:[e.jsxs(c,{children:[e.jsx("title",{children:"Culture - Sri Lanka Tourist Guide"}),e.jsx("meta",{name:"description",content:"Learn about Sri Lanka's rich cultural heritage and traditions spanning over 2,500 years."})]}),e.jsx("section",{className:"bg-gradient-to-br from-orange-50 to-red-50 section-padding",children:e.jsx("div",{className:"container-max",children:e.jsxs(i,{animation:"fadeIn",className:"text-center mb-12",children:[e.jsxs("h1",{className:"text-4xl md:text-6xl font-bold text-gray-900 mb-6",children:["Sri Lankan ",e.jsx("span",{className:"text-gradient",children:"Culture"})]}),e.jsx("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:"Discover the rich tapestry of Sri Lankan culture, where ancient traditions blend seamlessly with modern life, creating a unique and vibrant heritage."})]})})}),e.jsx("section",{className:"section-padding bg-white",children:e.jsxs("div",{className:"container-max",children:[e.jsxs(i,{animation:"slideUp",className:"text-center mb-16",children:[e.jsx("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-6",children:"Cultural Heritage"}),e.jsx("p",{className:"text-lg text-gray-600 max-w-2xl mx-auto",children:"Explore the diverse aspects of Sri Lankan culture that have been preserved and celebrated for generations."})]}),e.jsx(i,{animation:"stagger",staggerDelay:.2,children:e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:l.map((a,t)=>e.jsxs(s.div,{whileHover:{y:-5},className:"card p-6 text-center group",children:[e.jsx("div",{className:"text-5xl mb-4 group-hover:scale-110 transition-transform duration-300",children:a.icon}),e.jsx("h3",{className:"text-xl font-semibold mb-3 text-gray-900",children:a.title}),e.jsx("p",{className:"text-gray-600 mb-4",children:a.description}),e.jsx("div",{className:"flex flex-wrap gap-2 justify-center",children:a.highlights.map((n,r)=>e.jsx("span",{className:"inline-block px-3 py-1 bg-orange-100 text-orange-700 text-sm rounded-full",children:n},r))})]},t))})})]})}),e.jsx("section",{className:"section-padding bg-gray-50",children:e.jsxs("div",{className:"container-max",children:[e.jsxs(i,{animation:"slideUp",className:"text-center mb-16",children:[e.jsx("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-6",children:"Living Traditions"}),e.jsx("p",{className:"text-lg text-gray-600 max-w-2xl mx-auto",children:"Experience traditional practices that continue to thrive in modern Sri Lanka."})]}),e.jsx(i,{animation:"stagger",staggerDelay:.15,children:e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:o.map((a,t)=>e.jsxs(s.div,{whileHover:{scale:1.05},className:"bg-white rounded-lg p-6 text-center shadow-md hover:shadow-lg transition-all duration-300",children:[e.jsx("div",{className:"text-4xl mb-4",children:a.image}),e.jsx("h3",{className:"text-lg font-semibold mb-2 text-gray-900",children:a.name}),e.jsx("p",{className:"text-gray-600 text-sm",children:a.description})]},t))})})]})}),e.jsx("section",{className:"section-padding bg-white",children:e.jsxs("div",{className:"container-max",children:[e.jsxs(i,{animation:"slideUp",className:"text-center mb-16",children:[e.jsx("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-6",children:"Cultural Timeline"}),e.jsx("p",{className:"text-lg text-gray-600 max-w-2xl mx-auto",children:"Journey through the major periods that shaped Sri Lankan culture."})]}),e.jsx(i,{animation:"stagger",staggerDelay:.3,children:e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-gradient-to-b from-orange-400 to-red-400"}),d.map((a,t)=>e.jsxs("div",{className:`flex items-center mb-12 ${t%2===0?"flex-row":"flex-row-reverse"}`,children:[e.jsx("div",{className:"w-1/2 px-8",children:e.jsxs(s.div,{className:"bg-white p-6 rounded-lg shadow-lg border border-gray-100 hover:shadow-xl transition-shadow duration-300",whileHover:{scale:1.02},children:[e.jsx("div",{className:"text-primary-600 font-semibold text-sm mb-2",children:a.period}),e.jsx("h3",{className:"text-xl font-bold text-gray-900 mb-3",children:a.name}),e.jsx("p",{className:"text-gray-600 mb-4",children:a.description}),e.jsxs("div",{className:"mb-4",children:[e.jsx("h4",{className:"font-semibold text-gray-900 mb-2",children:"Key Events:"}),e.jsx("ul",{className:"text-sm text-gray-600 space-y-1",children:a.keyEvents.slice(0,3).map((n,r)=>e.jsxs("li",{className:"flex items-start",children:[e.jsx("span",{className:"text-primary-600 mr-2",children:"•"}),n]},r))})]}),a.monuments.length>0&&e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold text-gray-900 mb-2",children:"Notable Monuments:"}),e.jsx("div",{className:"flex flex-wrap gap-2",children:a.monuments.slice(0,3).map((n,r)=>e.jsx("span",{className:"text-xs bg-primary-100 text-primary-700 px-2 py-1 rounded",children:n},r))})]})]})}),e.jsx("div",{className:"w-4 h-4 bg-primary-600 rounded-full border-4 border-white shadow-lg z-10"}),e.jsx("div",{className:"w-1/2"})]},a.id))]})})]})}),e.jsx("section",{className:"section-padding bg-gray-50",children:e.jsxs("div",{className:"container-max",children:[e.jsxs(i,{animation:"slideUp",className:"text-center mb-16",children:[e.jsx("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-6",children:"Festivals & Celebrations"}),e.jsx("p",{className:"text-lg text-gray-600 max-w-3xl mx-auto",children:"Experience the vibrant festivals that bring Sri Lankan culture to life throughout the year"})]}),e.jsx(i,{animation:"stagger",staggerDelay:.2,children:e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:m.map(a=>e.jsxs(s.div,{className:"bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300",whileHover:{y:-5},children:[e.jsxs("div",{className:"h-48 bg-gradient-to-br from-orange-400 to-red-500 relative",children:[e.jsx("div",{className:"absolute inset-0 bg-black bg-opacity-20"}),e.jsxs("div",{className:"absolute bottom-4 left-4 text-white",children:[e.jsx("h3",{className:"text-xl font-bold",children:a.name}),e.jsx("p",{className:"text-sm opacity-90",children:a.when})]})]}),e.jsxs("div",{className:"p-6",children:[e.jsx("p",{className:"text-gray-600 mb-4",children:a.description}),e.jsxs("div",{className:"mb-4",children:[e.jsx("h4",{className:"font-semibold text-gray-900 mb-2",children:"Activities:"}),e.jsx("ul",{className:"text-sm text-gray-600 space-y-1",children:a.activities.slice(0,3).map((t,n)=>e.jsxs("li",{className:"flex items-start",children:[e.jsx("span",{className:"text-orange-500 mr-2",children:"•"}),t]},n))})]}),e.jsxs("div",{className:"text-sm text-gray-500",children:[e.jsx("strong",{children:"Where:"})," ",a.where]})]})]},a.id))})})]})}),e.jsx("section",{className:"section-padding bg-white",children:e.jsxs("div",{className:"container-max",children:[e.jsxs(i,{animation:"slideUp",className:"text-center mb-16",children:[e.jsx("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-6",children:"Traditional Arts & Cuisine"}),e.jsx("p",{className:"text-lg text-gray-600 max-w-3xl mx-auto",children:"Discover the artistic traditions and culinary heritage that define Sri Lankan culture"})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12",children:[e.jsxs(i,{animation:"slideLeft",children:[e.jsx("h3",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Traditional Arts"}),e.jsx("div",{className:"space-y-6",children:h.map(a=>e.jsxs(s.div,{className:"border-l-4 border-primary-500 pl-6 hover:border-primary-600 transition-colors",whileHover:{x:5},children:[e.jsx("h4",{className:"text-lg font-semibold text-gray-900 mb-2",children:a.name}),e.jsx("p",{className:"text-gray-600 mb-3",children:a.description}),e.jsxs("div",{className:"text-sm text-gray-500",children:[e.jsx("strong",{children:"Cultural Significance:"})," ",a.culturalSignificance]})]},a.id))})]}),e.jsxs(i,{animation:"slideRight",children:[e.jsx("h3",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Traditional Cuisine"}),e.jsx("div",{className:"space-y-6",children:g.map(a=>e.jsxs(s.div,{className:"bg-gray-50 rounded-lg p-6 hover:bg-gray-100 transition-colors",whileHover:{scale:1.02},children:[e.jsx("h4",{className:"text-lg font-semibold text-gray-900 mb-2",children:a.name}),e.jsx("p",{className:"text-gray-600 mb-3",children:a.description}),e.jsx("div",{className:"flex flex-wrap gap-2 mb-3",children:a.ingredients.slice(0,4).map((t,n)=>e.jsx("span",{className:"text-xs bg-orange-100 text-orange-700 px-2 py-1 rounded",children:t},n))}),e.jsxs("div",{className:"text-sm text-gray-500",children:[e.jsx("strong",{children:"Cultural Significance:"})," ",a.culturalSignificance]})]},a.id))})]})]})]})}),e.jsx("section",{className:"section-padding bg-gradient-to-r from-orange-600 to-red-600 text-white",children:e.jsx("div",{className:"container-max text-center",children:e.jsxs(i,{animation:"fadeIn",children:[e.jsx("h2",{className:"text-3xl md:text-4xl font-bold mb-6",children:"Experience Sri Lankan Culture"}),e.jsx("p",{className:"text-xl mb-8 opacity-90",children:"Immerse yourself in the rich traditions and vibrant culture of Sri Lanka."}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[e.jsx(s.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"bg-white text-orange-600 hover:bg-gray-100 font-medium py-3 px-8 rounded-lg transition-colors",children:"Cultural Tours"}),e.jsx(s.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"border-2 border-white text-white hover:bg-white hover:text-orange-600 font-medium py-3 px-8 rounded-lg transition-colors",children:"Festival Calendar"})]})]})})})]})};export{y as default};
