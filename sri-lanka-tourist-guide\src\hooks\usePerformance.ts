import { useEffect, useState } from 'react'

interface PerformanceMetrics {
  fcp: number | null // First Contentful Paint
  lcp: number | null // Largest Contentful Paint
  fid: number | null // First Input Delay
  cls: number | null // Cumulative Layout Shift
  ttfb: number | null // Time to First Byte
}

interface NavigationTiming {
  domContentLoaded: number
  loadComplete: number
  totalLoadTime: number
}

export const usePerformance = () => {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    fcp: null,
    lcp: null,
    fid: null,
    cls: null,
    ttfb: null
  })

  const [navigationTiming, setNavigationTiming] = useState<NavigationTiming>({
    domContentLoaded: 0,
    loadComplete: 0,
    totalLoadTime: 0
  })

  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Check if Performance Observer is supported
    if (!('PerformanceObserver' in window)) {
      console.warn('PerformanceObserver not supported')
      setIsLoading(false)
      return
    }

    const updateMetrics = (newMetrics: Partial<PerformanceMetrics>) => {
      setMetrics(prev => ({ ...prev, ...newMetrics }))
    }

    // Observe paint metrics (FCP)
    const paintObserver = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.name === 'first-contentful-paint') {
          updateMetrics({ fcp: entry.startTime })
        }
      }
    })

    // Observe LCP
    const lcpObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      const lastEntry = entries[entries.length - 1]
      updateMetrics({ lcp: lastEntry.startTime })
    })

    // Observe FID
    const fidObserver = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        const fidEntry = entry as any
        updateMetrics({ fid: fidEntry.processingStart - fidEntry.startTime })
      }
    })

    // Observe CLS
    let clsValue = 0
    const clsObserver = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (!(entry as any).hadRecentInput) {
          clsValue += (entry as any).value
          updateMetrics({ cls: clsValue })
        }
      }
    })

    try {
      paintObserver.observe({ entryTypes: ['paint'] })
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] })
      fidObserver.observe({ entryTypes: ['first-input'] })
      clsObserver.observe({ entryTypes: ['layout-shift'] })
    } catch (error) {
      console.warn('Error setting up performance observers:', error)
    }

    // Get navigation timing
    const getNavigationTiming = () => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
      if (navigation) {
        const domContentLoaded = navigation.domContentLoadedEventEnd - navigation.fetchStart
        const loadComplete = navigation.loadEventEnd - navigation.fetchStart
        const totalLoadTime = navigation.loadEventEnd - navigation.fetchStart
        const ttfb = navigation.responseStart - navigation.requestStart

        setNavigationTiming({
          domContentLoaded,
          loadComplete,
          totalLoadTime
        })

        updateMetrics({ ttfb })
      }
    }

    // Wait for page load to get accurate timing
    if (document.readyState === 'complete') {
      getNavigationTiming()
      setIsLoading(false)
    } else {
      window.addEventListener('load', () => {
        setTimeout(() => {
          getNavigationTiming()
          setIsLoading(false)
        }, 0)
      })
    }

    return () => {
      paintObserver.disconnect()
      lcpObserver.disconnect()
      fidObserver.disconnect()
      clsObserver.disconnect()
    }
  }, [])

  // Helper function to get performance grade
  const getPerformanceGrade = (): 'good' | 'needs-improvement' | 'poor' => {
    const { fcp, lcp, fid, cls } = metrics

    let score = 0
    let totalMetrics = 0

    if (fcp !== null) {
      totalMetrics++
      if (fcp <= 1800) score++
      else if (fcp <= 3000) score += 0.5
    }

    if (lcp !== null) {
      totalMetrics++
      if (lcp <= 2500) score++
      else if (lcp <= 4000) score += 0.5
    }

    if (fid !== null) {
      totalMetrics++
      if (fid <= 100) score++
      else if (fid <= 300) score += 0.5
    }

    if (cls !== null) {
      totalMetrics++
      if (cls <= 0.1) score++
      else if (cls <= 0.25) score += 0.5
    }

    if (totalMetrics === 0) return 'needs-improvement'

    const percentage = score / totalMetrics
    if (percentage >= 0.8) return 'good'
    if (percentage >= 0.5) return 'needs-improvement'
    return 'poor'
  }

  // Helper function to format metrics for display
  const formatMetrics = () => {
    return {
      fcp: metrics.fcp ? `${Math.round(metrics.fcp)}ms` : 'N/A',
      lcp: metrics.lcp ? `${Math.round(metrics.lcp)}ms` : 'N/A',
      fid: metrics.fid ? `${Math.round(metrics.fid)}ms` : 'N/A',
      cls: metrics.cls ? metrics.cls.toFixed(3) : 'N/A',
      ttfb: metrics.ttfb ? `${Math.round(metrics.ttfb)}ms` : 'N/A',
      domContentLoaded: `${Math.round(navigationTiming.domContentLoaded)}ms`,
      loadComplete: `${Math.round(navigationTiming.loadComplete)}ms`,
      totalLoadTime: `${Math.round(navigationTiming.totalLoadTime)}ms`
    }
  }

  return {
    metrics,
    navigationTiming,
    isLoading,
    performanceGrade: getPerformanceGrade(),
    formattedMetrics: formatMetrics()
  }
}

// Hook for measuring component render performance
export const useRenderPerformance = (componentName: string) => {
  useEffect(() => {
    const startTime = performance.now()
    
    return () => {
      const endTime = performance.now()
      const renderTime = endTime - startTime
      
      if (import.meta.env.DEV) {
        console.log(`${componentName} render time: ${renderTime.toFixed(2)}ms`)
      }
    }
  })
}

// Hook for measuring function execution time
export const useFunctionPerformance = () => {
  const measureFunction = <T extends (...args: any[]) => any>(
    fn: T,
    functionName: string
  ): T => {
    return ((...args: Parameters<T>) => {
      const startTime = performance.now()
      const result = fn(...args)
      const endTime = performance.now()
      
      if (import.meta.env.DEV) {
        console.log(`${functionName} execution time: ${(endTime - startTime).toFixed(2)}ms`)
      }
      
      return result
    }) as T
  }

  return { measureFunction }
}
