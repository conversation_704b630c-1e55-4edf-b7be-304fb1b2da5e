# Clamp your triggers! - YouTube

[Music]
thank you
hi folks so today I'm going to be
covering a small but powerful feature
that we've added into the latest gsap
release
[Music]
if you scroll Trigger or scroll smoother
before you might have run into this
Behavior so imagine you've got a
scrubbed animation above the fold of
your web page sometimes when the page
loads the animation loads part way
through it doesn't load from the very
beginning it kind of looks like the
animation has played some of the way
through and then paused so why does this
happen let's take a little look
let's have a mini refresher on how
scroll trigger works first so you can
add a scroll trigger to any gstap
animation like tween or timeline it's
just saying hey scroll trigger I would
like this animation to be triggered by
scroll here we've got a little spinning
box so we can tell scroll trigger when
to play The Scroll animation with the
start and end triggers there's a bunch
of different ways that we can write out
our triggers this is shorthand for when
the bottom of the Box hits the bottom of
the viewport but however we write out
our triggers under the hood scroll
trigger converts the value into a
numeric scroll position in pixels we can
also add visual markers to check out
exactly where our markers fall on the
page so we can see as we scroll down
when the boxes move past this point they
all animate so one by one they're
hitting that point and they're animating
now if we start off with some boxes in
view
these boxes would have already moved
past their start position so they'd fire
instantly on page load on board so far
awesome so let's take a look at scrubbed
animations so scrubbed animations don't
just fire as we pass the start Point
their progress is stretched out between
the start and end marker the progress of
the animation is tied in to the scroll
progress so now all the boxes will start
animating as they pass the start marker
and then they finish animating or when
they go off the top of the page here
nice
okay so now if we get rid of the spacer
at the top here so that there's less
space to scroll from and we get rid of
the spacer at the bottom so that there's
less space to scroll into and we can see
that some of the boxes are in view when
the page loads
so you can see that these boxes have
already passed their start position so
these animations are partially completed
already they don't get the chance to
start from the very beginning this is
the same if we scroll down to the boxes
at the end so these boxes at the end
never get the chance to hit their end
marker so their animations never finish
now I'm more visual so these boxes work
quite well in my head but for the more
logical folks let's do some consoing
so let's log out the Min and Mac scroll
values Okay so we've got zero for Min
odds and then we've got
1526 this is the maximum that the page
can scroll so let's take a little look
at the boxes let's log out the start and
end values for each of these boxes oh we
can check if they're in view as well on
page load we've got scroll trigger is in
viewport for that so let's do a little
ternary and then we can do some little
emojis
um if it's in view we'll do some googly
eyes and if it's hidden we can do little
monkey
um so now if we look at the values we
can see that box one and two are in view
already we've got googly eyes
um and their start values if we look at
the start value is negative so this is
less than zero and now right at the
bottom if we look at the last two boxes
their end values are higher than the
maximum scroll distance
now this isn't buggy Behavior this
behavior is totally in line with how
scrubbed animations work but sometimes
it might not be what you want you might
want all of the animations whether
they're in view or not to start at their
initial position and finish animating by
the time you hit the bottom of the page
so solution time if you've used gsap
utils at all you might have run into
clamp clamps are really cool little
function so we can define a minimum and
a maximum range and then we can use it
to clamp any value between those numbers
we need to make sure that the past
trigger values are never less than zero
before the top of the page or more than
the maximum scroll position so we want
to clamp the trigger value between these
two points this is a bit wordy so we've
made some nice shorthand for you to use
under the hood it's just like running
the resulting pixel value through this
clamp function right let's do it in the
code so this is nice and easy we're just
going to wrap the start and end values
in clamp and now all of our scrub scroll
triggers are going to play from start to
end no matter where the triggers are
placed or whether they're in view or not
when the page loads awesome so we can
also use this over in scroll smoother
land let's take a little look there
scroll smoother lets us add effects to
elements so we can add data speed or
data lag we're going to look at data
speed right now now speed lets you move
elements through the viewport at
different speeds it's kind of a nice
easy way to do Parallax type effects
and under the hood these effects are
basically just scrubbed animations
so right here we've got some boxes
they're all in a line so same as with
scroll trigger if we add effects true
here these elements are in view already
and because of that they're already part
way through an animation
when they hit the middle of the screen
you can see that they all line up so
they reach their kind of resting place
and that's the default Behavior so if we
did want them all to start in their
initial States we can add clamp to the
data attributes so just like we did with
the scroll triggered animations
so we're going to pop clamp in Here and
Now on page load they're all at their
start position so now they only start
animate when we scroll down
it's worth mentioning that this only
applies to boxes that are in view so if
we scroll down to this second set of
boxes we can see that they hit their
resting place in the center so that's
just the default Behavior it just means
that anything that is in view stays
where it's meant to be in the design on
page load
so it's a little bit hard to understand
with boxes sometimes I find it might be
easier to actually take a look at a
design a normal web page to kind of see
why this would be useful
so let's take this design for example we
want to animate this row of images here
and we want to also draw a little circle
around the word clamp withdraw SVG so
we've got a scrubbed scroll trigger
animation and then we've got a little
bit of scroll smoother effects that
we're adding in so if we take a little
look at our draw SVG animation
it's okay if it's triggering on load so
we've passed the start marker already
and then when the page loads the
animation is going to play but if we add
a scrub and some markers
we can see that it loads almost all of
the way through either start triggers
being passed by quite a way so it's more
than halfway through
so let's fix this with clamp we're going
to clamp the start value and now when
the page loads it's at the beginning it
waits for the user to scroll before
progressing awesome
so let's do a little data speed on these
images we're going to try and scroll
them through the viewport at different
speeds
so we're going to enable effects and
there's that issue again so the images
all start off in the wrong place and the
layout looks messy like no designer is
going to sign that off it doesn't look
how they intended so let's clamp them
and if we add clamp you can see that on
page load we get a nice orderly starting
position everything starts off where
it's been laid out in the Dom
let's add a little pin to the text just
for fun so that the images go underneath
and there's our effect
so I personally think that this is super
handy especially if you're working in a
code base where you've got components
that are a little bit more flexible you
might not know where they're going to
end up on a web page so if you don't
know where they're going to end up you
might end up having to write some
conditional logic to work out where to
place the triggers depending on whether
they're in view or not so this is quite
nice because you don't have to worry
about any of that you can just pass the
logic over to scroll trigger you just
pop clamp on and it sorts everything out
for you so I hope this was helpful and
you learned something that will help you
in your future projects I will chat to
you soon
[Music]