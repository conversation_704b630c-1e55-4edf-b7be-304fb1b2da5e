{"version": 3, "sources": ["../../react-fast-compare/index.js", "../../invariant/browser.js", "../../shallowequal/index.js", "../../react-helmet-async/lib/index.esm.js"], "sourcesContent": ["/* global Map:readonly, Set:readonly, ArrayBuffer:readonly */\n\nvar hasElementType = typeof Element !== 'undefined';\nvar hasMap = typeof Map === 'function';\nvar hasSet = typeof Set === 'function';\nvar hasArrayBuffer = typeof ArrayBuffer === 'function' && !!ArrayBuffer.isView;\n\n// Note: We **don't** need `envHasBigInt64Array` in fde es6/index.js\n\nfunction equal(a, b) {\n  // START: fast-deep-equal es6/index.js 3.1.3\n  if (a === b) return true;\n\n  if (a && b && typeof a == 'object' && typeof b == 'object') {\n    if (a.constructor !== b.constructor) return false;\n\n    var length, i, keys;\n    if (Array.isArray(a)) {\n      length = a.length;\n      if (length != b.length) return false;\n      for (i = length; i-- !== 0;)\n        if (!equal(a[i], b[i])) return false;\n      return true;\n    }\n\n    // START: Modifications:\n    // 1. Extra `has<Type> &&` helpers in initial condition allow es6 code\n    //    to co-exist with es5.\n    // 2. Replace `for of` with es5 compliant iteration using `for`.\n    //    Basically, take:\n    //\n    //    ```js\n    //    for (i of a.entries())\n    //      if (!b.has(i[0])) return false;\n    //    ```\n    //\n    //    ... and convert to:\n    //\n    //    ```js\n    //    it = a.entries();\n    //    while (!(i = it.next()).done)\n    //      if (!b.has(i.value[0])) return false;\n    //    ```\n    //\n    //    **Note**: `i` access switches to `i.value`.\n    var it;\n    if (hasMap && (a instanceof Map) && (b instanceof Map)) {\n      if (a.size !== b.size) return false;\n      it = a.entries();\n      while (!(i = it.next()).done)\n        if (!b.has(i.value[0])) return false;\n      it = a.entries();\n      while (!(i = it.next()).done)\n        if (!equal(i.value[1], b.get(i.value[0]))) return false;\n      return true;\n    }\n\n    if (hasSet && (a instanceof Set) && (b instanceof Set)) {\n      if (a.size !== b.size) return false;\n      it = a.entries();\n      while (!(i = it.next()).done)\n        if (!b.has(i.value[0])) return false;\n      return true;\n    }\n    // END: Modifications\n\n    if (hasArrayBuffer && ArrayBuffer.isView(a) && ArrayBuffer.isView(b)) {\n      length = a.length;\n      if (length != b.length) return false;\n      for (i = length; i-- !== 0;)\n        if (a[i] !== b[i]) return false;\n      return true;\n    }\n\n    if (a.constructor === RegExp) return a.source === b.source && a.flags === b.flags;\n    // START: Modifications:\n    // Apply guards for `Object.create(null)` handling. See:\n    // - https://github.com/FormidableLabs/react-fast-compare/issues/64\n    // - https://github.com/epoberezkin/fast-deep-equal/issues/49\n    if (a.valueOf !== Object.prototype.valueOf && typeof a.valueOf === 'function' && typeof b.valueOf === 'function') return a.valueOf() === b.valueOf();\n    if (a.toString !== Object.prototype.toString && typeof a.toString === 'function' && typeof b.toString === 'function') return a.toString() === b.toString();\n    // END: Modifications\n\n    keys = Object.keys(a);\n    length = keys.length;\n    if (length !== Object.keys(b).length) return false;\n\n    for (i = length; i-- !== 0;)\n      if (!Object.prototype.hasOwnProperty.call(b, keys[i])) return false;\n    // END: fast-deep-equal\n\n    // START: react-fast-compare\n    // custom handling for DOM elements\n    if (hasElementType && a instanceof Element) return false;\n\n    // custom handling for React/Preact\n    for (i = length; i-- !== 0;) {\n      if ((keys[i] === '_owner' || keys[i] === '__v' || keys[i] === '__o') && a.$$typeof) {\n        // React-specific: avoid traversing React elements' _owner\n        // Preact-specific: avoid traversing Preact elements' __v and __o\n        //    __v = $_original / $_vnode\n        //    __o = $_owner\n        // These properties contain circular references and are not needed when\n        // comparing the actual elements (and not their owners)\n        // .$$typeof and ._store on just reasonable markers of elements\n\n        continue;\n      }\n\n      // all other properties should be traversed as usual\n      if (!equal(a[keys[i]], b[keys[i]])) return false;\n    }\n    // END: react-fast-compare\n\n    // START: fast-deep-equal\n    return true;\n  }\n\n  return a !== a && b !== b;\n}\n// end fast-deep-equal\n\nmodule.exports = function isEqual(a, b) {\n  try {\n    return equal(a, b);\n  } catch (error) {\n    if (((error.message || '').match(/stack|recursion/i))) {\n      // warn on circular references, don't crash\n      // browsers give this different errors name and messages:\n      // chrome/safari: \"RangeError\", \"Maximum call stack size exceeded\"\n      // firefox: \"InternalError\", too much recursion\"\n      // edge: \"Error\", \"Out of stack space\"\n      console.warn('react-fast-compare cannot handle circular refs');\n      return false;\n    }\n    // some other error. we should definitely know about these\n    throw error;\n  }\n};\n", "/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\n/**\n * Use invariant() to assert state which your program assumes to be true.\n *\n * Provide sprintf-style format (only %s is supported) and arguments\n * to provide information about what broke and what you were\n * expecting.\n *\n * The invariant message will be stripped in production, but the invariant\n * will remain to ensure logic does not differ in production.\n */\n\nvar invariant = function(condition, format, a, b, c, d, e, f) {\n  if (process.env.NODE_ENV !== 'production') {\n    if (format === undefined) {\n      throw new Error('invariant requires an error message argument');\n    }\n  }\n\n  if (!condition) {\n    var error;\n    if (format === undefined) {\n      error = new Error(\n        'Minified exception occurred; use the non-minified dev environment ' +\n        'for the full error message and additional helpful warnings.'\n      );\n    } else {\n      var args = [a, b, c, d, e, f];\n      var argIndex = 0;\n      error = new Error(\n        format.replace(/%s/g, function() { return args[argIndex++]; })\n      );\n      error.name = 'Invariant Violation';\n    }\n\n    error.framesToPop = 1; // we don't care about invariant's own frame\n    throw error;\n  }\n};\n\nmodule.exports = invariant;\n", "//\n\nmodule.exports = function shallowEqual(objA, objB, compare, compareContext) {\n  var ret = compare ? compare.call(compareContext, objA, objB) : void 0;\n\n  if (ret !== void 0) {\n    return !!ret;\n  }\n\n  if (objA === objB) {\n    return true;\n  }\n\n  if (typeof objA !== \"object\" || !objA || typeof objB !== \"object\" || !objB) {\n    return false;\n  }\n\n  var keysA = Object.keys(objA);\n  var keysB = Object.keys(objB);\n\n  if (keysA.length !== keysB.length) {\n    return false;\n  }\n\n  var bHasOwnProperty = Object.prototype.hasOwnProperty.bind(objB);\n\n  // Test for A's keys different from B.\n  for (var idx = 0; idx < keysA.length; idx++) {\n    var key = keysA[idx];\n\n    if (!bHasOwnProperty(key)) {\n      return false;\n    }\n\n    var valueA = objA[key];\n    var valueB = objB[key];\n\n    ret = compare ? compare.call(compareContext, valueA, valueB, key) : void 0;\n\n    if (ret === false || (ret === void 0 && valueA !== valueB)) {\n      return false;\n    }\n  }\n\n  return true;\n};\n", "// src/index.tsx\nimport React3, { Component as Component3 } from \"react\";\nimport fastCompare from \"react-fast-compare\";\nimport invariant from \"invariant\";\n\n// src/Provider.tsx\nimport React2, { Component } from \"react\";\n\n// src/server.ts\nimport React from \"react\";\n\n// src/constants.ts\nvar TAG_NAMES = /* @__PURE__ */ ((TAG_NAMES2) => {\n  TAG_NAMES2[\"BASE\"] = \"base\";\n  TAG_NAMES2[\"BODY\"] = \"body\";\n  TAG_NAMES2[\"HEAD\"] = \"head\";\n  TAG_NAMES2[\"HTML\"] = \"html\";\n  TAG_NAMES2[\"LINK\"] = \"link\";\n  TAG_NAMES2[\"META\"] = \"meta\";\n  TAG_NAMES2[\"NOSCRIPT\"] = \"noscript\";\n  TAG_NAMES2[\"SCRIPT\"] = \"script\";\n  TAG_NAMES2[\"STYLE\"] = \"style\";\n  TAG_NAMES2[\"TITLE\"] = \"title\";\n  TAG_NAMES2[\"FRAGMENT\"] = \"Symbol(react.fragment)\";\n  return TAG_NAMES2;\n})(TAG_NAMES || {});\nvar SEO_PRIORITY_TAGS = {\n  link: { rel: [\"amphtml\", \"canonical\", \"alternate\"] },\n  script: { type: [\"application/ld+json\"] },\n  meta: {\n    charset: \"\",\n    name: [\"generator\", \"robots\", \"description\"],\n    property: [\n      \"og:type\",\n      \"og:title\",\n      \"og:url\",\n      \"og:image\",\n      \"og:image:alt\",\n      \"og:description\",\n      \"twitter:url\",\n      \"twitter:title\",\n      \"twitter:description\",\n      \"twitter:image\",\n      \"twitter:image:alt\",\n      \"twitter:card\",\n      \"twitter:site\"\n    ]\n  }\n};\nvar VALID_TAG_NAMES = Object.values(TAG_NAMES);\nvar REACT_TAG_MAP = {\n  accesskey: \"accessKey\",\n  charset: \"charSet\",\n  class: \"className\",\n  contenteditable: \"contentEditable\",\n  contextmenu: \"contextMenu\",\n  \"http-equiv\": \"httpEquiv\",\n  itemprop: \"itemProp\",\n  tabindex: \"tabIndex\"\n};\nvar HTML_TAG_MAP = Object.entries(REACT_TAG_MAP).reduce(\n  (carry, [key, value]) => {\n    carry[value] = key;\n    return carry;\n  },\n  {}\n);\nvar HELMET_ATTRIBUTE = \"data-rh\";\n\n// src/utils.ts\nvar HELMET_PROPS = {\n  DEFAULT_TITLE: \"defaultTitle\",\n  DEFER: \"defer\",\n  ENCODE_SPECIAL_CHARACTERS: \"encodeSpecialCharacters\",\n  ON_CHANGE_CLIENT_STATE: \"onChangeClientState\",\n  TITLE_TEMPLATE: \"titleTemplate\",\n  PRIORITIZE_SEO_TAGS: \"prioritizeSeoTags\"\n};\nvar getInnermostProperty = (propsList, property) => {\n  for (let i = propsList.length - 1; i >= 0; i -= 1) {\n    const props = propsList[i];\n    if (Object.prototype.hasOwnProperty.call(props, property)) {\n      return props[property];\n    }\n  }\n  return null;\n};\nvar getTitleFromPropsList = (propsList) => {\n  let innermostTitle = getInnermostProperty(propsList, \"title\" /* TITLE */);\n  const innermostTemplate = getInnermostProperty(propsList, HELMET_PROPS.TITLE_TEMPLATE);\n  if (Array.isArray(innermostTitle)) {\n    innermostTitle = innermostTitle.join(\"\");\n  }\n  if (innermostTemplate && innermostTitle) {\n    return innermostTemplate.replace(/%s/g, () => innermostTitle);\n  }\n  const innermostDefaultTitle = getInnermostProperty(propsList, HELMET_PROPS.DEFAULT_TITLE);\n  return innermostTitle || innermostDefaultTitle || void 0;\n};\nvar getOnChangeClientState = (propsList) => getInnermostProperty(propsList, HELMET_PROPS.ON_CHANGE_CLIENT_STATE) || (() => {\n});\nvar getAttributesFromPropsList = (tagType, propsList) => propsList.filter((props) => typeof props[tagType] !== \"undefined\").map((props) => props[tagType]).reduce((tagAttrs, current) => ({ ...tagAttrs, ...current }), {});\nvar getBaseTagFromPropsList = (primaryAttributes, propsList) => propsList.filter((props) => typeof props[\"base\" /* BASE */] !== \"undefined\").map((props) => props[\"base\" /* BASE */]).reverse().reduce((innermostBaseTag, tag) => {\n  if (!innermostBaseTag.length) {\n    const keys = Object.keys(tag);\n    for (let i = 0; i < keys.length; i += 1) {\n      const attributeKey = keys[i];\n      const lowerCaseAttributeKey = attributeKey.toLowerCase();\n      if (primaryAttributes.indexOf(lowerCaseAttributeKey) !== -1 && tag[lowerCaseAttributeKey]) {\n        return innermostBaseTag.concat(tag);\n      }\n    }\n  }\n  return innermostBaseTag;\n}, []);\nvar warn = (msg) => console && typeof console.warn === \"function\" && console.warn(msg);\nvar getTagsFromPropsList = (tagName, primaryAttributes, propsList) => {\n  const approvedSeenTags = {};\n  return propsList.filter((props) => {\n    if (Array.isArray(props[tagName])) {\n      return true;\n    }\n    if (typeof props[tagName] !== \"undefined\") {\n      warn(\n        `Helmet: ${tagName} should be of type \"Array\". Instead found type \"${typeof props[tagName]}\"`\n      );\n    }\n    return false;\n  }).map((props) => props[tagName]).reverse().reduce((approvedTags, instanceTags) => {\n    const instanceSeenTags = {};\n    instanceTags.filter((tag) => {\n      let primaryAttributeKey;\n      const keys2 = Object.keys(tag);\n      for (let i = 0; i < keys2.length; i += 1) {\n        const attributeKey = keys2[i];\n        const lowerCaseAttributeKey = attributeKey.toLowerCase();\n        if (primaryAttributes.indexOf(lowerCaseAttributeKey) !== -1 && !(primaryAttributeKey === \"rel\" /* REL */ && tag[primaryAttributeKey].toLowerCase() === \"canonical\") && !(lowerCaseAttributeKey === \"rel\" /* REL */ && tag[lowerCaseAttributeKey].toLowerCase() === \"stylesheet\")) {\n          primaryAttributeKey = lowerCaseAttributeKey;\n        }\n        if (primaryAttributes.indexOf(attributeKey) !== -1 && (attributeKey === \"innerHTML\" /* INNER_HTML */ || attributeKey === \"cssText\" /* CSS_TEXT */ || attributeKey === \"itemprop\" /* ITEM_PROP */)) {\n          primaryAttributeKey = attributeKey;\n        }\n      }\n      if (!primaryAttributeKey || !tag[primaryAttributeKey]) {\n        return false;\n      }\n      const value = tag[primaryAttributeKey].toLowerCase();\n      if (!approvedSeenTags[primaryAttributeKey]) {\n        approvedSeenTags[primaryAttributeKey] = {};\n      }\n      if (!instanceSeenTags[primaryAttributeKey]) {\n        instanceSeenTags[primaryAttributeKey] = {};\n      }\n      if (!approvedSeenTags[primaryAttributeKey][value]) {\n        instanceSeenTags[primaryAttributeKey][value] = true;\n        return true;\n      }\n      return false;\n    }).reverse().forEach((tag) => approvedTags.push(tag));\n    const keys = Object.keys(instanceSeenTags);\n    for (let i = 0; i < keys.length; i += 1) {\n      const attributeKey = keys[i];\n      const tagUnion = {\n        ...approvedSeenTags[attributeKey],\n        ...instanceSeenTags[attributeKey]\n      };\n      approvedSeenTags[attributeKey] = tagUnion;\n    }\n    return approvedTags;\n  }, []).reverse();\n};\nvar getAnyTrueFromPropsList = (propsList, checkedTag) => {\n  if (Array.isArray(propsList) && propsList.length) {\n    for (let index = 0; index < propsList.length; index += 1) {\n      const prop = propsList[index];\n      if (prop[checkedTag]) {\n        return true;\n      }\n    }\n  }\n  return false;\n};\nvar reducePropsToState = (propsList) => ({\n  baseTag: getBaseTagFromPropsList([\"href\" /* HREF */], propsList),\n  bodyAttributes: getAttributesFromPropsList(\"bodyAttributes\" /* BODY */, propsList),\n  defer: getInnermostProperty(propsList, HELMET_PROPS.DEFER),\n  encode: getInnermostProperty(propsList, HELMET_PROPS.ENCODE_SPECIAL_CHARACTERS),\n  htmlAttributes: getAttributesFromPropsList(\"htmlAttributes\" /* HTML */, propsList),\n  linkTags: getTagsFromPropsList(\n    \"link\" /* LINK */,\n    [\"rel\" /* REL */, \"href\" /* HREF */],\n    propsList\n  ),\n  metaTags: getTagsFromPropsList(\n    \"meta\" /* META */,\n    [\n      \"name\" /* NAME */,\n      \"charset\" /* CHARSET */,\n      \"http-equiv\" /* HTTPEQUIV */,\n      \"property\" /* PROPERTY */,\n      \"itemprop\" /* ITEM_PROP */\n    ],\n    propsList\n  ),\n  noscriptTags: getTagsFromPropsList(\"noscript\" /* NOSCRIPT */, [\"innerHTML\" /* INNER_HTML */], propsList),\n  onChangeClientState: getOnChangeClientState(propsList),\n  scriptTags: getTagsFromPropsList(\n    \"script\" /* SCRIPT */,\n    [\"src\" /* SRC */, \"innerHTML\" /* INNER_HTML */],\n    propsList\n  ),\n  styleTags: getTagsFromPropsList(\"style\" /* STYLE */, [\"cssText\" /* CSS_TEXT */], propsList),\n  title: getTitleFromPropsList(propsList),\n  titleAttributes: getAttributesFromPropsList(\"titleAttributes\" /* TITLE */, propsList),\n  prioritizeSeoTags: getAnyTrueFromPropsList(propsList, HELMET_PROPS.PRIORITIZE_SEO_TAGS)\n});\nvar flattenArray = (possibleArray) => Array.isArray(possibleArray) ? possibleArray.join(\"\") : possibleArray;\nvar checkIfPropsMatch = (props, toMatch) => {\n  const keys = Object.keys(props);\n  for (let i = 0; i < keys.length; i += 1) {\n    if (toMatch[keys[i]] && toMatch[keys[i]].includes(props[keys[i]])) {\n      return true;\n    }\n  }\n  return false;\n};\nvar prioritizer = (elementsList, propsToMatch) => {\n  if (Array.isArray(elementsList)) {\n    return elementsList.reduce(\n      (acc, elementAttrs) => {\n        if (checkIfPropsMatch(elementAttrs, propsToMatch)) {\n          acc.priority.push(elementAttrs);\n        } else {\n          acc.default.push(elementAttrs);\n        }\n        return acc;\n      },\n      { priority: [], default: [] }\n    );\n  }\n  return { default: elementsList, priority: [] };\n};\nvar without = (obj, key) => {\n  return {\n    ...obj,\n    [key]: void 0\n  };\n};\n\n// src/server.ts\nvar SELF_CLOSING_TAGS = [\"noscript\" /* NOSCRIPT */, \"script\" /* SCRIPT */, \"style\" /* STYLE */];\nvar encodeSpecialCharacters = (str, encode = true) => {\n  if (encode === false) {\n    return String(str);\n  }\n  return String(str).replace(/&/g, \"&amp;\").replace(/</g, \"&lt;\").replace(/>/g, \"&gt;\").replace(/\"/g, \"&quot;\").replace(/'/g, \"&#x27;\");\n};\nvar generateElementAttributesAsString = (attributes) => Object.keys(attributes).reduce((str, key) => {\n  const attr = typeof attributes[key] !== \"undefined\" ? `${key}=\"${attributes[key]}\"` : `${key}`;\n  return str ? `${str} ${attr}` : attr;\n}, \"\");\nvar generateTitleAsString = (type, title, attributes, encode) => {\n  const attributeString = generateElementAttributesAsString(attributes);\n  const flattenedTitle = flattenArray(title);\n  return attributeString ? `<${type} ${HELMET_ATTRIBUTE}=\"true\" ${attributeString}>${encodeSpecialCharacters(\n    flattenedTitle,\n    encode\n  )}</${type}>` : `<${type} ${HELMET_ATTRIBUTE}=\"true\">${encodeSpecialCharacters(\n    flattenedTitle,\n    encode\n  )}</${type}>`;\n};\nvar generateTagsAsString = (type, tags, encode = true) => tags.reduce((str, t) => {\n  const tag = t;\n  const attributeHtml = Object.keys(tag).filter(\n    (attribute) => !(attribute === \"innerHTML\" /* INNER_HTML */ || attribute === \"cssText\" /* CSS_TEXT */)\n  ).reduce((string, attribute) => {\n    const attr = typeof tag[attribute] === \"undefined\" ? attribute : `${attribute}=\"${encodeSpecialCharacters(tag[attribute], encode)}\"`;\n    return string ? `${string} ${attr}` : attr;\n  }, \"\");\n  const tagContent = tag.innerHTML || tag.cssText || \"\";\n  const isSelfClosing = SELF_CLOSING_TAGS.indexOf(type) === -1;\n  return `${str}<${type} ${HELMET_ATTRIBUTE}=\"true\" ${attributeHtml}${isSelfClosing ? `/>` : `>${tagContent}</${type}>`}`;\n}, \"\");\nvar convertElementAttributesToReactProps = (attributes, initProps = {}) => Object.keys(attributes).reduce((obj, key) => {\n  const mapped = REACT_TAG_MAP[key];\n  obj[mapped || key] = attributes[key];\n  return obj;\n}, initProps);\nvar generateTitleAsReactComponent = (_type, title, attributes) => {\n  const initProps = {\n    key: title,\n    [HELMET_ATTRIBUTE]: true\n  };\n  const props = convertElementAttributesToReactProps(attributes, initProps);\n  return [React.createElement(\"title\" /* TITLE */, props, title)];\n};\nvar generateTagsAsReactComponent = (type, tags) => tags.map((tag, i) => {\n  const mappedTag = {\n    key: i,\n    [HELMET_ATTRIBUTE]: true\n  };\n  Object.keys(tag).forEach((attribute) => {\n    const mapped = REACT_TAG_MAP[attribute];\n    const mappedAttribute = mapped || attribute;\n    if (mappedAttribute === \"innerHTML\" /* INNER_HTML */ || mappedAttribute === \"cssText\" /* CSS_TEXT */) {\n      const content = tag.innerHTML || tag.cssText;\n      mappedTag.dangerouslySetInnerHTML = { __html: content };\n    } else {\n      mappedTag[mappedAttribute] = tag[attribute];\n    }\n  });\n  return React.createElement(type, mappedTag);\n});\nvar getMethodsForTag = (type, tags, encode = true) => {\n  switch (type) {\n    case \"title\" /* TITLE */:\n      return {\n        toComponent: () => generateTitleAsReactComponent(type, tags.title, tags.titleAttributes),\n        toString: () => generateTitleAsString(type, tags.title, tags.titleAttributes, encode)\n      };\n    case \"bodyAttributes\" /* BODY */:\n    case \"htmlAttributes\" /* HTML */:\n      return {\n        toComponent: () => convertElementAttributesToReactProps(tags),\n        toString: () => generateElementAttributesAsString(tags)\n      };\n    default:\n      return {\n        toComponent: () => generateTagsAsReactComponent(type, tags),\n        toString: () => generateTagsAsString(type, tags, encode)\n      };\n  }\n};\nvar getPriorityMethods = ({ metaTags, linkTags, scriptTags, encode }) => {\n  const meta = prioritizer(metaTags, SEO_PRIORITY_TAGS.meta);\n  const link = prioritizer(linkTags, SEO_PRIORITY_TAGS.link);\n  const script = prioritizer(scriptTags, SEO_PRIORITY_TAGS.script);\n  const priorityMethods = {\n    toComponent: () => [\n      ...generateTagsAsReactComponent(\"meta\" /* META */, meta.priority),\n      ...generateTagsAsReactComponent(\"link\" /* LINK */, link.priority),\n      ...generateTagsAsReactComponent(\"script\" /* SCRIPT */, script.priority)\n    ],\n    toString: () => (\n      // generate all the tags as strings and concatenate them\n      `${getMethodsForTag(\"meta\" /* META */, meta.priority, encode)} ${getMethodsForTag(\n        \"link\" /* LINK */,\n        link.priority,\n        encode\n      )} ${getMethodsForTag(\"script\" /* SCRIPT */, script.priority, encode)}`\n    )\n  };\n  return {\n    priorityMethods,\n    metaTags: meta.default,\n    linkTags: link.default,\n    scriptTags: script.default\n  };\n};\nvar mapStateOnServer = (props) => {\n  const {\n    baseTag,\n    bodyAttributes,\n    encode = true,\n    htmlAttributes,\n    noscriptTags,\n    styleTags,\n    title = \"\",\n    titleAttributes,\n    prioritizeSeoTags\n  } = props;\n  let { linkTags, metaTags, scriptTags } = props;\n  let priorityMethods = {\n    toComponent: () => {\n    },\n    toString: () => \"\"\n  };\n  if (prioritizeSeoTags) {\n    ({ priorityMethods, linkTags, metaTags, scriptTags } = getPriorityMethods(props));\n  }\n  return {\n    priority: priorityMethods,\n    base: getMethodsForTag(\"base\" /* BASE */, baseTag, encode),\n    bodyAttributes: getMethodsForTag(\"bodyAttributes\" /* BODY */, bodyAttributes, encode),\n    htmlAttributes: getMethodsForTag(\"htmlAttributes\" /* HTML */, htmlAttributes, encode),\n    link: getMethodsForTag(\"link\" /* LINK */, linkTags, encode),\n    meta: getMethodsForTag(\"meta\" /* META */, metaTags, encode),\n    noscript: getMethodsForTag(\"noscript\" /* NOSCRIPT */, noscriptTags, encode),\n    script: getMethodsForTag(\"script\" /* SCRIPT */, scriptTags, encode),\n    style: getMethodsForTag(\"style\" /* STYLE */, styleTags, encode),\n    title: getMethodsForTag(\"title\" /* TITLE */, { title, titleAttributes }, encode)\n  };\n};\nvar server_default = mapStateOnServer;\n\n// src/HelmetData.ts\nvar instances = [];\nvar isDocument = !!(typeof window !== \"undefined\" && window.document && window.document.createElement);\nvar HelmetData = class {\n  instances = [];\n  canUseDOM = isDocument;\n  context;\n  value = {\n    setHelmet: (serverState) => {\n      this.context.helmet = serverState;\n    },\n    helmetInstances: {\n      get: () => this.canUseDOM ? instances : this.instances,\n      add: (instance) => {\n        (this.canUseDOM ? instances : this.instances).push(instance);\n      },\n      remove: (instance) => {\n        const index = (this.canUseDOM ? instances : this.instances).indexOf(instance);\n        (this.canUseDOM ? instances : this.instances).splice(index, 1);\n      }\n    }\n  };\n  constructor(context, canUseDOM) {\n    this.context = context;\n    this.canUseDOM = canUseDOM || false;\n    if (!canUseDOM) {\n      context.helmet = server_default({\n        baseTag: [],\n        bodyAttributes: {},\n        encodeSpecialCharacters: true,\n        htmlAttributes: {},\n        linkTags: [],\n        metaTags: [],\n        noscriptTags: [],\n        scriptTags: [],\n        styleTags: [],\n        title: \"\",\n        titleAttributes: {}\n      });\n    }\n  }\n};\n\n// src/Provider.tsx\nvar defaultValue = {};\nvar Context = React2.createContext(defaultValue);\nvar HelmetProvider = class _HelmetProvider extends Component {\n  static canUseDOM = isDocument;\n  helmetData;\n  constructor(props) {\n    super(props);\n    this.helmetData = new HelmetData(this.props.context || {}, _HelmetProvider.canUseDOM);\n  }\n  render() {\n    return /* @__PURE__ */ React2.createElement(Context.Provider, { value: this.helmetData.value }, this.props.children);\n  }\n};\n\n// src/Dispatcher.tsx\nimport { Component as Component2 } from \"react\";\nimport shallowEqual from \"shallowequal\";\n\n// src/client.ts\nvar updateTags = (type, tags) => {\n  const headElement = document.head || document.querySelector(\"head\" /* HEAD */);\n  const tagNodes = headElement.querySelectorAll(`${type}[${HELMET_ATTRIBUTE}]`);\n  const oldTags = [].slice.call(tagNodes);\n  const newTags = [];\n  let indexToDelete;\n  if (tags && tags.length) {\n    tags.forEach((tag) => {\n      const newElement = document.createElement(type);\n      for (const attribute in tag) {\n        if (Object.prototype.hasOwnProperty.call(tag, attribute)) {\n          if (attribute === \"innerHTML\" /* INNER_HTML */) {\n            newElement.innerHTML = tag.innerHTML;\n          } else if (attribute === \"cssText\" /* CSS_TEXT */) {\n            if (newElement.styleSheet) {\n              newElement.styleSheet.cssText = tag.cssText;\n            } else {\n              newElement.appendChild(document.createTextNode(tag.cssText));\n            }\n          } else {\n            const attr = attribute;\n            const value = typeof tag[attr] === \"undefined\" ? \"\" : tag[attr];\n            newElement.setAttribute(attribute, value);\n          }\n        }\n      }\n      newElement.setAttribute(HELMET_ATTRIBUTE, \"true\");\n      if (oldTags.some((existingTag, index) => {\n        indexToDelete = index;\n        return newElement.isEqualNode(existingTag);\n      })) {\n        oldTags.splice(indexToDelete, 1);\n      } else {\n        newTags.push(newElement);\n      }\n    });\n  }\n  oldTags.forEach((tag) => tag.parentNode?.removeChild(tag));\n  newTags.forEach((tag) => headElement.appendChild(tag));\n  return {\n    oldTags,\n    newTags\n  };\n};\nvar updateAttributes = (tagName, attributes) => {\n  const elementTag = document.getElementsByTagName(tagName)[0];\n  if (!elementTag) {\n    return;\n  }\n  const helmetAttributeString = elementTag.getAttribute(HELMET_ATTRIBUTE);\n  const helmetAttributes = helmetAttributeString ? helmetAttributeString.split(\",\") : [];\n  const attributesToRemove = [...helmetAttributes];\n  const attributeKeys = Object.keys(attributes);\n  for (const attribute of attributeKeys) {\n    const value = attributes[attribute] || \"\";\n    if (elementTag.getAttribute(attribute) !== value) {\n      elementTag.setAttribute(attribute, value);\n    }\n    if (helmetAttributes.indexOf(attribute) === -1) {\n      helmetAttributes.push(attribute);\n    }\n    const indexToSave = attributesToRemove.indexOf(attribute);\n    if (indexToSave !== -1) {\n      attributesToRemove.splice(indexToSave, 1);\n    }\n  }\n  for (let i = attributesToRemove.length - 1; i >= 0; i -= 1) {\n    elementTag.removeAttribute(attributesToRemove[i]);\n  }\n  if (helmetAttributes.length === attributesToRemove.length) {\n    elementTag.removeAttribute(HELMET_ATTRIBUTE);\n  } else if (elementTag.getAttribute(HELMET_ATTRIBUTE) !== attributeKeys.join(\",\")) {\n    elementTag.setAttribute(HELMET_ATTRIBUTE, attributeKeys.join(\",\"));\n  }\n};\nvar updateTitle = (title, attributes) => {\n  if (typeof title !== \"undefined\" && document.title !== title) {\n    document.title = flattenArray(title);\n  }\n  updateAttributes(\"title\" /* TITLE */, attributes);\n};\nvar commitTagChanges = (newState, cb) => {\n  const {\n    baseTag,\n    bodyAttributes,\n    htmlAttributes,\n    linkTags,\n    metaTags,\n    noscriptTags,\n    onChangeClientState,\n    scriptTags,\n    styleTags,\n    title,\n    titleAttributes\n  } = newState;\n  updateAttributes(\"body\" /* BODY */, bodyAttributes);\n  updateAttributes(\"html\" /* HTML */, htmlAttributes);\n  updateTitle(title, titleAttributes);\n  const tagUpdates = {\n    baseTag: updateTags(\"base\" /* BASE */, baseTag),\n    linkTags: updateTags(\"link\" /* LINK */, linkTags),\n    metaTags: updateTags(\"meta\" /* META */, metaTags),\n    noscriptTags: updateTags(\"noscript\" /* NOSCRIPT */, noscriptTags),\n    scriptTags: updateTags(\"script\" /* SCRIPT */, scriptTags),\n    styleTags: updateTags(\"style\" /* STYLE */, styleTags)\n  };\n  const addedTags = {};\n  const removedTags = {};\n  Object.keys(tagUpdates).forEach((tagType) => {\n    const { newTags, oldTags } = tagUpdates[tagType];\n    if (newTags.length) {\n      addedTags[tagType] = newTags;\n    }\n    if (oldTags.length) {\n      removedTags[tagType] = tagUpdates[tagType].oldTags;\n    }\n  });\n  if (cb) {\n    cb();\n  }\n  onChangeClientState(newState, addedTags, removedTags);\n};\nvar _helmetCallback = null;\nvar handleStateChangeOnClient = (newState) => {\n  if (_helmetCallback) {\n    cancelAnimationFrame(_helmetCallback);\n  }\n  if (newState.defer) {\n    _helmetCallback = requestAnimationFrame(() => {\n      commitTagChanges(newState, () => {\n        _helmetCallback = null;\n      });\n    });\n  } else {\n    commitTagChanges(newState);\n    _helmetCallback = null;\n  }\n};\nvar client_default = handleStateChangeOnClient;\n\n// src/Dispatcher.tsx\nvar HelmetDispatcher = class extends Component2 {\n  rendered = false;\n  shouldComponentUpdate(nextProps) {\n    return !shallowEqual(nextProps, this.props);\n  }\n  componentDidUpdate() {\n    this.emitChange();\n  }\n  componentWillUnmount() {\n    const { helmetInstances } = this.props.context;\n    helmetInstances.remove(this);\n    this.emitChange();\n  }\n  emitChange() {\n    const { helmetInstances, setHelmet } = this.props.context;\n    let serverState = null;\n    const state = reducePropsToState(\n      helmetInstances.get().map((instance) => {\n        const props = { ...instance.props };\n        delete props.context;\n        return props;\n      })\n    );\n    if (HelmetProvider.canUseDOM) {\n      client_default(state);\n    } else if (server_default) {\n      serverState = server_default(state);\n    }\n    setHelmet(serverState);\n  }\n  // componentWillMount will be deprecated\n  // for SSR, initialize on first render\n  // constructor is also unsafe in StrictMode\n  init() {\n    if (this.rendered) {\n      return;\n    }\n    this.rendered = true;\n    const { helmetInstances } = this.props.context;\n    helmetInstances.add(this);\n    this.emitChange();\n  }\n  render() {\n    this.init();\n    return null;\n  }\n};\n\n// src/index.tsx\nvar Helmet = class extends Component3 {\n  static defaultProps = {\n    defer: true,\n    encodeSpecialCharacters: true,\n    prioritizeSeoTags: false\n  };\n  shouldComponentUpdate(nextProps) {\n    return !fastCompare(without(this.props, \"helmetData\"), without(nextProps, \"helmetData\"));\n  }\n  mapNestedChildrenToProps(child, nestedChildren) {\n    if (!nestedChildren) {\n      return null;\n    }\n    switch (child.type) {\n      case \"script\" /* SCRIPT */:\n      case \"noscript\" /* NOSCRIPT */:\n        return {\n          innerHTML: nestedChildren\n        };\n      case \"style\" /* STYLE */:\n        return {\n          cssText: nestedChildren\n        };\n      default:\n        throw new Error(\n          `<${child.type} /> elements are self-closing and can not contain children. Refer to our API for more information.`\n        );\n    }\n  }\n  flattenArrayTypeChildren(child, arrayTypeChildren, newChildProps, nestedChildren) {\n    return {\n      ...arrayTypeChildren,\n      [child.type]: [\n        ...arrayTypeChildren[child.type] || [],\n        {\n          ...newChildProps,\n          ...this.mapNestedChildrenToProps(child, nestedChildren)\n        }\n      ]\n    };\n  }\n  mapObjectTypeChildren(child, newProps, newChildProps, nestedChildren) {\n    switch (child.type) {\n      case \"title\" /* TITLE */:\n        return {\n          ...newProps,\n          [child.type]: nestedChildren,\n          titleAttributes: { ...newChildProps }\n        };\n      case \"body\" /* BODY */:\n        return {\n          ...newProps,\n          bodyAttributes: { ...newChildProps }\n        };\n      case \"html\" /* HTML */:\n        return {\n          ...newProps,\n          htmlAttributes: { ...newChildProps }\n        };\n      default:\n        return {\n          ...newProps,\n          [child.type]: { ...newChildProps }\n        };\n    }\n  }\n  mapArrayTypeChildrenToProps(arrayTypeChildren, newProps) {\n    let newFlattenedProps = { ...newProps };\n    Object.keys(arrayTypeChildren).forEach((arrayChildName) => {\n      newFlattenedProps = {\n        ...newFlattenedProps,\n        [arrayChildName]: arrayTypeChildren[arrayChildName]\n      };\n    });\n    return newFlattenedProps;\n  }\n  warnOnInvalidChildren(child, nestedChildren) {\n    invariant(\n      VALID_TAG_NAMES.some((name) => child.type === name),\n      typeof child.type === \"function\" ? `You may be attempting to nest <Helmet> components within each other, which is not allowed. Refer to our API for more information.` : `Only elements types ${VALID_TAG_NAMES.join(\n        \", \"\n      )} are allowed. Helmet does not support rendering <${child.type}> elements. Refer to our API for more information.`\n    );\n    invariant(\n      !nestedChildren || typeof nestedChildren === \"string\" || Array.isArray(nestedChildren) && !nestedChildren.some((nestedChild) => typeof nestedChild !== \"string\"),\n      `Helmet expects a string as a child of <${child.type}>. Did you forget to wrap your children in braces? ( <${child.type}>{\\`\\`}</${child.type}> ) Refer to our API for more information.`\n    );\n    return true;\n  }\n  mapChildrenToProps(children, newProps) {\n    let arrayTypeChildren = {};\n    React3.Children.forEach(children, (child) => {\n      if (!child || !child.props) {\n        return;\n      }\n      const { children: nestedChildren, ...childProps } = child.props;\n      const newChildProps = Object.keys(childProps).reduce((obj, key) => {\n        obj[HTML_TAG_MAP[key] || key] = childProps[key];\n        return obj;\n      }, {});\n      let { type } = child;\n      if (typeof type === \"symbol\") {\n        type = type.toString();\n      } else {\n        this.warnOnInvalidChildren(child, nestedChildren);\n      }\n      switch (type) {\n        case \"Symbol(react.fragment)\" /* FRAGMENT */:\n          newProps = this.mapChildrenToProps(nestedChildren, newProps);\n          break;\n        case \"link\" /* LINK */:\n        case \"meta\" /* META */:\n        case \"noscript\" /* NOSCRIPT */:\n        case \"script\" /* SCRIPT */:\n        case \"style\" /* STYLE */:\n          arrayTypeChildren = this.flattenArrayTypeChildren(\n            child,\n            arrayTypeChildren,\n            newChildProps,\n            nestedChildren\n          );\n          break;\n        default:\n          newProps = this.mapObjectTypeChildren(child, newProps, newChildProps, nestedChildren);\n          break;\n      }\n    });\n    return this.mapArrayTypeChildrenToProps(arrayTypeChildren, newProps);\n  }\n  render() {\n    const { children, ...props } = this.props;\n    let newProps = { ...props };\n    let { helmetData } = props;\n    if (children) {\n      newProps = this.mapChildrenToProps(children, newProps);\n    }\n    if (helmetData && !(helmetData instanceof HelmetData)) {\n      const data = helmetData;\n      helmetData = new HelmetData(data.context, true);\n      delete newProps.helmetData;\n    }\n    return helmetData ? /* @__PURE__ */ React3.createElement(HelmetDispatcher, { ...newProps, context: helmetData.value }) : /* @__PURE__ */ React3.createElement(Context.Consumer, null, (context) => /* @__PURE__ */ React3.createElement(HelmetDispatcher, { ...newProps, context }));\n  }\n};\nexport {\n  Helmet,\n  HelmetData,\n  HelmetProvider\n};\n"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAEA,QAAI,iBAAiB,OAAO,YAAY;AACxC,QAAI,SAAS,OAAO,QAAQ;AAC5B,QAAI,SAAS,OAAO,QAAQ;AAC5B,QAAI,iBAAiB,OAAO,gBAAgB,cAAc,CAAC,CAAC,YAAY;AAIxE,aAAS,MAAM,GAAG,GAAG;AAEnB,UAAI,MAAM,EAAG,QAAO;AAEpB,UAAI,KAAK,KAAK,OAAO,KAAK,YAAY,OAAO,KAAK,UAAU;AAC1D,YAAI,EAAE,gBAAgB,EAAE,YAAa,QAAO;AAE5C,YAAI,QAAQ,GAAG;AACf,YAAI,MAAM,QAAQ,CAAC,GAAG;AACpB,mBAAS,EAAE;AACX,cAAI,UAAU,EAAE,OAAQ,QAAO;AAC/B,eAAK,IAAI,QAAQ,QAAQ;AACvB,gBAAI,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,EAAG,QAAO;AACjC,iBAAO;AAAA,QACT;AAsBA,YAAI;AACJ,YAAI,UAAW,aAAa,OAAS,aAAa,KAAM;AACtD,cAAI,EAAE,SAAS,EAAE,KAAM,QAAO;AAC9B,eAAK,EAAE,QAAQ;AACf,iBAAO,EAAE,IAAI,GAAG,KAAK,GAAG;AACtB,gBAAI,CAAC,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC,EAAG,QAAO;AACjC,eAAK,EAAE,QAAQ;AACf,iBAAO,EAAE,IAAI,GAAG,KAAK,GAAG;AACtB,gBAAI,CAAC,MAAM,EAAE,MAAM,CAAC,GAAG,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC,EAAG,QAAO;AACpD,iBAAO;AAAA,QACT;AAEA,YAAI,UAAW,aAAa,OAAS,aAAa,KAAM;AACtD,cAAI,EAAE,SAAS,EAAE,KAAM,QAAO;AAC9B,eAAK,EAAE,QAAQ;AACf,iBAAO,EAAE,IAAI,GAAG,KAAK,GAAG;AACtB,gBAAI,CAAC,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC,EAAG,QAAO;AACjC,iBAAO;AAAA,QACT;AAGA,YAAI,kBAAkB,YAAY,OAAO,CAAC,KAAK,YAAY,OAAO,CAAC,GAAG;AACpE,mBAAS,EAAE;AACX,cAAI,UAAU,EAAE,OAAQ,QAAO;AAC/B,eAAK,IAAI,QAAQ,QAAQ;AACvB,gBAAI,EAAE,CAAC,MAAM,EAAE,CAAC,EAAG,QAAO;AAC5B,iBAAO;AAAA,QACT;AAEA,YAAI,EAAE,gBAAgB,OAAQ,QAAO,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE;AAK5E,YAAI,EAAE,YAAY,OAAO,UAAU,WAAW,OAAO,EAAE,YAAY,cAAc,OAAO,EAAE,YAAY,WAAY,QAAO,EAAE,QAAQ,MAAM,EAAE,QAAQ;AACnJ,YAAI,EAAE,aAAa,OAAO,UAAU,YAAY,OAAO,EAAE,aAAa,cAAc,OAAO,EAAE,aAAa,WAAY,QAAO,EAAE,SAAS,MAAM,EAAE,SAAS;AAGzJ,eAAO,OAAO,KAAK,CAAC;AACpB,iBAAS,KAAK;AACd,YAAI,WAAW,OAAO,KAAK,CAAC,EAAE,OAAQ,QAAO;AAE7C,aAAK,IAAI,QAAQ,QAAQ;AACvB,cAAI,CAAC,OAAO,UAAU,eAAe,KAAK,GAAG,KAAK,CAAC,CAAC,EAAG,QAAO;AAKhE,YAAI,kBAAkB,aAAa,QAAS,QAAO;AAGnD,aAAK,IAAI,QAAQ,QAAQ,KAAI;AAC3B,eAAK,KAAK,CAAC,MAAM,YAAY,KAAK,CAAC,MAAM,SAAS,KAAK,CAAC,MAAM,UAAU,EAAE,UAAU;AASlF;AAAA,UACF;AAGA,cAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC,EAAG,QAAO;AAAA,QAC7C;AAIA,eAAO;AAAA,MACT;AAEA,aAAO,MAAM,KAAK,MAAM;AAAA,IAC1B;AAGA,WAAO,UAAU,SAAS,QAAQ,GAAG,GAAG;AACtC,UAAI;AACF,eAAO,MAAM,GAAG,CAAC;AAAA,MACnB,SAAS,OAAO;AACd,aAAM,MAAM,WAAW,IAAI,MAAM,kBAAkB,GAAI;AAMrD,kBAAQ,KAAK,gDAAgD;AAC7D,iBAAO;AAAA,QACT;AAEA,cAAM;AAAA,MACR;AAAA,IACF;AAAA;AAAA;;;AC1IA;AAAA;AAAA;AAoBA,QAAIA,aAAY,SAAS,WAAW,QAAQ,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AAC5D,UAAI,MAAuC;AACzC,YAAI,WAAW,QAAW;AACxB,gBAAM,IAAI,MAAM,8CAA8C;AAAA,QAChE;AAAA,MACF;AAEA,UAAI,CAAC,WAAW;AACd,YAAI;AACJ,YAAI,WAAW,QAAW;AACxB,kBAAQ,IAAI;AAAA,YACV;AAAA,UAEF;AAAA,QACF,OAAO;AACL,cAAI,OAAO,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAC5B,cAAI,WAAW;AACf,kBAAQ,IAAI;AAAA,YACV,OAAO,QAAQ,OAAO,WAAW;AAAE,qBAAO,KAAK,UAAU;AAAA,YAAG,CAAC;AAAA,UAC/D;AACA,gBAAM,OAAO;AAAA,QACf;AAEA,cAAM,cAAc;AACpB,cAAM;AAAA,MACR;AAAA,IACF;AAEA,WAAO,UAAUA;AAAA;AAAA;;;AChDjB;AAAA;AAEA,WAAO,UAAU,SAASC,cAAa,MAAM,MAAM,SAAS,gBAAgB;AAC1E,UAAI,MAAM,UAAU,QAAQ,KAAK,gBAAgB,MAAM,IAAI,IAAI;AAE/D,UAAI,QAAQ,QAAQ;AAClB,eAAO,CAAC,CAAC;AAAA,MACX;AAEA,UAAI,SAAS,MAAM;AACjB,eAAO;AAAA,MACT;AAEA,UAAI,OAAO,SAAS,YAAY,CAAC,QAAQ,OAAO,SAAS,YAAY,CAAC,MAAM;AAC1E,eAAO;AAAA,MACT;AAEA,UAAI,QAAQ,OAAO,KAAK,IAAI;AAC5B,UAAI,QAAQ,OAAO,KAAK,IAAI;AAE5B,UAAI,MAAM,WAAW,MAAM,QAAQ;AACjC,eAAO;AAAA,MACT;AAEA,UAAI,kBAAkB,OAAO,UAAU,eAAe,KAAK,IAAI;AAG/D,eAAS,MAAM,GAAG,MAAM,MAAM,QAAQ,OAAO;AAC3C,YAAI,MAAM,MAAM,GAAG;AAEnB,YAAI,CAAC,gBAAgB,GAAG,GAAG;AACzB,iBAAO;AAAA,QACT;AAEA,YAAI,SAAS,KAAK,GAAG;AACrB,YAAI,SAAS,KAAK,GAAG;AAErB,cAAM,UAAU,QAAQ,KAAK,gBAAgB,QAAQ,QAAQ,GAAG,IAAI;AAEpE,YAAI,QAAQ,SAAU,QAAQ,UAAU,WAAW,QAAS;AAC1D,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAAA;AAAA;;;AC5CA,mBAAgD;AAChD,gCAAwB;AACxB,uBAAsB;AAGtB,IAAAC,gBAAkC;AAGlC,IAAAA,gBAAkB;AA8blB,IAAAA,gBAAwC;AACxC,0BAAyB;AA5bzB,IAAI,aAA6B,CAAC,eAAe;AAC/C,aAAW,MAAM,IAAI;AACrB,aAAW,MAAM,IAAI;AACrB,aAAW,MAAM,IAAI;AACrB,aAAW,MAAM,IAAI;AACrB,aAAW,MAAM,IAAI;AACrB,aAAW,MAAM,IAAI;AACrB,aAAW,UAAU,IAAI;AACzB,aAAW,QAAQ,IAAI;AACvB,aAAW,OAAO,IAAI;AACtB,aAAW,OAAO,IAAI;AACtB,aAAW,UAAU,IAAI;AACzB,SAAO;AACT,GAAG,aAAa,CAAC,CAAC;AAClB,IAAI,oBAAoB;AAAA,EACtB,MAAM,EAAE,KAAK,CAAC,WAAW,aAAa,WAAW,EAAE;AAAA,EACnD,QAAQ,EAAE,MAAM,CAAC,qBAAqB,EAAE;AAAA,EACxC,MAAM;AAAA,IACJ,SAAS;AAAA,IACT,MAAM,CAAC,aAAa,UAAU,aAAa;AAAA,IAC3C,UAAU;AAAA,MACR;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAI,kBAAkB,OAAO,OAAO,SAAS;AAC7C,IAAI,gBAAgB;AAAA,EAClB,WAAW;AAAA,EACX,SAAS;AAAA,EACT,OAAO;AAAA,EACP,iBAAiB;AAAA,EACjB,aAAa;AAAA,EACb,cAAc;AAAA,EACd,UAAU;AAAA,EACV,UAAU;AACZ;AACA,IAAI,eAAe,OAAO,QAAQ,aAAa,EAAE;AAAA,EAC/C,CAAC,OAAO,CAAC,KAAK,KAAK,MAAM;AACvB,UAAM,KAAK,IAAI;AACf,WAAO;AAAA,EACT;AAAA,EACA,CAAC;AACH;AACA,IAAI,mBAAmB;AAGvB,IAAI,eAAe;AAAA,EACjB,eAAe;AAAA,EACf,OAAO;AAAA,EACP,2BAA2B;AAAA,EAC3B,wBAAwB;AAAA,EACxB,gBAAgB;AAAA,EAChB,qBAAqB;AACvB;AACA,IAAI,uBAAuB,CAAC,WAAW,aAAa;AAClD,WAAS,IAAI,UAAU,SAAS,GAAG,KAAK,GAAG,KAAK,GAAG;AACjD,UAAM,QAAQ,UAAU,CAAC;AACzB,QAAI,OAAO,UAAU,eAAe,KAAK,OAAO,QAAQ,GAAG;AACzD,aAAO,MAAM,QAAQ;AAAA,IACvB;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAI,wBAAwB,CAAC,cAAc;AACzC,MAAI,iBAAiB;AAAA,IAAqB;AAAA,IAAW;AAAA;AAAA,EAAmB;AACxE,QAAM,oBAAoB,qBAAqB,WAAW,aAAa,cAAc;AACrF,MAAI,MAAM,QAAQ,cAAc,GAAG;AACjC,qBAAiB,eAAe,KAAK,EAAE;AAAA,EACzC;AACA,MAAI,qBAAqB,gBAAgB;AACvC,WAAO,kBAAkB,QAAQ,OAAO,MAAM,cAAc;AAAA,EAC9D;AACA,QAAM,wBAAwB,qBAAqB,WAAW,aAAa,aAAa;AACxF,SAAO,kBAAkB,yBAAyB;AACpD;AACA,IAAI,yBAAyB,CAAC,cAAc,qBAAqB,WAAW,aAAa,sBAAsB,MAAM,MAAM;AAC3H;AACA,IAAI,6BAA6B,CAAC,SAAS,cAAc,UAAU,OAAO,CAAC,UAAU,OAAO,MAAM,OAAO,MAAM,WAAW,EAAE,IAAI,CAAC,UAAU,MAAM,OAAO,CAAC,EAAE,OAAO,CAAC,UAAU,aAAa,EAAE,GAAG,UAAU,GAAG,QAAQ,IAAI,CAAC,CAAC;AAC1N,IAAI,0BAA0B,CAAC,mBAAmB,cAAc,UAAU,OAAO,CAAC,UAAU,OAAO;AAAA,EAAM;AAAA;AAAiB,MAAM,WAAW,EAAE,IAAI,CAAC,UAAU;AAAA,EAAM;AAAA;AAAiB,CAAC,EAAE,QAAQ,EAAE,OAAO,CAAC,kBAAkB,QAAQ;AAChO,MAAI,CAAC,iBAAiB,QAAQ;AAC5B,UAAM,OAAO,OAAO,KAAK,GAAG;AAC5B,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK,GAAG;AACvC,YAAM,eAAe,KAAK,CAAC;AAC3B,YAAM,wBAAwB,aAAa,YAAY;AACvD,UAAI,kBAAkB,QAAQ,qBAAqB,MAAM,MAAM,IAAI,qBAAqB,GAAG;AACzF,eAAO,iBAAiB,OAAO,GAAG;AAAA,MACpC;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT,GAAG,CAAC,CAAC;AACL,IAAI,OAAO,CAAC,QAAQ,WAAW,OAAO,QAAQ,SAAS,cAAc,QAAQ,KAAK,GAAG;AACrF,IAAI,uBAAuB,CAAC,SAAS,mBAAmB,cAAc;AACpE,QAAM,mBAAmB,CAAC;AAC1B,SAAO,UAAU,OAAO,CAAC,UAAU;AACjC,QAAI,MAAM,QAAQ,MAAM,OAAO,CAAC,GAAG;AACjC,aAAO;AAAA,IACT;AACA,QAAI,OAAO,MAAM,OAAO,MAAM,aAAa;AACzC;AAAA,QACE,WAAW,OAAO,mDAAmD,OAAO,MAAM,OAAO,CAAC;AAAA,MAC5F;AAAA,IACF;AACA,WAAO;AAAA,EACT,CAAC,EAAE,IAAI,CAAC,UAAU,MAAM,OAAO,CAAC,EAAE,QAAQ,EAAE,OAAO,CAAC,cAAc,iBAAiB;AACjF,UAAM,mBAAmB,CAAC;AAC1B,iBAAa,OAAO,CAAC,QAAQ;AAC3B,UAAI;AACJ,YAAM,QAAQ,OAAO,KAAK,GAAG;AAC7B,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK,GAAG;AACxC,cAAM,eAAe,MAAM,CAAC;AAC5B,cAAM,wBAAwB,aAAa,YAAY;AACvD,YAAI,kBAAkB,QAAQ,qBAAqB,MAAM,MAAM,EAAE,wBAAwB,SAAmB,IAAI,mBAAmB,EAAE,YAAY,MAAM,gBAAgB,EAAE,0BAA0B,SAAmB,IAAI,qBAAqB,EAAE,YAAY,MAAM,eAAe;AAChR,gCAAsB;AAAA,QACxB;AACA,YAAI,kBAAkB,QAAQ,YAAY,MAAM,OAAO,iBAAiB,eAAgC,iBAAiB,aAA4B,iBAAiB,aAA6B;AACjM,gCAAsB;AAAA,QACxB;AAAA,MACF;AACA,UAAI,CAAC,uBAAuB,CAAC,IAAI,mBAAmB,GAAG;AACrD,eAAO;AAAA,MACT;AACA,YAAM,QAAQ,IAAI,mBAAmB,EAAE,YAAY;AACnD,UAAI,CAAC,iBAAiB,mBAAmB,GAAG;AAC1C,yBAAiB,mBAAmB,IAAI,CAAC;AAAA,MAC3C;AACA,UAAI,CAAC,iBAAiB,mBAAmB,GAAG;AAC1C,yBAAiB,mBAAmB,IAAI,CAAC;AAAA,MAC3C;AACA,UAAI,CAAC,iBAAiB,mBAAmB,EAAE,KAAK,GAAG;AACjD,yBAAiB,mBAAmB,EAAE,KAAK,IAAI;AAC/C,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT,CAAC,EAAE,QAAQ,EAAE,QAAQ,CAAC,QAAQ,aAAa,KAAK,GAAG,CAAC;AACpD,UAAM,OAAO,OAAO,KAAK,gBAAgB;AACzC,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK,GAAG;AACvC,YAAM,eAAe,KAAK,CAAC;AAC3B,YAAM,WAAW;AAAA,QACf,GAAG,iBAAiB,YAAY;AAAA,QAChC,GAAG,iBAAiB,YAAY;AAAA,MAClC;AACA,uBAAiB,YAAY,IAAI;AAAA,IACnC;AACA,WAAO;AAAA,EACT,GAAG,CAAC,CAAC,EAAE,QAAQ;AACjB;AACA,IAAI,0BAA0B,CAAC,WAAW,eAAe;AACvD,MAAI,MAAM,QAAQ,SAAS,KAAK,UAAU,QAAQ;AAChD,aAAS,QAAQ,GAAG,QAAQ,UAAU,QAAQ,SAAS,GAAG;AACxD,YAAM,OAAO,UAAU,KAAK;AAC5B,UAAI,KAAK,UAAU,GAAG;AACpB,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAI,qBAAqB,CAAC,eAAe;AAAA,EACvC,SAAS,wBAAwB;AAAA,IAAC;AAAA;AAAA,EAAiB,GAAG,SAAS;AAAA,EAC/D,gBAAgB,2BAA2B,kBAA6B,SAAS;AAAA,EACjF,OAAO,qBAAqB,WAAW,aAAa,KAAK;AAAA,EACzD,QAAQ,qBAAqB,WAAW,aAAa,yBAAyB;AAAA,EAC9E,gBAAgB,2BAA2B,kBAA6B,SAAS;AAAA,EACjF,UAAU;AAAA,IACR;AAAA,IACA;AAAA,MAAC;AAAA,MAAiB;AAAA;AAAA,IAAiB;AAAA,IACnC;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR;AAAA,IACA;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,IACF;AAAA,IACA;AAAA,EACF;AAAA,EACA,cAAc,qBAAqB,YAA2B;AAAA,IAAC;AAAA;AAAA,EAA4B,GAAG,SAAS;AAAA,EACvG,qBAAqB,uBAAuB,SAAS;AAAA,EACrD,YAAY;AAAA,IACV;AAAA,IACA;AAAA,MAAC;AAAA,MAAiB;AAAA;AAAA,IAA4B;AAAA,IAC9C;AAAA,EACF;AAAA,EACA,WAAW,qBAAqB,SAAqB;AAAA,IAAC;AAAA;AAAA,EAAwB,GAAG,SAAS;AAAA,EAC1F,OAAO,sBAAsB,SAAS;AAAA,EACtC,iBAAiB,2BAA2B,mBAA+B,SAAS;AAAA,EACpF,mBAAmB,wBAAwB,WAAW,aAAa,mBAAmB;AACxF;AACA,IAAI,eAAe,CAAC,kBAAkB,MAAM,QAAQ,aAAa,IAAI,cAAc,KAAK,EAAE,IAAI;AAC9F,IAAI,oBAAoB,CAAC,OAAO,YAAY;AAC1C,QAAM,OAAO,OAAO,KAAK,KAAK;AAC9B,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK,GAAG;AACvC,QAAI,QAAQ,KAAK,CAAC,CAAC,KAAK,QAAQ,KAAK,CAAC,CAAC,EAAE,SAAS,MAAM,KAAK,CAAC,CAAC,CAAC,GAAG;AACjE,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAI,cAAc,CAAC,cAAc,iBAAiB;AAChD,MAAI,MAAM,QAAQ,YAAY,GAAG;AAC/B,WAAO,aAAa;AAAA,MAClB,CAAC,KAAK,iBAAiB;AACrB,YAAI,kBAAkB,cAAc,YAAY,GAAG;AACjD,cAAI,SAAS,KAAK,YAAY;AAAA,QAChC,OAAO;AACL,cAAI,QAAQ,KAAK,YAAY;AAAA,QAC/B;AACA,eAAO;AAAA,MACT;AAAA,MACA,EAAE,UAAU,CAAC,GAAG,SAAS,CAAC,EAAE;AAAA,IAC9B;AAAA,EACF;AACA,SAAO,EAAE,SAAS,cAAc,UAAU,CAAC,EAAE;AAC/C;AACA,IAAI,UAAU,CAAC,KAAK,QAAQ;AAC1B,SAAO;AAAA,IACL,GAAG;AAAA,IACH,CAAC,GAAG,GAAG;AAAA,EACT;AACF;AAGA,IAAI,oBAAoB;AAAA,EAAC;AAAA,EAA2B;AAAA,EAAuB;AAAA;AAAmB;AAC9F,IAAI,0BAA0B,CAAC,KAAK,SAAS,SAAS;AACpD,MAAI,WAAW,OAAO;AACpB,WAAO,OAAO,GAAG;AAAA,EACnB;AACA,SAAO,OAAO,GAAG,EAAE,QAAQ,MAAM,OAAO,EAAE,QAAQ,MAAM,MAAM,EAAE,QAAQ,MAAM,MAAM,EAAE,QAAQ,MAAM,QAAQ,EAAE,QAAQ,MAAM,QAAQ;AACtI;AACA,IAAI,oCAAoC,CAAC,eAAe,OAAO,KAAK,UAAU,EAAE,OAAO,CAAC,KAAK,QAAQ;AACnG,QAAM,OAAO,OAAO,WAAW,GAAG,MAAM,cAAc,GAAG,GAAG,KAAK,WAAW,GAAG,CAAC,MAAM,GAAG,GAAG;AAC5F,SAAO,MAAM,GAAG,GAAG,IAAI,IAAI,KAAK;AAClC,GAAG,EAAE;AACL,IAAI,wBAAwB,CAAC,MAAM,OAAO,YAAY,WAAW;AAC/D,QAAM,kBAAkB,kCAAkC,UAAU;AACpE,QAAM,iBAAiB,aAAa,KAAK;AACzC,SAAO,kBAAkB,IAAI,IAAI,IAAI,gBAAgB,WAAW,eAAe,IAAI;AAAA,IACjF;AAAA,IACA;AAAA,EACF,CAAC,KAAK,IAAI,MAAM,IAAI,IAAI,IAAI,gBAAgB,WAAW;AAAA,IACrD;AAAA,IACA;AAAA,EACF,CAAC,KAAK,IAAI;AACZ;AACA,IAAI,uBAAuB,CAAC,MAAM,MAAM,SAAS,SAAS,KAAK,OAAO,CAAC,KAAK,MAAM;AAChF,QAAM,MAAM;AACZ,QAAM,gBAAgB,OAAO,KAAK,GAAG,EAAE;AAAA,IACrC,CAAC,cAAc,EAAE,cAAc,eAAgC,cAAc;AAAA,EAC/E,EAAE,OAAO,CAAC,QAAQ,cAAc;AAC9B,UAAM,OAAO,OAAO,IAAI,SAAS,MAAM,cAAc,YAAY,GAAG,SAAS,KAAK,wBAAwB,IAAI,SAAS,GAAG,MAAM,CAAC;AACjI,WAAO,SAAS,GAAG,MAAM,IAAI,IAAI,KAAK;AAAA,EACxC,GAAG,EAAE;AACL,QAAM,aAAa,IAAI,aAAa,IAAI,WAAW;AACnD,QAAM,gBAAgB,kBAAkB,QAAQ,IAAI,MAAM;AAC1D,SAAO,GAAG,GAAG,IAAI,IAAI,IAAI,gBAAgB,WAAW,aAAa,GAAG,gBAAgB,OAAO,IAAI,UAAU,KAAK,IAAI,GAAG;AACvH,GAAG,EAAE;AACL,IAAI,uCAAuC,CAAC,YAAY,YAAY,CAAC,MAAM,OAAO,KAAK,UAAU,EAAE,OAAO,CAAC,KAAK,QAAQ;AACtH,QAAM,SAAS,cAAc,GAAG;AAChC,MAAI,UAAU,GAAG,IAAI,WAAW,GAAG;AACnC,SAAO;AACT,GAAG,SAAS;AACZ,IAAI,gCAAgC,CAAC,OAAO,OAAO,eAAe;AAChE,QAAM,YAAY;AAAA,IAChB,KAAK;AAAA,IACL,CAAC,gBAAgB,GAAG;AAAA,EACtB;AACA,QAAM,QAAQ,qCAAqC,YAAY,SAAS;AACxE,SAAO,CAAC,cAAAC,QAAM,cAAc,SAAqB,OAAO,KAAK,CAAC;AAChE;AACA,IAAI,+BAA+B,CAAC,MAAM,SAAS,KAAK,IAAI,CAAC,KAAK,MAAM;AACtE,QAAM,YAAY;AAAA,IAChB,KAAK;AAAA,IACL,CAAC,gBAAgB,GAAG;AAAA,EACtB;AACA,SAAO,KAAK,GAAG,EAAE,QAAQ,CAAC,cAAc;AACtC,UAAM,SAAS,cAAc,SAAS;AACtC,UAAM,kBAAkB,UAAU;AAClC,QAAI,oBAAoB,eAAgC,oBAAoB,WAA0B;AACpG,YAAM,UAAU,IAAI,aAAa,IAAI;AACrC,gBAAU,0BAA0B,EAAE,QAAQ,QAAQ;AAAA,IACxD,OAAO;AACL,gBAAU,eAAe,IAAI,IAAI,SAAS;AAAA,IAC5C;AAAA,EACF,CAAC;AACD,SAAO,cAAAA,QAAM,cAAc,MAAM,SAAS;AAC5C,CAAC;AACD,IAAI,mBAAmB,CAAC,MAAM,MAAM,SAAS,SAAS;AACpD,UAAQ,MAAM;AAAA,IACZ,KAAK;AACH,aAAO;AAAA,QACL,aAAa,MAAM,8BAA8B,MAAM,KAAK,OAAO,KAAK,eAAe;AAAA,QACvF,UAAU,MAAM,sBAAsB,MAAM,KAAK,OAAO,KAAK,iBAAiB,MAAM;AAAA,MACtF;AAAA,IACF,KAAK;AAAA,IACL,KAAK;AACH,aAAO;AAAA,QACL,aAAa,MAAM,qCAAqC,IAAI;AAAA,QAC5D,UAAU,MAAM,kCAAkC,IAAI;AAAA,MACxD;AAAA,IACF;AACE,aAAO;AAAA,QACL,aAAa,MAAM,6BAA6B,MAAM,IAAI;AAAA,QAC1D,UAAU,MAAM,qBAAqB,MAAM,MAAM,MAAM;AAAA,MACzD;AAAA,EACJ;AACF;AACA,IAAI,qBAAqB,CAAC,EAAE,UAAU,UAAU,YAAY,OAAO,MAAM;AACvE,QAAM,OAAO,YAAY,UAAU,kBAAkB,IAAI;AACzD,QAAM,OAAO,YAAY,UAAU,kBAAkB,IAAI;AACzD,QAAM,SAAS,YAAY,YAAY,kBAAkB,MAAM;AAC/D,QAAM,kBAAkB;AAAA,IACtB,aAAa,MAAM;AAAA,MACjB,GAAG,6BAA6B,QAAmB,KAAK,QAAQ;AAAA,MAChE,GAAG,6BAA6B,QAAmB,KAAK,QAAQ;AAAA,MAChE,GAAG,6BAA6B,UAAuB,OAAO,QAAQ;AAAA,IACxE;AAAA,IACA,UAAU;AAAA;AAAA,MAER,GAAG,iBAAiB,QAAmB,KAAK,UAAU,MAAM,CAAC,IAAI;AAAA,QAC/D;AAAA,QACA,KAAK;AAAA,QACL;AAAA,MACF,CAAC,IAAI,iBAAiB,UAAuB,OAAO,UAAU,MAAM,CAAC;AAAA;AAAA,EAEzE;AACA,SAAO;AAAA,IACL;AAAA,IACA,UAAU,KAAK;AAAA,IACf,UAAU,KAAK;AAAA,IACf,YAAY,OAAO;AAAA,EACrB;AACF;AACA,IAAI,mBAAmB,CAAC,UAAU;AAChC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,SAAS;AAAA,IACT;AAAA,IACA;AAAA,IACA;AAAA,IACA,QAAQ;AAAA,IACR;AAAA,IACA;AAAA,EACF,IAAI;AACJ,MAAI,EAAE,UAAU,UAAU,WAAW,IAAI;AACzC,MAAI,kBAAkB;AAAA,IACpB,aAAa,MAAM;AAAA,IACnB;AAAA,IACA,UAAU,MAAM;AAAA,EAClB;AACA,MAAI,mBAAmB;AACrB,KAAC,EAAE,iBAAiB,UAAU,UAAU,WAAW,IAAI,mBAAmB,KAAK;AAAA,EACjF;AACA,SAAO;AAAA,IACL,UAAU;AAAA,IACV,MAAM,iBAAiB,QAAmB,SAAS,MAAM;AAAA,IACzD,gBAAgB,iBAAiB,kBAA6B,gBAAgB,MAAM;AAAA,IACpF,gBAAgB,iBAAiB,kBAA6B,gBAAgB,MAAM;AAAA,IACpF,MAAM,iBAAiB,QAAmB,UAAU,MAAM;AAAA,IAC1D,MAAM,iBAAiB,QAAmB,UAAU,MAAM;AAAA,IAC1D,UAAU,iBAAiB,YAA2B,cAAc,MAAM;AAAA,IAC1E,QAAQ,iBAAiB,UAAuB,YAAY,MAAM;AAAA,IAClE,OAAO,iBAAiB,SAAqB,WAAW,MAAM;AAAA,IAC9D,OAAO,iBAAiB,SAAqB,EAAE,OAAO,gBAAgB,GAAG,MAAM;AAAA,EACjF;AACF;AACA,IAAI,iBAAiB;AAGrB,IAAI,YAAY,CAAC;AACjB,IAAI,aAAa,CAAC,EAAE,OAAO,WAAW,eAAe,OAAO,YAAY,OAAO,SAAS;AACxF,IAAI,aAAa,MAAM;AAAA,EAmBrB,YAAY,SAAS,WAAW;AAlBhC,qCAAY,CAAC;AACb,qCAAY;AACZ;AACA,iCAAQ;AAAA,MACN,WAAW,CAAC,gBAAgB;AAC1B,aAAK,QAAQ,SAAS;AAAA,MACxB;AAAA,MACA,iBAAiB;AAAA,QACf,KAAK,MAAM,KAAK,YAAY,YAAY,KAAK;AAAA,QAC7C,KAAK,CAAC,aAAa;AACjB,WAAC,KAAK,YAAY,YAAY,KAAK,WAAW,KAAK,QAAQ;AAAA,QAC7D;AAAA,QACA,QAAQ,CAAC,aAAa;AACpB,gBAAM,SAAS,KAAK,YAAY,YAAY,KAAK,WAAW,QAAQ,QAAQ;AAC5E,WAAC,KAAK,YAAY,YAAY,KAAK,WAAW,OAAO,OAAO,CAAC;AAAA,QAC/D;AAAA,MACF;AAAA,IACF;AAEE,SAAK,UAAU;AACf,SAAK,YAAY,aAAa;AAC9B,QAAI,CAAC,WAAW;AACd,cAAQ,SAAS,eAAe;AAAA,QAC9B,SAAS,CAAC;AAAA,QACV,gBAAgB,CAAC;AAAA,QACjB,yBAAyB;AAAA,QACzB,gBAAgB,CAAC;AAAA,QACjB,UAAU,CAAC;AAAA,QACX,UAAU,CAAC;AAAA,QACX,cAAc,CAAC;AAAA,QACf,YAAY,CAAC;AAAA,QACb,WAAW,CAAC;AAAA,QACZ,OAAO;AAAA,QACP,iBAAiB,CAAC;AAAA,MACpB,CAAC;AAAA,IACH;AAAA,EACF;AACF;AAGA,IAAI,eAAe,CAAC;AACpB,IAAI,UAAU,cAAAC,QAAO,cAAc,YAAY;AAzb/C;AA0bA,IAAI,kBAAiB,mBAA8B,wBAAU;AAAA,EAG3D,YAAY,OAAO;AACjB,UAAM,KAAK;AAFb;AAGE,SAAK,aAAa,IAAI,WAAW,KAAK,MAAM,WAAW,CAAC,GAAG,GAAgB,SAAS;AAAA,EACtF;AAAA,EACA,SAAS;AACP,WAAuB,cAAAA,QAAO,cAAc,QAAQ,UAAU,EAAE,OAAO,KAAK,WAAW,MAAM,GAAG,KAAK,MAAM,QAAQ;AAAA,EACrH;AACF,GATE,cADmB,IACZ,aAAY,aADA;AAiBrB,IAAI,aAAa,CAAC,MAAM,SAAS;AAC/B,QAAM,cAAc,SAAS,QAAQ,SAAS;AAAA,IAAc;AAAA;AAAA,EAAiB;AAC7E,QAAM,WAAW,YAAY,iBAAiB,GAAG,IAAI,IAAI,gBAAgB,GAAG;AAC5E,QAAM,UAAU,CAAC,EAAE,MAAM,KAAK,QAAQ;AACtC,QAAM,UAAU,CAAC;AACjB,MAAI;AACJ,MAAI,QAAQ,KAAK,QAAQ;AACvB,SAAK,QAAQ,CAAC,QAAQ;AACpB,YAAM,aAAa,SAAS,cAAc,IAAI;AAC9C,iBAAW,aAAa,KAAK;AAC3B,YAAI,OAAO,UAAU,eAAe,KAAK,KAAK,SAAS,GAAG;AACxD,cAAI,cAAc,aAA8B;AAC9C,uBAAW,YAAY,IAAI;AAAA,UAC7B,WAAW,cAAc,WAA0B;AACjD,gBAAI,WAAW,YAAY;AACzB,yBAAW,WAAW,UAAU,IAAI;AAAA,YACtC,OAAO;AACL,yBAAW,YAAY,SAAS,eAAe,IAAI,OAAO,CAAC;AAAA,YAC7D;AAAA,UACF,OAAO;AACL,kBAAM,OAAO;AACb,kBAAM,QAAQ,OAAO,IAAI,IAAI,MAAM,cAAc,KAAK,IAAI,IAAI;AAC9D,uBAAW,aAAa,WAAW,KAAK;AAAA,UAC1C;AAAA,QACF;AAAA,MACF;AACA,iBAAW,aAAa,kBAAkB,MAAM;AAChD,UAAI,QAAQ,KAAK,CAAC,aAAa,UAAU;AACvC,wBAAgB;AAChB,eAAO,WAAW,YAAY,WAAW;AAAA,MAC3C,CAAC,GAAG;AACF,gBAAQ,OAAO,eAAe,CAAC;AAAA,MACjC,OAAO;AACL,gBAAQ,KAAK,UAAU;AAAA,MACzB;AAAA,IACF,CAAC;AAAA,EACH;AACA,UAAQ,QAAQ,CAAC,QAAK;AAhfxB,QAAAC;AAgf2B,YAAAA,MAAA,IAAI,eAAJ,gBAAAA,IAAgB,YAAY;AAAA,GAAI;AACzD,UAAQ,QAAQ,CAAC,QAAQ,YAAY,YAAY,GAAG,CAAC;AACrD,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AACA,IAAI,mBAAmB,CAAC,SAAS,eAAe;AAC9C,QAAM,aAAa,SAAS,qBAAqB,OAAO,EAAE,CAAC;AAC3D,MAAI,CAAC,YAAY;AACf;AAAA,EACF;AACA,QAAM,wBAAwB,WAAW,aAAa,gBAAgB;AACtE,QAAM,mBAAmB,wBAAwB,sBAAsB,MAAM,GAAG,IAAI,CAAC;AACrF,QAAM,qBAAqB,CAAC,GAAG,gBAAgB;AAC/C,QAAM,gBAAgB,OAAO,KAAK,UAAU;AAC5C,aAAW,aAAa,eAAe;AACrC,UAAM,QAAQ,WAAW,SAAS,KAAK;AACvC,QAAI,WAAW,aAAa,SAAS,MAAM,OAAO;AAChD,iBAAW,aAAa,WAAW,KAAK;AAAA,IAC1C;AACA,QAAI,iBAAiB,QAAQ,SAAS,MAAM,IAAI;AAC9C,uBAAiB,KAAK,SAAS;AAAA,IACjC;AACA,UAAM,cAAc,mBAAmB,QAAQ,SAAS;AACxD,QAAI,gBAAgB,IAAI;AACtB,yBAAmB,OAAO,aAAa,CAAC;AAAA,IAC1C;AAAA,EACF;AACA,WAAS,IAAI,mBAAmB,SAAS,GAAG,KAAK,GAAG,KAAK,GAAG;AAC1D,eAAW,gBAAgB,mBAAmB,CAAC,CAAC;AAAA,EAClD;AACA,MAAI,iBAAiB,WAAW,mBAAmB,QAAQ;AACzD,eAAW,gBAAgB,gBAAgB;AAAA,EAC7C,WAAW,WAAW,aAAa,gBAAgB,MAAM,cAAc,KAAK,GAAG,GAAG;AAChF,eAAW,aAAa,kBAAkB,cAAc,KAAK,GAAG,CAAC;AAAA,EACnE;AACF;AACA,IAAI,cAAc,CAAC,OAAO,eAAe;AACvC,MAAI,OAAO,UAAU,eAAe,SAAS,UAAU,OAAO;AAC5D,aAAS,QAAQ,aAAa,KAAK;AAAA,EACrC;AACA,mBAAiB,SAAqB,UAAU;AAClD;AACA,IAAI,mBAAmB,CAAC,UAAU,OAAO;AACvC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,mBAAiB,QAAmB,cAAc;AAClD,mBAAiB,QAAmB,cAAc;AAClD,cAAY,OAAO,eAAe;AAClC,QAAM,aAAa;AAAA,IACjB,SAAS,WAAW,QAAmB,OAAO;AAAA,IAC9C,UAAU,WAAW,QAAmB,QAAQ;AAAA,IAChD,UAAU,WAAW,QAAmB,QAAQ;AAAA,IAChD,cAAc,WAAW,YAA2B,YAAY;AAAA,IAChE,YAAY,WAAW,UAAuB,UAAU;AAAA,IACxD,WAAW,WAAW,SAAqB,SAAS;AAAA,EACtD;AACA,QAAM,YAAY,CAAC;AACnB,QAAM,cAAc,CAAC;AACrB,SAAO,KAAK,UAAU,EAAE,QAAQ,CAAC,YAAY;AAC3C,UAAM,EAAE,SAAS,QAAQ,IAAI,WAAW,OAAO;AAC/C,QAAI,QAAQ,QAAQ;AAClB,gBAAU,OAAO,IAAI;AAAA,IACvB;AACA,QAAI,QAAQ,QAAQ;AAClB,kBAAY,OAAO,IAAI,WAAW,OAAO,EAAE;AAAA,IAC7C;AAAA,EACF,CAAC;AACD,MAAI,IAAI;AACN,OAAG;AAAA,EACL;AACA,sBAAoB,UAAU,WAAW,WAAW;AACtD;AACA,IAAI,kBAAkB;AACtB,IAAI,4BAA4B,CAAC,aAAa;AAC5C,MAAI,iBAAiB;AACnB,yBAAqB,eAAe;AAAA,EACtC;AACA,MAAI,SAAS,OAAO;AAClB,sBAAkB,sBAAsB,MAAM;AAC5C,uBAAiB,UAAU,MAAM;AAC/B,0BAAkB;AAAA,MACpB,CAAC;AAAA,IACH,CAAC;AAAA,EACH,OAAO;AACL,qBAAiB,QAAQ;AACzB,sBAAkB;AAAA,EACpB;AACF;AACA,IAAI,iBAAiB;AAGrB,IAAI,mBAAmB,cAAc,cAAAC,UAAW;AAAA,EAAzB;AAAA;AACrB,oCAAW;AAAA;AAAA,EACX,sBAAsB,WAAW;AAC/B,WAAO,KAAC,oBAAAC,SAAa,WAAW,KAAK,KAAK;AAAA,EAC5C;AAAA,EACA,qBAAqB;AACnB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,uBAAuB;AACrB,UAAM,EAAE,gBAAgB,IAAI,KAAK,MAAM;AACvC,oBAAgB,OAAO,IAAI;AAC3B,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,aAAa;AACX,UAAM,EAAE,iBAAiB,UAAU,IAAI,KAAK,MAAM;AAClD,QAAI,cAAc;AAClB,UAAM,QAAQ;AAAA,MACZ,gBAAgB,IAAI,EAAE,IAAI,CAAC,aAAa;AACtC,cAAM,QAAQ,EAAE,GAAG,SAAS,MAAM;AAClC,eAAO,MAAM;AACb,eAAO;AAAA,MACT,CAAC;AAAA,IACH;AACA,QAAI,eAAe,WAAW;AAC5B,qBAAe,KAAK;AAAA,IACtB,WAAW,gBAAgB;AACzB,oBAAc,eAAe,KAAK;AAAA,IACpC;AACA,cAAU,WAAW;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO;AACL,QAAI,KAAK,UAAU;AACjB;AAAA,IACF;AACA,SAAK,WAAW;AAChB,UAAM,EAAE,gBAAgB,IAAI,KAAK,MAAM;AACvC,oBAAgB,IAAI,IAAI;AACxB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,SAAS;AACP,SAAK,KAAK;AACV,WAAO;AAAA,EACT;AACF;AAtoBA,IAAAF;AAyoBA,IAAI,UAASA,MAAA,cAAc,aAAAG,UAAW;AAAA,EAMpC,sBAAsB,WAAW;AAC/B,WAAO,KAAC,0BAAAC,SAAY,QAAQ,KAAK,OAAO,YAAY,GAAG,QAAQ,WAAW,YAAY,CAAC;AAAA,EACzF;AAAA,EACA,yBAAyB,OAAO,gBAAgB;AAC9C,QAAI,CAAC,gBAAgB;AACnB,aAAO;AAAA,IACT;AACA,YAAQ,MAAM,MAAM;AAAA,MAClB,KAAK;AAAA,MACL,KAAK;AACH,eAAO;AAAA,UACL,WAAW;AAAA,QACb;AAAA,MACF,KAAK;AACH,eAAO;AAAA,UACL,SAAS;AAAA,QACX;AAAA,MACF;AACE,cAAM,IAAI;AAAA,UACR,IAAI,MAAM,IAAI;AAAA,QAChB;AAAA,IACJ;AAAA,EACF;AAAA,EACA,yBAAyB,OAAO,mBAAmB,eAAe,gBAAgB;AAChF,WAAO;AAAA,MACL,GAAG;AAAA,MACH,CAAC,MAAM,IAAI,GAAG;AAAA,QACZ,GAAG,kBAAkB,MAAM,IAAI,KAAK,CAAC;AAAA,QACrC;AAAA,UACE,GAAG;AAAA,UACH,GAAG,KAAK,yBAAyB,OAAO,cAAc;AAAA,QACxD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,sBAAsB,OAAO,UAAU,eAAe,gBAAgB;AACpE,YAAQ,MAAM,MAAM;AAAA,MAClB,KAAK;AACH,eAAO;AAAA,UACL,GAAG;AAAA,UACH,CAAC,MAAM,IAAI,GAAG;AAAA,UACd,iBAAiB,EAAE,GAAG,cAAc;AAAA,QACtC;AAAA,MACF,KAAK;AACH,eAAO;AAAA,UACL,GAAG;AAAA,UACH,gBAAgB,EAAE,GAAG,cAAc;AAAA,QACrC;AAAA,MACF,KAAK;AACH,eAAO;AAAA,UACL,GAAG;AAAA,UACH,gBAAgB,EAAE,GAAG,cAAc;AAAA,QACrC;AAAA,MACF;AACE,eAAO;AAAA,UACL,GAAG;AAAA,UACH,CAAC,MAAM,IAAI,GAAG,EAAE,GAAG,cAAc;AAAA,QACnC;AAAA,IACJ;AAAA,EACF;AAAA,EACA,4BAA4B,mBAAmB,UAAU;AACvD,QAAI,oBAAoB,EAAE,GAAG,SAAS;AACtC,WAAO,KAAK,iBAAiB,EAAE,QAAQ,CAAC,mBAAmB;AACzD,0BAAoB;AAAA,QAClB,GAAG;AAAA,QACH,CAAC,cAAc,GAAG,kBAAkB,cAAc;AAAA,MACpD;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,sBAAsB,OAAO,gBAAgB;AAC3C,yBAAAC;AAAA,MACE,gBAAgB,KAAK,CAAC,SAAS,MAAM,SAAS,IAAI;AAAA,MAClD,OAAO,MAAM,SAAS,aAAa,sIAAsI,uBAAuB,gBAAgB;AAAA,QAC9M;AAAA,MACF,CAAC,oDAAoD,MAAM,IAAI;AAAA,IACjE;AACA,yBAAAA;AAAA,MACE,CAAC,kBAAkB,OAAO,mBAAmB,YAAY,MAAM,QAAQ,cAAc,KAAK,CAAC,eAAe,KAAK,CAAC,gBAAgB,OAAO,gBAAgB,QAAQ;AAAA,MAC/J,0CAA0C,MAAM,IAAI,yDAAyD,MAAM,IAAI,YAAY,MAAM,IAAI;AAAA,IAC/I;AACA,WAAO;AAAA,EACT;AAAA,EACA,mBAAmB,UAAU,UAAU;AACrC,QAAI,oBAAoB,CAAC;AACzB,iBAAAC,QAAO,SAAS,QAAQ,UAAU,CAAC,UAAU;AAC3C,UAAI,CAAC,SAAS,CAAC,MAAM,OAAO;AAC1B;AAAA,MACF;AACA,YAAM,EAAE,UAAU,gBAAgB,GAAG,WAAW,IAAI,MAAM;AAC1D,YAAM,gBAAgB,OAAO,KAAK,UAAU,EAAE,OAAO,CAAC,KAAK,QAAQ;AACjE,YAAI,aAAa,GAAG,KAAK,GAAG,IAAI,WAAW,GAAG;AAC9C,eAAO;AAAA,MACT,GAAG,CAAC,CAAC;AACL,UAAI,EAAE,KAAK,IAAI;AACf,UAAI,OAAO,SAAS,UAAU;AAC5B,eAAO,KAAK,SAAS;AAAA,MACvB,OAAO;AACL,aAAK,sBAAsB,OAAO,cAAc;AAAA,MAClD;AACA,cAAQ,MAAM;AAAA,QACZ,KAAK;AACH,qBAAW,KAAK,mBAAmB,gBAAgB,QAAQ;AAC3D;AAAA,QACF,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,8BAAoB,KAAK;AAAA,YACvB;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF;AACA;AAAA,QACF;AACE,qBAAW,KAAK,sBAAsB,OAAO,UAAU,eAAe,cAAc;AACpF;AAAA,MACJ;AAAA,IACF,CAAC;AACD,WAAO,KAAK,4BAA4B,mBAAmB,QAAQ;AAAA,EACrE;AAAA,EACA,SAAS;AACP,UAAM,EAAE,UAAU,GAAG,MAAM,IAAI,KAAK;AACpC,QAAI,WAAW,EAAE,GAAG,MAAM;AAC1B,QAAI,EAAE,WAAW,IAAI;AACrB,QAAI,UAAU;AACZ,iBAAW,KAAK,mBAAmB,UAAU,QAAQ;AAAA,IACvD;AACA,QAAI,cAAc,EAAE,sBAAsB,aAAa;AACrD,YAAM,OAAO;AACb,mBAAa,IAAI,WAAW,KAAK,SAAS,IAAI;AAC9C,aAAO,SAAS;AAAA,IAClB;AACA,WAAO,aAA6B,aAAAA,QAAO,cAAc,kBAAkB,EAAE,GAAG,UAAU,SAAS,WAAW,MAAM,CAAC,IAAoB,aAAAA,QAAO,cAAc,QAAQ,UAAU,MAAM,CAAC,YAA4B,aAAAA,QAAO,cAAc,kBAAkB,EAAE,GAAG,UAAU,QAAQ,CAAC,CAAC;AAAA,EACrR;AACF,GA9IE,cADWN,KACJ,gBAAe;AAAA,EACpB,OAAO;AAAA,EACP,yBAAyB;AAAA,EACzB,mBAAmB;AACrB,IALWA;", "names": ["invariant", "shallowEqual", "import_react", "React", "React2", "_a", "Component2", "shallowEqual", "Component3", "fastCompare", "invariant", "React3"]}