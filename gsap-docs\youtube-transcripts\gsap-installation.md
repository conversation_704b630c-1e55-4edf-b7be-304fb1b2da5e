# GSAP Installation - YouTube

[Music]
Hey folks. So today we're going to be
covering installation, the necessary
step before all of the fun animation
stuff can start. We provide a few
different installation options. This
isn't because it's complicated to
install GSAP. It's just because there
are a bunch of different tools and
frameworks out there that all have
different requirements. We split this
video up into chapters. So if you know
what you're looking for, like mpm for
example, just jump right ahead to that
spot. If you're not sure how to get
started, just stick with me and we'll
run through it all together. Oh, and if
you're here because you're running into
build errors or something isn't working,
don't you worry. We've got your back. If
this video doesn't help, just pop over
to the forums and we'll give you a hand
over
there. So, before we get started, it's
important to note that Gap's comprised
of a bunch of different files. We can
see how it's split up by looking at the
overview on the main docs page. So, this
section here is the core Gap library.
For most people, this is the only file
that they'll ever need. It contains
everything that you need to make
performant browser friendly animation.
Then we've got some plugins and easers
that allow you to add extra
functionality. Stuff like controlling
animations with scroll, dragging, text
animation, or maybe even morphing SVG
paths. We even have a React package,
which I'll cover in a
bit. Are you going to help us, <PERSON><PERSON>?
That's nice. Cool. So, first up, let's
take a little look at the zip download
on the install page. So, if you go to
our install page and you click on the
big get Gap button, you'll get a zip
file with a bunch of folders in it. So,
the number of files here can be a little
bit confusing at first glance, but don't
worry, you don't need to dig through all
of them to install Gap. Basically, there
are lots of different tools and ways to
build websites. So, we've given you all
different flavors of JavaScript, so
everyone's got what they need. Each of
these folders contain the main Gap file
and then the plug-in files. The minified
files here are what you need if you're
self-hosting the files and using a
script tag. So there's no need for build
tooling. Just pop it into a script tag
and you're good to go. You'll see in the
minified folder you have min.js files
and map files. The min.js files are the
ones that you'll include in your
project. The map files are called source
maps. They connect minified files to the
original authored versions, but you can
totally ignore them if you want. They're
just there in case people need them for
debugging. The ESM folder is the same
stuff, GSAP file and plugins, but these
are ES module files. They're transpiled
down to be compatible with modern build
tools. The UMD folder is the same stuff
again, but in UMD format. So, generally,
these are used for debugging because
they're not minified, so they're human
readable. Basically, different folders,
different formats, but at the end of the
day, it's all just JavaScript. Now that
you've got an overview of the zip file,
let's use the minified GAP library file
to install Gap in a simple little web
page with a script tag.
Script tags are probably the easiest way
to get set up with Gap. In fact,
probably the easiest way to add
JavaScript to a web page in general.
Doesn't require any build tooling. Just
pop in a script tag and you're good to
go. If you're building out a simple web
page without frameworks or build
processes, and you're not sure how to
get Gap in there, the likelihood is that
script tags is what you're after. For
demo purposes today, here's a super
simple little starting point. Here's our
website folder. In our HTML file, we've
got a little box element that we're
going to spin around. So, let's add some
JavaScript. There's a couple of ways to
include JavaScript in a script tag. We
can pop it directly into a web page by
adding it inside a script element like
this. Or we can reference an external
file. So, this is the same JavaScript,
just two different ways to include it.
To animate this little box with Gap, we
need to include the Gap library. And
then we also need to write some of our
own JavaScript in the form of Gap code
to animate the box. So, first let's get
the Gap library from the zip folder that
we downloaded from the install page. So,
we want the minified file. So, we're
going to grab
gap.mmin.js from the minified folder.
And then let's pop this file into our
website folder. If we open this file in
our text editor, you can see all of the
JavaScript that makes up the core gap
library. So, now we're going to
reference this file with a script tag.
We're going to pop this script tag at
the end of the body before the closing
body tag. And then we're going to add a
path to the file in the source
attribute. Perfect. So now we've got gap
included. We can write some of our own
gap code. So let's make a new script
tag. We're going to write our code in
here for demo purposes, but you can
write your code in your own JavaScript
file if you want. And if we refresh this
page animation, hooray. When we animate,
it's really important to make sure that
the elements that we're trying to
animate and the external JavaScript that
we're using are available to us. So
we've popped our gap file at the end of
the body and our gap code after the gap
file for that reason. So our web page
and all of our elements or DOM loads and
then our external JavaScript and then
our GSA code. But to be on the safe
side, let's add our gap code inside a
DOM content loaded event. So now this
code is only going to run after our HTML
documents been completely passed by the
browser. This is super helpful if you're
using a UI based website builder like
web flow or Wix. You might not have
control over where your code goes.
You'll probably just have a JavaScript
panel in a guey somewhere to add your
custom code into. If you use DOM content
loaded, you can be sure that no matter
where the tool puts your JavaScript, it
always waits until your elements are
there and ready to be animated before it
runs the Gap code.
If you're using a modern framework like
React or Vue, or maybe you have some
sort of bundling setup like Webpack or
Parcel, you'll likely be reaching for a
package manager to install your
JavaScript. If we pop over to the
install helper, you can see that there's
a tab for mpm and a tab here for yarn.
They're basically exactly the same. For
mpm, we do mpm install gap. Yarn is yarn
add. So, let's take a look at how to
install gap with mpm over in this little
project. So, we're just using vanillajs
here with a really simple parcel
bundling setup. We're going to copy the
mpm command from the install helper. And
then if we paste this into our terminal,
we can see that mpm's doing stuff.
And and then gap's been added to our
dependencies. Awesome. So, if we poke
into our node modules folder, we can see
gap and all of the plug-in files have
been added. So we've got gap here and
then we've got scroll trigger and some
of the other plugins. So we've got the
gap files and then over here in our
JavaScript file, we've got a little gap
tween set up. But nothing's happening
yet because we haven't imported gap. So
let's import. If we go back to the
install helper and we scroll down, we
can see our import snippets here. So
this is importing the ES module file. ES
modules are pretty standard. Um but
occasionally some frameworks will use
UMD files. So, we have an option here to
import the UMD files from the disk
directory instead. If you're not sure
which to use, just check the docs for
the framework that you're using, but
it's most likely to be the ESM imports.
So, let's copy this and then we'll go
back to our project. We're going to
import the core gap file from the gap
package and save. And then, hooray,
there we go.
Animation. So, let's install one of the
plugins. We already saw that the plugins
are just chilling in our node modules
folder waiting for us. So all we have to
do is import one. So let's pop back to
our install helper. We're going to get
the correct syntax and we're going to
click on scroll trigger and grab this.
So you can see that this has added
another import line and then also a line
here to register the plug-in with the
core gap library. This makes sure our
plugins and the core work nicely
together and it prevents any tree
shaking issues. So let's copy this into
our project and let's use the plugin.
So, we'll add a scroll trigger onto our
box tween here. And then we'll add some
height on the body to scroll into. And
then if we refresh, we've got a lovely
scroll animation. Nice. So, a little
tip, if we're using a lot of plugins, we
might end up with lots of lines of
imports. So, optionally, you can also
import GAP and all of your plugins from
gap/all to keep things a little tidy.
There are lots of different ways to use
WordPress, but one of the most common
ways to include GSAP in WordPress is to
incue your scripts in a functions.php
file. So here we're just adding the main
GAP library. If you're including plugins
2, you'll need to add the plugin and
then pass the main gap file as a
dependency. Then finally, we're going to
incue a file that contains our custom
GAP code. So this is the code that we've
written. This also needs to be passed
the main gap file as a dependency. So,
this is quite a big snippet, but don't
worry about trying to pause the video
and copy it down. If you head over to
gap.com/wordpress, you'll find this
snippet and then some other helpful
resources. Bear in mind that if you're
using a premium theme or something off
the shelf like divvy, it's not advised
to edit the functions.php file. When the
theme updates, it'll get overwritten and
you'll lose all of your changes. So,
you'll either need to create a child
theme or potentially if your theme
allows for it, you might be able to
upload some JavaScript or custom code in
the theme settings. Just check in with
the theme developer or the
documentation. If you're using React, we
have a special hook just for you. I
won't go into it in detail here because
I made a whole other video for that
which I'll link to below, but TLDDR, it
handles animation cleanup for you. You
just drop your Gap animations into the
hook and you're good to go. So to get it
installed, you can add the use gap hook
into the selected options in the install
helper. So scroll down in the mpm tab
and you'll see use gap 2. If we select
this, you'll see that we've now got
install commands for the gap library and
the react package. And then down here
we've got an import for use gap 2. So
make sure to register use gap just like
you would do with your plugins. Here's a
little stack blitz example. So, this is
installing the GSAP package and then
it's installing use gap and then
registering the plugin and then this is
using the hook down here. If you want to
know any more about the hook, make sure
to check out the official video and
check out the docs as well. I really
hope this video has been helpful for you
and that you found that little bit of
information that you are after. If
you're still stuck, please don't
struggle on your own. We're here for
you. No judgment at all. Just pop into
our forums and we'll give you a hand.
That's all I've got for now. So, until
next time, folks. Have fun and happy
tweening.
[Music]