import{r as i,j as e,m as n}from"./animations-Dls6NBzi.js";import{b as x,N as h,L as o}from"./router-DklJj9iW.js";import{H as p}from"./index-DoUPJfA4.js";import{d as g}from"./destinations-BSfjDwhr.js";import{a as u,b as y}from"./useGSAP-KRu8-WuZ.js";import{F as j}from"./MapPinIcon-DxqRjeFE.js";import{F as f}from"./ClockIcon-Bop423fN.js";import{F as N}from"./InformationCircleIcon-DFxq8xut.js";import"./vendor-BtP0CW_r.js";function b({title:r,titleId:t,...l},c){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:c,"aria-labelledby":t},l),r?i.createElement("title",{id:t},r):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M10.5 19.5 3 12m0 0 7.5-7.5M3 12h18"}))}const v=i.forwardRef(b);function w({title:r,titleId:t,...l},c){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:c,"aria-labelledby":t},l),r?i.createElement("title",{id:t},r):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5"}))}const k=i.forwardRef(w);function F({title:r,titleId:t,...l},c){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:c,"aria-labelledby":t},l),r?i.createElement("title",{id:t},r):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M11.48 3.499a.562.562 0 0 1 1.04 0l2.125 5.111a.563.563 0 0 0 .475.345l5.518.442c.499.04.701.663.321.988l-4.204 3.602a.563.563 0 0 0-.182.557l1.285 5.385a.562.562 0 0 1-.84.61l-4.725-2.885a.562.562 0 0 0-.586 0L6.982 20.54a.562.562 0 0 1-.84-.61l1.285-5.386a.562.562 0 0 0-.182-.557l-4.204-3.602a.562.562 0 0 1 .321-.988l5.518-.442a.563.563 0 0 0 .475-.345L11.48 3.5Z"}))}const L=i.forwardRef(F),$=()=>{const{id:r}=x(),t=i.useMemo(()=>g.find(s=>s.id===r),[r]),l=u(),c=y("fadeIn");if(!t)return e.jsx(h,{to:"/destinations",replace:!0});const d=s=>{switch(s){case"easy":return"text-green-600 bg-green-100";case"moderate":return"text-yellow-600 bg-yellow-100";case"challenging":return"text-red-600 bg-red-100";default:return"text-gray-600 bg-gray-100"}},m=s=>{switch(s){case"beach":return"🏖️";case"cultural":return"🏛️";case"nature":return"🌿";case"adventure":return"🏔️";case"city":return"🏙️";default:return"📍"}};return e.jsxs(e.Fragment,{children:[e.jsxs(p,{children:[e.jsxs("title",{children:[t.name," - Sri Lanka Tourist Guide"]}),e.jsx("meta",{name:"description",content:t.description}),e.jsx("meta",{property:"og:title",content:`${t.name} - Sri Lanka Tourist Guide`}),e.jsx("meta",{property:"og:description",content:t.shortDescription})]}),e.jsxs("section",{className:"relative min-h-screen flex items-center",children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-blue-600 to-purple-600"}),e.jsx("div",{className:"absolute inset-0 bg-black bg-opacity-30"}),e.jsxs("div",{ref:l,className:"container-max section-padding relative z-10 text-white",children:[e.jsxs(o,{to:"/destinations",className:"inline-flex items-center mb-8 text-white hover:text-gray-200 transition-colors",children:[e.jsx(v,{className:"h-5 w-5 mr-2"}),"Back to Destinations"]}),e.jsxs("div",{className:"max-w-4xl",children:[e.jsxs("div",{className:"flex items-center gap-4 mb-6",children:[e.jsx("span",{className:"text-4xl",children:m(t.category)}),e.jsx("span",{className:"inline-block px-4 py-2 bg-white bg-opacity-20 rounded-full text-sm font-medium",children:t.category}),e.jsx("span",{className:`inline-block px-4 py-2 rounded-full text-sm font-medium ${d(t.difficulty)}`,children:t.difficulty})]}),e.jsx("h1",{className:"text-4xl md:text-6xl font-bold mb-6",children:t.name}),e.jsx("p",{className:"text-xl md:text-2xl mb-8 opacity-90 leading-relaxed",children:t.shortDescription}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 text-sm",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(j,{className:"h-5 w-5 mr-2"}),t.province," Province"]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(f,{className:"h-5 w-5 mr-2"}),t.duration]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(k,{className:"h-5 w-5 mr-2"}),t.bestTimeToVisit]})]})]})]})]}),e.jsx("section",{ref:c,className:"section-padding bg-white",children:e.jsx("div",{className:"container-max",children:e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-12",children:[e.jsxs("div",{className:"lg:col-span-2",children:[e.jsxs(n.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"mb-12",children:[e.jsxs("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:["About ",t.name]}),e.jsx("p",{className:"text-lg text-gray-600 leading-relaxed",children:t.description})]}),e.jsxs(n.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.2},viewport:{once:!0},className:"mb-12",children:[e.jsx("h3",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Highlights"}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:t.highlights.map((s,a)=>e.jsxs("div",{className:"flex items-start",children:[e.jsx(L,{className:"h-5 w-5 text-yellow-500 mr-3 mt-1 flex-shrink-0"}),e.jsx("span",{className:"text-gray-700",children:s})]},a))})]}),e.jsxs(n.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.4},viewport:{once:!0},className:"mb-12",children:[e.jsx("h3",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Activities"}),e.jsx("div",{className:"flex flex-wrap gap-2",children:t.activities.map((s,a)=>e.jsx("span",{className:"inline-block px-4 py-2 bg-primary-100 text-primary-700 rounded-full text-sm font-medium",children:s},a))})]}),e.jsxs(n.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.6},viewport:{once:!0},className:"mb-12",children:[e.jsx("h3",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Getting There"}),e.jsxs("div",{className:"bg-gray-50 rounded-lg p-6",children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("h4",{className:"font-semibold text-gray-900 mb-2",children:"From Colombo"}),e.jsx("p",{className:"text-gray-600",children:t.transportation.fromColombo})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold text-gray-900 mb-2",children:"Local Transport"}),e.jsx("div",{className:"flex flex-wrap gap-2",children:t.transportation.localTransport.map((s,a)=>e.jsx("span",{className:"inline-block px-3 py-1 bg-white text-gray-700 rounded text-sm",children:s},a))})]})]})]}),e.jsxs(n.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.8},viewport:{once:!0},children:[e.jsx("h3",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Travel Tips"}),e.jsx("div",{className:"space-y-3",children:t.tips.map((s,a)=>e.jsxs("div",{className:"flex items-start",children:[e.jsx(N,{className:"h-5 w-5 text-blue-500 mr-3 mt-1 flex-shrink-0"}),e.jsx("span",{className:"text-gray-700",children:s})]},a))})]})]}),e.jsx("div",{className:"lg:col-span-1",children:e.jsxs("div",{className:"sticky top-8",children:[e.jsxs(n.div,{initial:{opacity:0,x:30},whileInView:{opacity:1,x:0},transition:{duration:.8},viewport:{once:!0},className:"card p-6 mb-8",children:[e.jsx("h3",{className:"text-xl font-bold text-gray-900 mb-4",children:"Quick Info"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("span",{className:"text-sm font-medium text-gray-500",children:"Province"}),e.jsx("p",{className:"text-gray-900",children:t.province})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-sm font-medium text-gray-500",children:"Duration"}),e.jsx("p",{className:"text-gray-900",children:t.duration})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-sm font-medium text-gray-500",children:"Difficulty"}),e.jsx("p",{className:"text-gray-900 capitalize",children:t.difficulty})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-sm font-medium text-gray-500",children:"Best Time"}),e.jsx("p",{className:"text-gray-900",children:t.bestTimeToVisit})]}),t.entryFee&&e.jsxs("div",{children:[e.jsx("span",{className:"text-sm font-medium text-gray-500",children:"Entry Fee"}),e.jsx("p",{className:"text-gray-900",children:t.entryFee})]}),t.openingHours&&e.jsxs("div",{children:[e.jsx("span",{className:"text-sm font-medium text-gray-500",children:"Opening Hours"}),e.jsx("p",{className:"text-gray-900",children:t.openingHours})]})]})]}),e.jsxs(n.div,{initial:{opacity:0,x:30},whileInView:{opacity:1,x:0},transition:{duration:.8,delay:.2},viewport:{once:!0},className:"card p-6 mb-8",children:[e.jsx("h3",{className:"text-xl font-bold text-gray-900 mb-4",children:"Nearby Attractions"}),e.jsx("ul",{className:"space-y-2",children:t.nearbyAttractions.map((s,a)=>e.jsxs("li",{className:"text-gray-700 text-sm",children:["• ",s]},a))})]}),e.jsxs(n.div,{initial:{opacity:0,x:30},whileInView:{opacity:1,x:0},transition:{duration:.8,delay:.4},viewport:{once:!0},className:"card p-6",children:[e.jsx("h3",{className:"text-xl font-bold text-gray-900 mb-4",children:"Accommodation"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-gray-900 mb-2",children:"Budget"}),e.jsx("ul",{className:"text-sm text-gray-600 space-y-1",children:t.accommodation.budget.map((s,a)=>e.jsxs("li",{children:["• ",s]},a))})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-gray-900 mb-2",children:"Mid-Range"}),e.jsx("ul",{className:"text-sm text-gray-600 space-y-1",children:t.accommodation.midRange.map((s,a)=>e.jsxs("li",{children:["• ",s]},a))})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-gray-900 mb-2",children:"Luxury"}),e.jsx("ul",{className:"text-sm text-gray-600 space-y-1",children:t.accommodation.luxury.map((s,a)=>e.jsxs("li",{children:["• ",s]},a))})]})]})]})]})})]})})}),e.jsx("section",{className:"section-padding bg-gradient-to-r from-primary-600 to-purple-600 text-white",children:e.jsx("div",{className:"container-max text-center",children:e.jsxs(n.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},children:[e.jsxs("h2",{className:"text-3xl md:text-4xl font-bold mb-6",children:["Ready to Visit ",t.name,"?"]}),e.jsx("p",{className:"text-xl mb-8 opacity-90",children:"Start planning your trip with our comprehensive travel guides and tips."}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[e.jsx(o,{to:"/travel-tips",className:"bg-white text-primary-600 hover:bg-gray-100 font-medium py-3 px-8 rounded-lg transition-colors",children:"Get Travel Tips"}),e.jsx(o,{to:"/destinations",className:"border-2 border-white text-white hover:bg-white hover:text-primary-600 font-medium py-3 px-8 rounded-lg transition-colors",children:"Explore More Destinations"})]})]})})})]})};export{$ as default};
