import React, { useMemo } from 'react'
import { use<PERSON><PERSON>ms, Link, Navigate } from 'react-router-dom'
import { Helmet } from 'react-helmet-async'
import { motion } from 'framer-motion'
import {
  MapPinIcon,
  ClockIcon,
  CalendarIcon,
  StarIcon,
  ArrowLeftIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline'
import { destinations } from '@/data/destinations'
import { useFadeIn, useScrollTrigger } from '@/hooks/useGSAP'

const DestinationDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>()

  const destination = useMemo(() => {
    return destinations.find(d => d.id === id)
  }, [id])

  const heroRef = useFadeIn()
  const contentRef = useScrollTrigger('fadeIn')

  if (!destination) {
    return <Navigate to="/destinations" replace />
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'text-green-600 bg-green-100'
      case 'moderate': return 'text-yellow-600 bg-yellow-100'
      case 'challenging': return 'text-red-600 bg-red-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'beach': return '🏖️'
      case 'cultural': return '🏛️'
      case 'nature': return '🌿'
      case 'adventure': return '🏔️'
      case 'city': return '🏙️'
      default: return '📍'
    }
  }

  return (
    <>
      <Helmet>
        <title>{destination.name} - Sri Lanka Tourist Guide</title>
        <meta name="description" content={destination.description} />
        <meta property="og:title" content={`${destination.name} - Sri Lanka Tourist Guide`} />
        <meta property="og:description" content={destination.shortDescription} />
      </Helmet>

      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-600 to-purple-600" />
        <div className="absolute inset-0 bg-black bg-opacity-30" />

        <div ref={heroRef} className="container-max section-padding relative z-10 text-white">
          {/* Back Button */}
          <Link
            to="/destinations"
            className="inline-flex items-center mb-8 text-white hover:text-gray-200 transition-colors"
          >
            <ArrowLeftIcon className="h-5 w-5 mr-2" />
            Back to Destinations
          </Link>

          <div className="max-w-4xl">
            <div className="flex items-center gap-4 mb-6">
              <span className="text-4xl">{getCategoryIcon(destination.category)}</span>
              <span className="inline-block px-4 py-2 bg-white bg-opacity-20 rounded-full text-sm font-medium">
                {destination.category}
              </span>
              <span className={`inline-block px-4 py-2 rounded-full text-sm font-medium ${getDifficultyColor(destination.difficulty)}`}>
                {destination.difficulty}
              </span>
            </div>

            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              {destination.name}
            </h1>

            <p className="text-xl md:text-2xl mb-8 opacity-90 leading-relaxed">
              {destination.shortDescription}
            </p>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-sm">
              <div className="flex items-center">
                <MapPinIcon className="h-5 w-5 mr-2" />
                {destination.province} Province
              </div>
              <div className="flex items-center">
                <ClockIcon className="h-5 w-5 mr-2" />
                {destination.duration}
              </div>
              <div className="flex items-center">
                <CalendarIcon className="h-5 w-5 mr-2" />
                {destination.bestTimeToVisit}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section ref={contentRef} className="section-padding bg-white">
        <div className="container-max">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
            {/* Main Content */}
            <div className="lg:col-span-2">
              {/* Description */}
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
                className="mb-12"
              >
                <h2 className="text-3xl font-bold text-gray-900 mb-6">About {destination.name}</h2>
                <p className="text-lg text-gray-600 leading-relaxed">
                  {destination.description}
                </p>
              </motion.div>

              {/* Highlights */}
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                viewport={{ once: true }}
                className="mb-12"
              >
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Highlights</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {destination.highlights.map((highlight, index) => (
                    <div key={index} className="flex items-start">
                      <StarIcon className="h-5 w-5 text-yellow-500 mr-3 mt-1 flex-shrink-0" />
                      <span className="text-gray-700">{highlight}</span>
                    </div>
                  ))}
                </div>
              </motion.div>

              {/* Activities */}
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.4 }}
                viewport={{ once: true }}
                className="mb-12"
              >
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Activities</h3>
                <div className="flex flex-wrap gap-2">
                  {destination.activities.map((activity, index) => (
                    <span
                      key={index}
                      className="inline-block px-4 py-2 bg-primary-100 text-primary-700 rounded-full text-sm font-medium"
                    >
                      {activity}
                    </span>
                  ))}
                </div>
              </motion.div>

              {/* Transportation */}
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.6 }}
                viewport={{ once: true }}
                className="mb-12"
              >
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Getting There</h3>
                <div className="bg-gray-50 rounded-lg p-6">
                  <div className="mb-4">
                    <h4 className="font-semibold text-gray-900 mb-2">From Colombo</h4>
                    <p className="text-gray-600">{destination.transportation.fromColombo}</p>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-2">Local Transport</h4>
                    <div className="flex flex-wrap gap-2">
                      {destination.transportation.localTransport.map((transport, index) => (
                        <span
                          key={index}
                          className="inline-block px-3 py-1 bg-white text-gray-700 rounded text-sm"
                        >
                          {transport}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </motion.div>

              {/* Tips */}
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.8 }}
                viewport={{ once: true }}
              >
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Travel Tips</h3>
                <div className="space-y-3">
                  {destination.tips.map((tip, index) => (
                    <div key={index} className="flex items-start">
                      <InformationCircleIcon className="h-5 w-5 text-blue-500 mr-3 mt-1 flex-shrink-0" />
                      <span className="text-gray-700">{tip}</span>
                    </div>
                  ))}
                </div>
              </motion.div>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <div className="sticky top-8">
                {/* Quick Info */}
                <motion.div
                  initial={{ opacity: 0, x: 30 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8 }}
                  viewport={{ once: true }}
                  className="card p-6 mb-8"
                >
                  <h3 className="text-xl font-bold text-gray-900 mb-4">Quick Info</h3>
                  <div className="space-y-4">
                    <div>
                      <span className="text-sm font-medium text-gray-500">Province</span>
                      <p className="text-gray-900">{destination.province}</p>
                    </div>
                    <div>
                      <span className="text-sm font-medium text-gray-500">Duration</span>
                      <p className="text-gray-900">{destination.duration}</p>
                    </div>
                    <div>
                      <span className="text-sm font-medium text-gray-500">Difficulty</span>
                      <p className="text-gray-900 capitalize">{destination.difficulty}</p>
                    </div>
                    <div>
                      <span className="text-sm font-medium text-gray-500">Best Time</span>
                      <p className="text-gray-900">{destination.bestTimeToVisit}</p>
                    </div>
                    {destination.entryFee && (
                      <div>
                        <span className="text-sm font-medium text-gray-500">Entry Fee</span>
                        <p className="text-gray-900">{destination.entryFee}</p>
                      </div>
                    )}
                    {destination.openingHours && (
                      <div>
                        <span className="text-sm font-medium text-gray-500">Opening Hours</span>
                        <p className="text-gray-900">{destination.openingHours}</p>
                      </div>
                    )}
                  </div>
                </motion.div>

                {/* Nearby Attractions */}
                <motion.div
                  initial={{ opacity: 0, x: 30 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8, delay: 0.2 }}
                  viewport={{ once: true }}
                  className="card p-6 mb-8"
                >
                  <h3 className="text-xl font-bold text-gray-900 mb-4">Nearby Attractions</h3>
                  <ul className="space-y-2">
                    {destination.nearbyAttractions.map((attraction, index) => (
                      <li key={index} className="text-gray-700 text-sm">
                        • {attraction}
                      </li>
                    ))}
                  </ul>
                </motion.div>

                {/* Accommodation */}
                <motion.div
                  initial={{ opacity: 0, x: 30 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8, delay: 0.4 }}
                  viewport={{ once: true }}
                  className="card p-6"
                >
                  <h3 className="text-xl font-bold text-gray-900 mb-4">Accommodation</h3>

                  <div className="space-y-4">
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Budget</h4>
                      <ul className="text-sm text-gray-600 space-y-1">
                        {destination.accommodation.budget.map((hotel, index) => (
                          <li key={index}>• {hotel}</li>
                        ))}
                      </ul>
                    </div>

                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Mid-Range</h4>
                      <ul className="text-sm text-gray-600 space-y-1">
                        {destination.accommodation.midRange.map((hotel, index) => (
                          <li key={index}>• {hotel}</li>
                        ))}
                      </ul>
                    </div>

                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Luxury</h4>
                      <ul className="text-sm text-gray-600 space-y-1">
                        {destination.accommodation.luxury.map((hotel, index) => (
                          <li key={index}>• {hotel}</li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </motion.div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="section-padding bg-gradient-to-r from-primary-600 to-purple-600 text-white">
        <div className="container-max text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Ready to Visit {destination.name}?
            </h2>
            <p className="text-xl mb-8 opacity-90">
              Start planning your trip with our comprehensive travel guides and tips.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                to="/travel-tips"
                className="bg-white text-primary-600 hover:bg-gray-100 font-medium py-3 px-8 rounded-lg transition-colors"
              >
                Get Travel Tips
              </Link>
              <Link
                to="/destinations"
                className="border-2 border-white text-white hover:bg-white hover:text-primary-600 font-medium py-3 px-8 rounded-lg transition-colors"
              >
                Explore More Destinations
              </Link>
            </div>
          </motion.div>
        </div>
      </section>
    </>
  )
}

export default DestinationDetail
