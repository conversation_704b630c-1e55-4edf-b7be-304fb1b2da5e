import React, { useState, useMemo } from 'react'
import { Helmet } from 'react-helmet-async'
import { Link } from 'react-router-dom'
import { motion } from 'framer-motion'
import { MagnifyingGlassIcon, MapPinIcon, ClockIcon } from '@heroicons/react/24/outline'
import { destinations, Destination } from '@/data/destinations'
import { useStaggerChildren } from '@/hooks/useGSAP'
import InteractiveMap from '@/components/InteractiveMap'

const Destinations: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [selectedProvince, setSelectedProvince] = useState<string>('all')

  const destinationsRef = useStaggerChildren('fadeIn', 0.1)

  const categories = ['all', 'beach', 'cultural', 'nature', 'adventure', 'city']
  const provinces = ['all', ...Array.from(new Set(destinations.map(d => d.province)))]

  const filteredDestinations = useMemo(() => {
    return destinations.filter(destination => {
      const matchesSearch = destination.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           destination.description.toLowerCase().includes(searchTerm.toLowerCase())
      const matchesCategory = selectedCategory === 'all' || destination.category === selectedCategory
      const matchesProvince = selectedProvince === 'all' || destination.province === selectedProvince

      return matchesSearch && matchesCategory && matchesProvince
    })
  }, [searchTerm, selectedCategory, selectedProvince])



  return (
    <>
      <Helmet>
        <title>Destinations - Sri Lanka Tourist Guide</title>
        <meta name="description" content="Explore the most beautiful destinations in Sri Lanka, from beaches to mountains to cultural sites." />
      </Helmet>

      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 to-purple-50 section-padding">
        <div className="container-max">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-12"
          >
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
              Discover Amazing <span className="text-gradient">Destinations</span>
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              From ancient cities to pristine beaches, explore the diverse landscapes and
              rich cultural heritage that make Sri Lanka truly special.
            </p>
          </motion.div>

          {/* Search and Filters */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="bg-white rounded-2xl shadow-lg p-6 mb-12"
          >
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Search */}
              <div className="relative">
                <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search destinations..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                />
              </div>

              {/* Category Filter */}
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                aria-label="Filter by category"
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              >
                {categories.map(category => (
                  <option key={category} value={category}>
                    {category === 'all' ? 'All Categories' : category.charAt(0).toUpperCase() + category.slice(1)}
                  </option>
                ))}
              </select>

              {/* Province Filter */}
              <select
                value={selectedProvince}
                onChange={(e) => setSelectedProvince(e.target.value)}
                aria-label="Filter by province"
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              >
                {provinces.map(province => (
                  <option key={province} value={province}>
                    {province === 'all' ? 'All Provinces' : `${province} Province`}
                  </option>
                ))}
              </select>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Interactive Map Section */}
      <section className="section-padding bg-gray-50">
        <div className="container-max">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-8"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Explore Sri Lanka on the Map
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Discover destinations across all 9 provinces of Sri Lanka. Click on markers to learn more about each location.
            </p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <InteractiveMap height="500px" />
          </motion.div>
        </div>
      </section>

      {/* Destinations Grid */}
      <section className="section-padding bg-white">
        <div className="container-max">
          {filteredDestinations.length === 0 ? (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="text-center py-16"
            >
              <div className="text-6xl mb-4">🔍</div>
              <h3 className="text-2xl font-semibold text-gray-900 mb-2">No destinations found</h3>
              <p className="text-gray-600">Try adjusting your search criteria</p>
            </motion.div>
          ) : (
            <>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="flex items-center justify-between mb-8"
              >
                <h2 className="text-2xl font-semibold text-gray-900">
                  {filteredDestinations.length} destination{filteredDestinations.length !== 1 ? 's' : ''} found
                </h2>
                <div className="text-sm text-gray-500">
                  Showing results for {selectedCategory !== 'all' && `${selectedCategory} destinations`}
                  {selectedCategory !== 'all' && selectedProvince !== 'all' && ' in '}
                  {selectedProvince !== 'all' && `${selectedProvince} Province`}
                </div>
              </motion.div>

              <div ref={destinationsRef} className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {filteredDestinations.map((destination) => (
                  <div key={destination.id} className="gsap-fade-in">
                    <DestinationCard destination={destination} />
                  </div>
                ))}
              </div>
            </>
          )}
        </div>
      </section>
    </>
  )
}

// Destination Card Component
interface DestinationCardProps {
  destination: Destination
}

const DestinationCard: React.FC<DestinationCardProps> = ({ destination }) => {
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'text-green-600 bg-green-100'
      case 'moderate': return 'text-yellow-600 bg-yellow-100'
      case 'challenging': return 'text-red-600 bg-red-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'beach': return '🏖️'
      case 'cultural': return '🏛️'
      case 'nature': return '🌿'
      case 'adventure': return '🏔️'
      case 'city': return '🏙️'
      default: return '📍'
    }
  }

  return (
    <motion.div
      whileHover={{ y: -5 }}
      transition={{ duration: 0.3 }}
      className="card overflow-hidden group"
    >
      {/* Image */}
      <div className="relative h-64 bg-gradient-to-br from-blue-400 to-purple-500 overflow-hidden">
        <div className="absolute inset-0 bg-black bg-opacity-20 group-hover:bg-opacity-10 transition-all duration-300" />

        {/* Category Badge */}
        <div className="absolute top-4 left-4">
          <span className="inline-flex items-center px-3 py-1 bg-white bg-opacity-90 rounded-full text-sm font-medium text-gray-900">
            <span className="mr-1">{getCategoryIcon(destination.category)}</span>
            {destination.category}
          </span>
        </div>

        {/* Difficulty Badge */}
        <div className="absolute top-4 right-4">
          <span className={`inline-block px-3 py-1 rounded-full text-xs font-medium ${getDifficultyColor(destination.difficulty)}`}>
            {destination.difficulty}
          </span>
        </div>

        {/* Province */}
        <div className="absolute bottom-4 left-4">
          <span className="inline-flex items-center text-white text-sm">
            <MapPinIcon className="h-4 w-4 mr-1" />
            {destination.province} Province
          </span>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        <h3 className="text-xl font-semibold mb-2 text-gray-900 group-hover:text-primary-600 transition-colors">
          {destination.name}
        </h3>

        <p className="text-gray-600 mb-4 line-clamp-2">
          {destination.shortDescription}
        </p>

        {/* Meta Info */}
        <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
          <div className="flex items-center">
            <ClockIcon className="h-4 w-4 mr-1" />
            {destination.duration}
          </div>
          <div className="text-right">
            Best: {destination.bestTimeToVisit}
          </div>
        </div>

        {/* Highlights */}
        <div className="mb-4">
          <div className="flex flex-wrap gap-1">
            {destination.highlights.slice(0, 2).map((highlight, index) => (
              <span
                key={index}
                className="inline-block px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded"
              >
                {highlight}
              </span>
            ))}
            {destination.highlights.length > 2 && (
              <span className="inline-block px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">
                +{destination.highlights.length - 2} more
              </span>
            )}
          </div>
        </div>

        {/* Action */}
        <Link
          to={`/destinations/${destination.id}`}
          className="block w-full text-center bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-4 rounded-lg transition-colors"
        >
          Explore Destination
        </Link>
      </div>
    </motion.div>
  )
}

export default Destinations
