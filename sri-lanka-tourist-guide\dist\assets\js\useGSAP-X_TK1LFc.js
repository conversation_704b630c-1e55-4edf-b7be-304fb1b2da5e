import{g as n,r as a}from"./animations-Dls6NBzi.js";import{S as i}from"./index-Dv-lgoNZ.js";n.registerPlugin(i);const o={duration:{normal:.6},ease:{smooth:"power2.out",bounce:"back.out(1.7)"}},u=(t,e)=>n.fromTo(t,{opacity:0,y:30},{opacity:1,y:0,duration:o.duration.normal,ease:o.ease.smooth,...e}),m=(t,e)=>n.fromTo(t,{opacity:0,x:-50},{opacity:1,x:0,duration:o.duration.normal,ease:o.ease.smooth,...e}),d=(t,e)=>n.fromTo(t,{opacity:0,x:50},{opacity:1,x:0,duration:o.duration.normal,ease:o.ease.smooth,...e}),g=(t,e)=>n.fromTo(t,{opacity:0,scale:.8},{opacity:1,scale:1,duration:o.duration.normal,ease:o.ease.bounce,...e}),p=(t,e,r)=>i.create({trigger:t,start:"top 80%",end:"bottom 20%",toggleActions:"play none none reverse",...r,animation:e()});n.registerPlugin(i);const h=(t=0)=>{const e=a.useRef(null);return a.useEffect(()=>{e.current&&u(e.current,{delay:t})},[t]),e},x=(t="fadeIn",e)=>{const r=a.useRef(null);return a.useEffect(()=>{if(r.current){const c={fadeIn:()=>u(r.current),slideInLeft:()=>m(r.current),slideInRight:()=>d(r.current),scaleIn:()=>g(r.current)},s=p(r.current,c[t],e);return()=>{s.kill()}}},[t,e]),r},S=(t="fadeIn",e=.1)=>{const r=a.useRef(null);return a.useEffect(()=>{if(r.current){const c=r.current.children;if(c.length>0){const s={fadeIn:{from:{opacity:0,y:30},to:{opacity:1,y:0}},slideInLeft:{from:{opacity:0,x:-50},to:{opacity:1,x:0}},slideInRight:{from:{opacity:0,x:50},to:{opacity:1,x:0}},scaleIn:{from:{opacity:0,scale:.8},to:{opacity:1,scale:1}}},{from:l,to:f}=s[t];n.fromTo(c,l,{...f,duration:.6,ease:"power2.out",stagger:e})}}},[t,e]),r};export{h as a,x as b,S as u};
