import{j as e}from"./animations-Dls6NBzi.js";import{L as t}from"./router-Bcnr4W6T.js";import{H as s}from"./index-Bxt4fWYT.js";import"./vendor-BtP0CW_r.js";const i=()=>e.jsxs(e.Fragment,{children:[e.jsxs(s,{children:[e.jsx("title",{children:"Page Not Found - Sri Lanka Tourist Guide"}),e.jsx("meta",{name:"description",content:"The page you're looking for doesn't exist."})]}),e.jsx("div",{className:"section-padding min-h-screen flex items-center",children:e.jsxs("div",{className:"container-max text-center",children:[e.jsx("h1",{className:"text-6xl font-bold text-gray-900 mb-4",children:"404"}),e.jsx("h2",{className:"text-2xl font-semibold text-gray-700 mb-4",children:"Page Not Found"}),e.jsx("p",{className:"text-lg text-gray-600 mb-8",children:"The page you're looking for doesn't exist or has been moved."}),e.jsx(t,{to:"/",className:"btn-primary",children:"Return Home"})]})})]});export{i as default};
