export interface CulturalSite {
  id: string
  name: string
  description: string
  category: 'temple' | 'historical' | 'festival' | 'tradition' | 'art' | 'cuisine'
  location: string
  significance: string
  bestTimeToVisit: string
  images: string[]
  highlights: string[]
  culturalTips: string[]
}

export interface HistoricalPeriod {
  id: string
  name: string
  period: string
  description: string
  keyEvents: string[]
  culturalImpact: string[]
  monuments: string[]
}

export interface Festival {
  id: string
  name: string
  description: string
  when: string
  where: string
  significance: string
  activities: string[]
  images: string[]
}

export interface Tradition {
  id: string
  name: string
  description: string
  origin: string
  modernPractice: string
  culturalSignificance: string
  examples: string[]
}

export interface CuisineItem {
  id: string
  name: string
  description: string
  type: 'main-dish' | 'snack' | 'dessert' | 'beverage' | 'spice'
  ingredients: string[]
  culturalSignificance: string
  regions: string[]
}

// Historical Timeline
export const historicalPeriods: HistoricalPeriod[] = [
  {
    id: 'prehistoric',
    name: 'Prehistoric Era',
    period: '125,000 - 500 BCE',
    description: 'The earliest human settlements in Sri Lanka, with evidence of Balangoda Man and early hunter-gatherer communities.',
    keyEvents: [
      'Arrival of Balangoda Man (125,000 years ago)',
      'Development of early tools and cave paintings',
      'Transition to agriculture (3000 BCE)',
      'Early irrigation systems'
    ],
    culturalImpact: [
      'Foundation of agricultural practices',
      'Early artistic expressions in caves',
      'Development of irrigation knowledge',
      'Establishment of settled communities'
    ],
    monuments: [
      'Fa Hien Cave',
      'Batadombalena Cave',
      'Pahiyangala Cave'
    ]
  },
  {
    id: 'anuradhapura-kingdom',
    name: 'Anuradhapura Kingdom',
    period: '377 BCE - 1017 CE',
    description: 'The first major Sinhalese kingdom and the golden age of ancient Sri Lankan civilization, marked by the introduction of Buddhism and magnificent architectural achievements.',
    keyEvents: [
      'Founding by King Pandukabhaya (377 BCE)',
      'Introduction of Buddhism by Mahinda (247 BCE)',
      'Planting of Sri Maha Bodhi tree (236 BCE)',
      'Construction of great stupas',
      'Development of hydraulic civilization'
    ],
    culturalImpact: [
      'Establishment of Theravada Buddhism',
      'Development of Sinhalese script and literature',
      'Advanced hydraulic engineering',
      'Monastic education system',
      'Artistic and architectural innovations'
    ],
    monuments: [
      'Sri Maha Bodhi Tree',
      'Ruwanwelisaya Stupa',
      'Jetavanaramaya',
      'Abhayagiri Monastery',
      'Thuparamaya'
    ]
  },
  {
    id: 'polonnaruwa-kingdom',
    name: 'Polonnaruwa Kingdom',
    period: '1055 - 1232 CE',
    description: 'The second ancient capital representing the medieval golden age of Sinhalese civilization with remarkable architectural and artistic achievements.',
    keyEvents: [
      'Establishment as capital by Vijayabahu I (1055)',
      'Reign of Parakramabahu I (1153-1186)',
      'Construction of Parakrama Samudra',
      'Artistic renaissance period',
      'Decline due to invasions'
    ],
    culturalImpact: [
      'Peak of Sinhalese sculpture and architecture',
      'Advanced irrigation and urban planning',
      'Cultural synthesis with South Indian influences',
      'Development of classical Sinhalese literature',
      'Refinement of Buddhist art'
    ],
    monuments: [
      'Gal Vihara rock sculptures',
      'Parakrama Samudra reservoir',
      'Royal Palace complex',
      'Lankatilaka Temple',
      'Rankoth Vehera'
    ]
  }
]

// Major Festivals
export const festivals: Festival[] = [
  {
    id: 'vesak',
    name: 'Vesak Festival',
    description: 'The most important Buddhist festival celebrating the birth, enlightenment, and death of Buddha. The entire country is illuminated with colorful lanterns and decorations.',
    when: 'May (full moon day)',
    where: 'Nationwide',
    significance: 'Commemorates the three most important events in Buddha\'s life',
    activities: [
      'Lantern displays and illuminations',
      'Free food distribution (dansalas)',
      'Religious observances and meditation',
      'Pandol (decorative archways)',
      'Temple visits and offerings'
    ],
    images: [
      '/images/culture/vesak-1.jpg',
      '/images/culture/vesak-2.jpg'
    ]
  },
  {
    id: 'esala-perahera',
    name: 'Kandy Esala Perahera',
    description: 'One of Asia\'s grandest festivals featuring a spectacular procession of elephants, dancers, drummers, and fire performers honoring the Sacred Tooth Relic.',
    when: 'July/August (10 nights)',
    where: 'Kandy',
    significance: 'Honors the Sacred Tooth Relic of Buddha',
    activities: [
      'Elephant processions',
      'Traditional Kandyan dancing',
      'Fire dancing and acrobatics',
      'Drumming performances',
      'Religious ceremonies'
    ],
    images: [
      '/images/culture/perahera-1.jpg',
      '/images/culture/perahera-2.jpg'
    ]
  },
  {
    id: 'sinhala-tamil-new-year',
    name: 'Sinhala and Tamil New Year',
    description: 'The traditional New Year celebrated by both Sinhalese and Tamil communities, marking the end of harvest season and beginning of new agricultural cycle.',
    when: 'April 13-14',
    where: 'Nationwide',
    significance: 'Celebrates new beginnings and family unity',
    activities: [
      'Traditional games and sports',
      'Preparation of traditional sweets',
      'Oil lamp lighting ceremonies',
      'Family gatherings and visits',
      'Astrological observations'
    ],
    images: [
      '/images/culture/new-year-1.jpg',
      '/images/culture/new-year-2.jpg'
    ]
  }
]

// Traditional Arts and Crafts
export const traditions: Tradition[] = [
  {
    id: 'kandyan-dance',
    name: 'Kandyan Dance',
    description: 'The classical dance form of Sri Lanka characterized by rapid movements, acrobatic feats, and elaborate costumes, traditionally performed to honor the gods.',
    origin: 'Ancient Kandyan Kingdom rituals and ceremonies',
    modernPractice: 'Performed at cultural shows, festivals, and special occasions',
    culturalSignificance: 'Preserves ancient religious and cultural traditions',
    examples: [
      'Ves dance (most sacred form)',
      'Naiyandi dance',
      'Uddekki dance',
      'Pantheru dance'
    ]
  },
  {
    id: 'batik-art',
    name: 'Batik Art',
    description: 'Traditional fabric art using wax-resist dyeing technique to create intricate patterns and designs on cloth, often depicting Sri Lankan motifs.',
    origin: 'Influenced by Indonesian and Indian traditions, adapted locally',
    modernPractice: 'Contemporary artists create modern designs while preserving traditional techniques',
    culturalSignificance: 'Represents Sri Lankan artistic heritage and craftsmanship',
    examples: [
      'Traditional elephant motifs',
      'Buddhist symbols',
      'Floral and nature patterns',
      'Contemporary abstract designs'
    ]
  },
  {
    id: 'wood-carving',
    name: 'Traditional Wood Carving',
    description: 'Intricate wood carving art form used in temple decoration, furniture, and architectural elements, showcasing exceptional craftsmanship.',
    origin: 'Ancient temple and palace decoration traditions',
    modernPractice: 'Continues in temple restoration and contemporary furniture making',
    culturalSignificance: 'Preserves ancient artistic techniques and religious symbolism',
    examples: [
      'Temple door carvings',
      'Decorative pillars',
      'Traditional furniture',
      'Masks and sculptures'
    ]
  }
]

// Traditional Cuisine
export const cuisine: CuisineItem[] = [
  {
    id: 'rice-and-curry',
    name: 'Rice and Curry',
    description: 'The staple meal of Sri Lanka consisting of rice served with multiple curry dishes, vegetables, and accompaniments.',
    type: 'main-dish',
    ingredients: ['Rice', 'Various curries', 'Coconut', 'Spices', 'Vegetables'],
    culturalSignificance: 'Central to Sri Lankan food culture and daily life',
    regions: ['Nationwide']
  },
  {
    id: 'hoppers',
    name: 'Hoppers (Appa)',
    description: 'Bowl-shaped pancakes made from fermented rice flour and coconut milk, often served with egg or honey.',
    type: 'main-dish',
    ingredients: ['Rice flour', 'Coconut milk', 'Palm toddy', 'Eggs'],
    culturalSignificance: 'Traditional breakfast dish with Portuguese colonial influences',
    regions: ['Nationwide', 'especially coastal areas']
  },
  {
    id: 'kottu-roti',
    name: 'Kottu Roti',
    description: 'Chopped roti bread stir-fried with vegetables, meat, and spices, creating a rhythmic chopping sound during preparation.',
    type: 'main-dish',
    ingredients: ['Roti bread', 'Vegetables', 'Meat/eggs', 'Spices'],
    culturalSignificance: 'Popular street food representing modern Sri Lankan cuisine',
    regions: ['Nationwide', 'urban areas']
  }
]

export const culturalSites: CulturalSite[] = [
  {
    id: 'temple-of-tooth',
    name: 'Temple of the Sacred Tooth Relic',
    description: 'The most sacred Buddhist temple in Sri Lanka, housing the tooth relic of Buddha and serving as the spiritual center of the nation.',
    category: 'temple',
    location: 'Kandy',
    significance: 'Houses the Sacred Tooth Relic of Buddha, symbol of sovereignty',
    bestTimeToVisit: 'During Pooja ceremonies (5:30 AM, 10:30 AM, 6:30 PM)',
    images: ['/images/culture/tooth-temple-1.jpg'],
    highlights: [
      'Sacred Tooth Relic chamber',
      'Golden roof and architecture',
      'Daily Pooja ceremonies',
      'Museum with royal artifacts'
    ],
    culturalTips: [
      'Dress modestly covering shoulders and knees',
      'Remove shoes before entering',
      'Maintain silence during ceremonies',
      'Photography restrictions in inner chambers'
    ]
  }
]
