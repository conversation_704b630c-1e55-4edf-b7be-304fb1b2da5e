import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'

// Register GSAP plugins
gsap.registerPlugin(ScrollTrigger)

// Animation configurations
export const animationConfig = {
  duration: {
    fast: 0.3,
    normal: 0.6,
    slow: 1.2,
  },
  ease: {
    smooth: 'power2.out',
    bounce: 'back.out(1.7)',
    elastic: 'elastic.out(1, 0.3)',
  },
  stagger: {
    fast: 0.1,
    normal: 0.2,
    slow: 0.3,
  }
}

// Fade in animation
export const fadeIn = (element: string | Element, options?: gsap.TweenVars) => {
  return gsap.fromTo(
    element,
    { opacity: 0, y: 30 },
    {
      opacity: 1,
      y: 0,
      duration: animationConfig.duration.normal,
      ease: animationConfig.ease.smooth,
      ...options,
    }
  )
}

// Slide in from left
export const slideInLeft = (element: string | Element, options?: gsap.TweenVars) => {
  return gsap.fromTo(
    element,
    { opacity: 0, x: -50 },
    {
      opacity: 1,
      x: 0,
      duration: animationConfig.duration.normal,
      ease: animationConfig.ease.smooth,
      ...options,
    }
  )
}

// Slide in from right
export const slideInRight = (element: string | Element, options?: gsap.TweenVars) => {
  return gsap.fromTo(
    element,
    { opacity: 0, x: 50 },
    {
      opacity: 1,
      x: 0,
      duration: animationConfig.duration.normal,
      ease: animationConfig.ease.smooth,
      ...options,
    }
  )
}

// Scale in animation
export const scaleIn = (element: string | Element, options?: gsap.TweenVars) => {
  return gsap.fromTo(
    element,
    { opacity: 0, scale: 0.8 },
    {
      opacity: 1,
      scale: 1,
      duration: animationConfig.duration.normal,
      ease: animationConfig.ease.bounce,
      ...options,
    }
  )
}

// Stagger animation for multiple elements
export const staggerAnimation = (
  elements: string | Element[],
  animation: 'fadeIn' | 'slideInLeft' | 'slideInRight' | 'scaleIn',
  staggerDelay: number = animationConfig.stagger.normal
) => {
  const animationMap = {
    fadeIn: { opacity: 0, y: 30 },
    slideInLeft: { opacity: 0, x: -50 },
    slideInRight: { opacity: 0, x: 50 },
    scaleIn: { opacity: 0, scale: 0.8 },
  }

  const toMap = {
    fadeIn: { opacity: 1, y: 0 },
    slideInLeft: { opacity: 1, x: 0 },
    slideInRight: { opacity: 1, x: 0 },
    scaleIn: { opacity: 1, scale: 1 },
  }

  return gsap.fromTo(
    elements,
    animationMap[animation],
    {
      ...toMap[animation],
      duration: animationConfig.duration.normal,
      ease: animationConfig.ease.smooth,
      stagger: staggerDelay,
    }
  )
}

// Scroll-triggered animations
export const createScrollTrigger = (
  element: string | Element,
  animation: () => gsap.core.Timeline | gsap.core.Tween,
  options?: ScrollTrigger.Vars
) => {
  return ScrollTrigger.create({
    trigger: element,
    start: 'top 80%',
    end: 'bottom 20%',
    toggleActions: 'play none none reverse',
    ...options,
    animation: animation(),
  })
}

// Parallax effect
export const createParallax = (element: string | Element, speed: number = 0.5) => {
  return gsap.to(element, {
    yPercent: -50 * speed,
    ease: 'none',
    scrollTrigger: {
      trigger: element,
      start: 'top bottom',
      end: 'bottom top',
      scrub: true,
    },
  })
}

// Text reveal animation
export const textReveal = (element: string | Element, options?: gsap.TweenVars) => {
  return gsap.fromTo(
    element,
    { 
      opacity: 0,
      y: 100,
      skewY: 7,
    },
    {
      opacity: 1,
      y: 0,
      skewY: 0,
      duration: animationConfig.duration.slow,
      ease: animationConfig.ease.smooth,
      ...options,
    }
  )
}

// Hover animations
export const createHoverAnimation = (element: string | Element) => {
  const el = typeof element === 'string' ? document.querySelector(element) : element
  if (!el) return

  el.addEventListener('mouseenter', () => {
    gsap.to(el, {
      scale: 1.05,
      duration: animationConfig.duration.fast,
      ease: animationConfig.ease.smooth,
    })
  })

  el.addEventListener('mouseleave', () => {
    gsap.to(el, {
      scale: 1,
      duration: animationConfig.duration.fast,
      ease: animationConfig.ease.smooth,
    })
  })
}

// Loading animation
export const createLoadingAnimation = (element: string | Element) => {
  return gsap.to(element, {
    rotation: 360,
    duration: 1,
    ease: 'none',
    repeat: -1,
  })
}

// Page transition
export const pageTransition = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 },
  exit: { opacity: 0, y: -20 },
  transition: { duration: 0.5, ease: 'easeInOut' }
}

// Cleanup function
export const cleanupAnimations = () => {
  ScrollTrigger.getAll().forEach(trigger => trigger.kill())
  gsap.killTweensOf('*')
}
