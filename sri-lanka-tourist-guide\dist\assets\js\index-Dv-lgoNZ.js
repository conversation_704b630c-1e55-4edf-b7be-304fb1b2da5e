const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/js/Home-YUC6wHC2.js","assets/js/animations-Dls6NBzi.js","assets/js/vendor-BtP0CW_r.js","assets/js/router-DklJj9iW.js","assets/js/useGSAP-X_TK1LFc.js","assets/js/destinations-CZhMEJGZ.js","assets/js/Destinations-DQD4JVHA.js","assets/js/MagnifyingGlassIcon-DvDOtkAW.js","assets/js/MapPinIcon-DxqRjeFE.js","assets/js/ClockIcon-Bop423fN.js","assets/js/DestinationDetail-CZutERg6.js","assets/js/InformationCircleIcon-DFxq8xut.js","assets/js/Activities-BZeeRuUO.js","assets/js/CurrencyDollarIcon-BeUMafu9.js","assets/js/Culture-fhcqi9hV.js","assets/js/AnimatedSection-DtEgkEn2.js","assets/js/TravelTips-D7-Oa4NF.js","assets/js/Contact-B6OcraIi.js","assets/js/NotFound-Dm9N144W.js"])))=>i.map(i=>d[i]);
var yo=Object.defineProperty;var xo=(i,e,n)=>e in i?yo(i,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):i[e]=n;var Et=(i,e,n)=>xo(i,typeof e!="symbol"?e+"":e,n);import{r as H,a as xe,j as p,m as me,g as Tn,A as fi}from"./animations-Dls6NBzi.js";import{a as bo,g as Cn}from"./vendor-BtP0CW_r.js";import{u as wo,L as Jt,R as To,a as It,B as Co}from"./router-DklJj9iW.js";(function(){const e=document.createElement("link").relList;if(e&&e.supports&&e.supports("modulepreload"))return;for(const r of document.querySelectorAll('link[rel="modulepreload"]'))t(r);new MutationObserver(r=>{for(const o of r)if(o.type==="childList")for(const a of o.addedNodes)a.tagName==="LINK"&&a.rel==="modulepreload"&&t(a)}).observe(document,{childList:!0,subtree:!0});function n(r){const o={};return r.integrity&&(o.integrity=r.integrity),r.referrerPolicy&&(o.referrerPolicy=r.referrerPolicy),r.crossOrigin==="use-credentials"?o.credentials="include":r.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function t(r){if(r.ep)return;r.ep=!0;const o=n(r);fetch(r.href,o)}})();var Kr={},di;function Eo(){if(di)return Kr;di=1;var i=bo();return Kr.createRoot=i.createRoot,Kr.hydrateRoot=i.hydrateRoot,Kr}var So=Eo();const ko=Cn(So);var kn,pi;function Ao(){if(pi)return kn;pi=1;var i=typeof Element<"u",e=typeof Map=="function",n=typeof Set=="function",t=typeof ArrayBuffer=="function"&&!!ArrayBuffer.isView;function r(o,a){if(o===a)return!0;if(o&&a&&typeof o=="object"&&typeof a=="object"){if(o.constructor!==a.constructor)return!1;var s,c,f;if(Array.isArray(o)){if(s=o.length,s!=a.length)return!1;for(c=s;c--!==0;)if(!r(o[c],a[c]))return!1;return!0}var h;if(e&&o instanceof Map&&a instanceof Map){if(o.size!==a.size)return!1;for(h=o.entries();!(c=h.next()).done;)if(!a.has(c.value[0]))return!1;for(h=o.entries();!(c=h.next()).done;)if(!r(c.value[1],a.get(c.value[0])))return!1;return!0}if(n&&o instanceof Set&&a instanceof Set){if(o.size!==a.size)return!1;for(h=o.entries();!(c=h.next()).done;)if(!a.has(c.value[0]))return!1;return!0}if(t&&ArrayBuffer.isView(o)&&ArrayBuffer.isView(a)){if(s=o.length,s!=a.length)return!1;for(c=s;c--!==0;)if(o[c]!==a[c])return!1;return!0}if(o.constructor===RegExp)return o.source===a.source&&o.flags===a.flags;if(o.valueOf!==Object.prototype.valueOf&&typeof o.valueOf=="function"&&typeof a.valueOf=="function")return o.valueOf()===a.valueOf();if(o.toString!==Object.prototype.toString&&typeof o.toString=="function"&&typeof a.toString=="function")return o.toString()===a.toString();if(f=Object.keys(o),s=f.length,s!==Object.keys(a).length)return!1;for(c=s;c--!==0;)if(!Object.prototype.hasOwnProperty.call(a,f[c]))return!1;if(i&&o instanceof Element)return!1;for(c=s;c--!==0;)if(!((f[c]==="_owner"||f[c]==="__v"||f[c]==="__o")&&o.$$typeof)&&!r(o[f[c]],a[f[c]]))return!1;return!0}return o!==o&&a!==a}return kn=function(a,s){try{return r(a,s)}catch(c){if((c.message||"").match(/stack|recursion/i))return console.warn("react-fast-compare cannot handle circular refs"),!1;throw c}},kn}var Oo=Ao();const Po=Cn(Oo);var An,hi;function Ro(){if(hi)return An;hi=1;var i=function(e,n,t,r,o,a,s,c){if(!e){var f;if(n===void 0)f=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var h=[t,r,o,a,s,c],v=0;f=new Error(n.replace(/%s/g,function(){return h[v++]})),f.name="Invariant Violation"}throw f.framesToPop=1,f}};return An=i,An}var Mo=Ro();const gi=Cn(Mo);var On,mi;function Do(){return mi||(mi=1,On=function(e,n,t,r){var o=t?t.call(r,e,n):void 0;if(o!==void 0)return!!o;if(e===n)return!0;if(typeof e!="object"||!e||typeof n!="object"||!n)return!1;var a=Object.keys(e),s=Object.keys(n);if(a.length!==s.length)return!1;for(var c=Object.prototype.hasOwnProperty.bind(n),f=0;f<a.length;f++){var h=a[f];if(!c(h))return!1;var v=e[h],g=n[h];if(o=t?t.call(r,v,g,h):void 0,o===!1||o===void 0&&v!==g)return!1}return!0}),On}var jo=Do();const Lo=Cn(jo);var zi=(i=>(i.BASE="base",i.BODY="body",i.HEAD="head",i.HTML="html",i.LINK="link",i.META="meta",i.NOSCRIPT="noscript",i.SCRIPT="script",i.STYLE="style",i.TITLE="title",i.FRAGMENT="Symbol(react.fragment)",i))(zi||{}),Pn={link:{rel:["amphtml","canonical","alternate"]},script:{type:["application/ld+json"]},meta:{charset:"",name:["generator","robots","description"],property:["og:type","og:title","og:url","og:image","og:image:alt","og:description","twitter:url","twitter:title","twitter:description","twitter:image","twitter:image:alt","twitter:card","twitter:site"]}},_i=Object.values(zi),ti={accesskey:"accessKey",charset:"charSet",class:"className",contenteditable:"contentEditable",contextmenu:"contextMenu","http-equiv":"httpEquiv",itemprop:"itemProp",tabindex:"tabIndex"},Io=Object.entries(ti).reduce((i,[e,n])=>(i[n]=e,i),{}),ft="data-rh",_r={DEFAULT_TITLE:"defaultTitle",DEFER:"defer",ENCODE_SPECIAL_CHARACTERS:"encodeSpecialCharacters",ON_CHANGE_CLIENT_STATE:"onChangeClientState",TITLE_TEMPLATE:"titleTemplate",PRIORITIZE_SEO_TAGS:"prioritizeSeoTags"},vr=(i,e)=>{for(let n=i.length-1;n>=0;n-=1){const t=i[n];if(Object.prototype.hasOwnProperty.call(t,e))return t[e]}return null},No=i=>{let e=vr(i,"title");const n=vr(i,_r.TITLE_TEMPLATE);if(Array.isArray(e)&&(e=e.join("")),n&&e)return n.replace(/%s/g,()=>e);const t=vr(i,_r.DEFAULT_TITLE);return e||t||void 0},Ho=i=>vr(i,_r.ON_CHANGE_CLIENT_STATE)||(()=>{}),Rn=(i,e)=>e.filter(n=>typeof n[i]<"u").map(n=>n[i]).reduce((n,t)=>({...n,...t}),{}),Fo=(i,e)=>e.filter(n=>typeof n.base<"u").map(n=>n.base).reverse().reduce((n,t)=>{if(!n.length){const r=Object.keys(t);for(let o=0;o<r.length;o+=1){const s=r[o].toLowerCase();if(i.indexOf(s)!==-1&&t[s])return n.concat(t)}}return n},[]),$o=i=>console&&typeof console.warn=="function"&&console.warn(i),kr=(i,e,n)=>{const t={};return n.filter(r=>Array.isArray(r[i])?!0:(typeof r[i]<"u"&&$o(`Helmet: ${i} should be of type "Array". Instead found type "${typeof r[i]}"`),!1)).map(r=>r[i]).reverse().reduce((r,o)=>{const a={};o.filter(c=>{let f;const h=Object.keys(c);for(let g=0;g<h.length;g+=1){const d=h[g],y=d.toLowerCase();e.indexOf(y)!==-1&&!(f==="rel"&&c[f].toLowerCase()==="canonical")&&!(y==="rel"&&c[y].toLowerCase()==="stylesheet")&&(f=y),e.indexOf(d)!==-1&&(d==="innerHTML"||d==="cssText"||d==="itemprop")&&(f=d)}if(!f||!c[f])return!1;const v=c[f].toLowerCase();return t[f]||(t[f]={}),a[f]||(a[f]={}),t[f][v]?!1:(a[f][v]=!0,!0)}).reverse().forEach(c=>r.push(c));const s=Object.keys(a);for(let c=0;c<s.length;c+=1){const f=s[c],h={...t[f],...a[f]};t[f]=h}return r},[]).reverse()},zo=(i,e)=>{if(Array.isArray(i)&&i.length){for(let n=0;n<i.length;n+=1)if(i[n][e])return!0}return!1},Bo=i=>({baseTag:Fo(["href"],i),bodyAttributes:Rn("bodyAttributes",i),defer:vr(i,_r.DEFER),encode:vr(i,_r.ENCODE_SPECIAL_CHARACTERS),htmlAttributes:Rn("htmlAttributes",i),linkTags:kr("link",["rel","href"],i),metaTags:kr("meta",["name","charset","http-equiv","property","itemprop"],i),noscriptTags:kr("noscript",["innerHTML"],i),onChangeClientState:Ho(i),scriptTags:kr("script",["src","innerHTML"],i),styleTags:kr("style",["cssText"],i),title:No(i),titleAttributes:Rn("titleAttributes",i),prioritizeSeoTags:zo(i,_r.PRIORITIZE_SEO_TAGS)}),Bi=i=>Array.isArray(i)?i.join(""):i,Yo=(i,e)=>{const n=Object.keys(i);for(let t=0;t<n.length;t+=1)if(e[n[t]]&&e[n[t]].includes(i[n[t]]))return!0;return!1},Mn=(i,e)=>Array.isArray(i)?i.reduce((n,t)=>(Yo(t,e)?n.priority.push(t):n.default.push(t),n),{priority:[],default:[]}):{default:i,priority:[]},vi=(i,e)=>({...i,[e]:void 0}),Xo=["noscript","script","style"],zn=(i,e=!0)=>e===!1?String(i):String(i).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#x27;"),Yi=i=>Object.keys(i).reduce((e,n)=>{const t=typeof i[n]<"u"?`${n}="${i[n]}"`:`${n}`;return e?`${e} ${t}`:t},""),Vo=(i,e,n,t)=>{const r=Yi(n),o=Bi(e);return r?`<${i} ${ft}="true" ${r}>${zn(o,t)}</${i}>`:`<${i} ${ft}="true">${zn(o,t)}</${i}>`},qo=(i,e,n=!0)=>e.reduce((t,r)=>{const o=r,a=Object.keys(o).filter(f=>!(f==="innerHTML"||f==="cssText")).reduce((f,h)=>{const v=typeof o[h]>"u"?h:`${h}="${zn(o[h],n)}"`;return f?`${f} ${v}`:v},""),s=o.innerHTML||o.cssText||"",c=Xo.indexOf(i)===-1;return`${t}<${i} ${ft}="true" ${a}${c?"/>":`>${s}</${i}>`}`},""),Xi=(i,e={})=>Object.keys(i).reduce((n,t)=>{const r=ti[t];return n[r||t]=i[t],n},e),Uo=(i,e,n)=>{const t={key:e,[ft]:!0},r=Xi(n,t);return[xe.createElement("title",r,e)]},fn=(i,e)=>e.map((n,t)=>{const r={key:t,[ft]:!0};return Object.keys(n).forEach(o=>{const s=ti[o]||o;if(s==="innerHTML"||s==="cssText"){const c=n.innerHTML||n.cssText;r.dangerouslySetInnerHTML={__html:c}}else r[s]=n[o]}),xe.createElement(i,r)}),tt=(i,e,n=!0)=>{switch(i){case"title":return{toComponent:()=>Uo(i,e.title,e.titleAttributes),toString:()=>Vo(i,e.title,e.titleAttributes,n)};case"bodyAttributes":case"htmlAttributes":return{toComponent:()=>Xi(e),toString:()=>Yi(e)};default:return{toComponent:()=>fn(i,e),toString:()=>qo(i,e,n)}}},Wo=({metaTags:i,linkTags:e,scriptTags:n,encode:t})=>{const r=Mn(i,Pn.meta),o=Mn(e,Pn.link),a=Mn(n,Pn.script);return{priorityMethods:{toComponent:()=>[...fn("meta",r.priority),...fn("link",o.priority),...fn("script",a.priority)],toString:()=>`${tt("meta",r.priority,t)} ${tt("link",o.priority,t)} ${tt("script",a.priority,t)}`},metaTags:r.default,linkTags:o.default,scriptTags:a.default}},Go=i=>{const{baseTag:e,bodyAttributes:n,encode:t=!0,htmlAttributes:r,noscriptTags:o,styleTags:a,title:s="",titleAttributes:c,prioritizeSeoTags:f}=i;let{linkTags:h,metaTags:v,scriptTags:g}=i,d={toComponent:()=>{},toString:()=>""};return f&&({priorityMethods:d,linkTags:h,metaTags:v,scriptTags:g}=Wo(i)),{priority:d,base:tt("base",e,t),bodyAttributes:tt("bodyAttributes",n,t),htmlAttributes:tt("htmlAttributes",r,t),link:tt("link",h,t),meta:tt("meta",v,t),noscript:tt("noscript",o,t),script:tt("script",g,t),style:tt("style",a,t),title:tt("title",{title:s,titleAttributes:c},t)}},Bn=Go,Zr=[],Vi=!!(typeof window<"u"&&window.document&&window.document.createElement),Yn=class{constructor(i,e){Et(this,"instances",[]);Et(this,"canUseDOM",Vi);Et(this,"context");Et(this,"value",{setHelmet:i=>{this.context.helmet=i},helmetInstances:{get:()=>this.canUseDOM?Zr:this.instances,add:i=>{(this.canUseDOM?Zr:this.instances).push(i)},remove:i=>{const e=(this.canUseDOM?Zr:this.instances).indexOf(i);(this.canUseDOM?Zr:this.instances).splice(e,1)}}});this.context=i,this.canUseDOM=e||!1,e||(i.helmet=Bn({baseTag:[],bodyAttributes:{},htmlAttributes:{},linkTags:[],metaTags:[],noscriptTags:[],scriptTags:[],styleTags:[],title:"",titleAttributes:{}}))}},Ko={},qi=xe.createContext(Ko),tr,Ui=(tr=class extends H.Component{constructor(n){super(n);Et(this,"helmetData");this.helmetData=new Yn(this.props.context||{},tr.canUseDOM)}render(){return xe.createElement(qi.Provider,{value:this.helmetData.value},this.props.children)}},Et(tr,"canUseDOM",Vi),tr),fr=(i,e)=>{const n=document.head||document.querySelector("head"),t=n.querySelectorAll(`${i}[${ft}]`),r=[].slice.call(t),o=[];let a;return e&&e.length&&e.forEach(s=>{const c=document.createElement(i);for(const f in s)if(Object.prototype.hasOwnProperty.call(s,f))if(f==="innerHTML")c.innerHTML=s.innerHTML;else if(f==="cssText")c.styleSheet?c.styleSheet.cssText=s.cssText:c.appendChild(document.createTextNode(s.cssText));else{const h=f,v=typeof s[h]>"u"?"":s[h];c.setAttribute(f,v)}c.setAttribute(ft,"true"),r.some((f,h)=>(a=h,c.isEqualNode(f)))?r.splice(a,1):o.push(c)}),r.forEach(s=>{var c;return(c=s.parentNode)==null?void 0:c.removeChild(s)}),o.forEach(s=>n.appendChild(s)),{oldTags:r,newTags:o}},Xn=(i,e)=>{const n=document.getElementsByTagName(i)[0];if(!n)return;const t=n.getAttribute(ft),r=t?t.split(","):[],o=[...r],a=Object.keys(e);for(const s of a){const c=e[s]||"";n.getAttribute(s)!==c&&n.setAttribute(s,c),r.indexOf(s)===-1&&r.push(s);const f=o.indexOf(s);f!==-1&&o.splice(f,1)}for(let s=o.length-1;s>=0;s-=1)n.removeAttribute(o[s]);r.length===o.length?n.removeAttribute(ft):n.getAttribute(ft)!==a.join(",")&&n.setAttribute(ft,a.join(","))},Zo=(i,e)=>{typeof i<"u"&&document.title!==i&&(document.title=Bi(i)),Xn("title",e)},yi=(i,e)=>{const{baseTag:n,bodyAttributes:t,htmlAttributes:r,linkTags:o,metaTags:a,noscriptTags:s,onChangeClientState:c,scriptTags:f,styleTags:h,title:v,titleAttributes:g}=i;Xn("body",t),Xn("html",r),Zo(v,g);const d={baseTag:fr("base",n),linkTags:fr("link",o),metaTags:fr("meta",a),noscriptTags:fr("noscript",s),scriptTags:fr("script",f),styleTags:fr("style",h)},y={},Y={};Object.keys(d).forEach(I=>{const{newTags:W,oldTags:X}=d[I];W.length&&(y[I]=W),X.length&&(Y[I]=d[I].oldTags)}),e&&e(),c(i,y,Y)},Ar=null,Qo=i=>{Ar&&cancelAnimationFrame(Ar),i.defer?Ar=requestAnimationFrame(()=>{yi(i,()=>{Ar=null})}):(yi(i),Ar=null)},Jo=Qo,xi=class extends H.Component{constructor(){super(...arguments);Et(this,"rendered",!1)}shouldComponentUpdate(e){return!Lo(e,this.props)}componentDidUpdate(){this.emitChange()}componentWillUnmount(){const{helmetInstances:e}=this.props.context;e.remove(this),this.emitChange()}emitChange(){const{helmetInstances:e,setHelmet:n}=this.props.context;let t=null;const r=Bo(e.get().map(o=>{const a={...o.props};return delete a.context,a}));Ui.canUseDOM?Jo(r):Bn&&(t=Bn(r)),n(t)}init(){if(this.rendered)return;this.rendered=!0;const{helmetInstances:e}=this.props.context;e.add(this),this.emitChange()}render(){return this.init(),null}},$n,Ks=($n=class extends H.Component{shouldComponentUpdate(i){return!Po(vi(this.props,"helmetData"),vi(i,"helmetData"))}mapNestedChildrenToProps(i,e){if(!e)return null;switch(i.type){case"script":case"noscript":return{innerHTML:e};case"style":return{cssText:e};default:throw new Error(`<${i.type} /> elements are self-closing and can not contain children. Refer to our API for more information.`)}}flattenArrayTypeChildren(i,e,n,t){return{...e,[i.type]:[...e[i.type]||[],{...n,...this.mapNestedChildrenToProps(i,t)}]}}mapObjectTypeChildren(i,e,n,t){switch(i.type){case"title":return{...e,[i.type]:t,titleAttributes:{...n}};case"body":return{...e,bodyAttributes:{...n}};case"html":return{...e,htmlAttributes:{...n}};default:return{...e,[i.type]:{...n}}}}mapArrayTypeChildrenToProps(i,e){let n={...e};return Object.keys(i).forEach(t=>{n={...n,[t]:i[t]}}),n}warnOnInvalidChildren(i,e){return gi(_i.some(n=>i.type===n),typeof i.type=="function"?"You may be attempting to nest <Helmet> components within each other, which is not allowed. Refer to our API for more information.":`Only elements types ${_i.join(", ")} are allowed. Helmet does not support rendering <${i.type}> elements. Refer to our API for more information.`),gi(!e||typeof e=="string"||Array.isArray(e)&&!e.some(n=>typeof n!="string"),`Helmet expects a string as a child of <${i.type}>. Did you forget to wrap your children in braces? ( <${i.type}>{\`\`}</${i.type}> ) Refer to our API for more information.`),!0}mapChildrenToProps(i,e){let n={};return xe.Children.forEach(i,t=>{if(!t||!t.props)return;const{children:r,...o}=t.props,a=Object.keys(o).reduce((c,f)=>(c[Io[f]||f]=o[f],c),{});let{type:s}=t;switch(typeof s=="symbol"?s=s.toString():this.warnOnInvalidChildren(t,r),s){case"Symbol(react.fragment)":e=this.mapChildrenToProps(r,e);break;case"link":case"meta":case"noscript":case"script":case"style":n=this.flattenArrayTypeChildren(t,n,a,r);break;default:e=this.mapObjectTypeChildren(t,e,a,r);break}}),this.mapArrayTypeChildrenToProps(n,e)}render(){const{children:i,...e}=this.props;let n={...e},{helmetData:t}=e;if(i&&(n=this.mapChildrenToProps(i,n)),t&&!(t instanceof Yn)){const r=t;t=new Yn(r.context,!0),delete n.helmetData}return t?xe.createElement(xi,{...n,context:t.value}):xe.createElement(qi.Consumer,null,r=>xe.createElement(xi,{...n,context:r}))}},Et($n,"defaultProps",{defer:!0,encodeSpecialCharacters:!0,prioritizeSeoTags:!1}),$n);const es="modulepreload",ts=function(i){return"/"+i},bi={},Xt=function(e,n,t){let r=Promise.resolve();if(n&&n.length>0){let a=function(f){return Promise.all(f.map(h=>Promise.resolve(h).then(v=>({status:"fulfilled",value:v}),v=>({status:"rejected",reason:v}))))};document.getElementsByTagName("link");const s=document.querySelector("meta[property=csp-nonce]"),c=(s==null?void 0:s.nonce)||(s==null?void 0:s.getAttribute("nonce"));r=a(n.map(f=>{if(f=ts(f),f in bi)return;bi[f]=!0;const h=f.endsWith(".css"),v=h?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${f}"]${v}`))return;const g=document.createElement("link");if(g.rel=h?"stylesheet":es,h||(g.as="script"),g.crossOrigin="",g.href=f,c&&g.setAttribute("nonce",c),document.head.appendChild(g),h)return new Promise((d,y)=>{g.addEventListener("load",d),g.addEventListener("error",()=>y(new Error(`Unable to preload CSS for ${f}`)))})}))}function o(a){const s=new Event("vite:preloadError",{cancelable:!0});if(s.payload=a,window.dispatchEvent(s),!s.defaultPrevented)throw a}return r.then(a=>{for(const s of a||[])s.status==="rejected"&&o(s.reason);return e().catch(o)})};function rs({title:i,titleId:e,...n},t){return H.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":e},n),i?H.createElement("title",{id:e},i):null,H.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4.5 10.5 12 3m0 0 7.5 7.5M12 3v18"}))}const wi=H.forwardRef(rs);function ns({title:i,titleId:e,...n},t){return H.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":e},n),i?H.createElement("title",{id:e},i):null,H.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"}))}const is=H.forwardRef(ns);function os({title:i,titleId:e,...n},t){return H.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":e},n),i?H.createElement("title",{id:e},i):null,H.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 12.76c0 1.6 1.123 2.994 2.707 3.227 1.087.16 2.185.283 3.293.369V21l4.076-4.076a1.526 1.526 0 0 1 1.037-.443 48.282 48.282 0 0 0 5.68-.494c1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0 0 12 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018Z"}))}const ss=H.forwardRef(os);function as({title:i,titleId:e,...n},t){return H.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":e},n),i?H.createElement("title",{id:e},i):null,H.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 6.75V15m6-6v8.25m.503 3.498 4.875-2.437c.381-.19.622-.58.622-1.006V4.82c0-.836-.88-1.38-1.628-1.006l-3.869 1.934c-.317.159-.69.159-1.006 0L9.503 3.252a1.125 1.125 0 0 0-1.006 0L3.622 5.689C3.24 5.88 3 6.27 3 6.695V19.18c0 .836.88 1.38 1.628 1.006l3.869-1.934c.317-.159.69-.159 1.006 0l4.994 2.497c.317.158.69.158 1.006 0Z"}))}const ls=H.forwardRef(as);function cs({title:i,titleId:e,...n},t){return H.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":e},n),i?H.createElement("title",{id:e},i):null,H.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 18 18 6M6 6l12 12"}))}const us=H.forwardRef(cs),fs=()=>{const[i,e]=H.useState(!1),n=wo(),t=[{name:"Home",href:"/"},{name:"Destinations",href:"/destinations"},{name:"Activities",href:"/activities"},{name:"Culture",href:"/culture"},{name:"Travel Tips",href:"/travel-tips"},{name:"Contact",href:"/contact"}],r=o=>n.pathname===o;return p.jsx("header",{className:"bg-white shadow-lg sticky top-0 z-50",children:p.jsxs("nav",{className:"container-max",children:[p.jsxs("div",{className:"flex justify-between items-center py-4",children:[p.jsx(Jt,{to:"/",className:"flex items-center space-x-2",children:p.jsx(me.div,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"text-2xl font-bold text-gradient",children:"Sri Lanka Guide"})}),p.jsx("div",{className:"hidden md:flex space-x-8",children:t.map(o=>p.jsxs(Jt,{to:o.href,className:`relative px-3 py-2 text-sm font-medium transition-colors duration-200 ${r(o.href)?"text-primary-600":"text-gray-700 hover:text-primary-600"}`,children:[o.name,r(o.href)&&p.jsx(me.div,{layoutId:"activeTab",className:"absolute bottom-0 left-0 right-0 h-0.5 bg-primary-600",initial:!1,transition:{type:"spring",stiffness:500,damping:30}})]},o.name))}),p.jsx("div",{className:"md:hidden",children:p.jsx("button",{onClick:()=>e(!i),className:"p-2 rounded-md text-gray-700 hover:text-primary-600 hover:bg-gray-100",children:i?p.jsx(us,{className:"h-6 w-6"}):p.jsx(is,{className:"h-6 w-6"})})})]}),i&&p.jsx(me.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},className:"md:hidden border-t border-gray-200",children:p.jsx("div",{className:"py-4 space-y-2",children:t.map(o=>p.jsx(Jt,{to:o.href,onClick:()=>e(!1),className:`block px-4 py-2 text-sm font-medium rounded-md transition-colors duration-200 ${r(o.href)?"text-primary-600 bg-primary-50":"text-gray-700 hover:text-primary-600 hover:bg-gray-100"}`,children:o.name},o.name))})})]})})},ds=()=>p.jsx("footer",{className:"bg-gray-900 text-white",children:p.jsxs("div",{className:"container-max section-padding",children:[p.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[p.jsxs("div",{className:"col-span-1 md:col-span-2",children:[p.jsx("h3",{className:"text-2xl font-bold text-gradient mb-4",children:"Sri Lanka Guide"}),p.jsx("p",{className:"text-gray-300 mb-4",children:"Discover the beauty and wonder of Sri Lanka with our comprehensive tourist guide. From pristine beaches to ancient temples, we help you explore the Pearl of the Indian Ocean."}),p.jsxs("p",{className:"text-sm text-gray-400",children:["Powered by ",p.jsx("span",{className:"text-primary-400",children:"Tera Works"})]})]}),p.jsxs("div",{children:[p.jsx("h4",{className:"text-lg font-semibold mb-4",children:"Quick Links"}),p.jsxs("ul",{className:"space-y-2",children:[p.jsx("li",{children:p.jsx(Jt,{to:"/destinations",className:"text-gray-300 hover:text-white transition-colors",children:"Destinations"})}),p.jsx("li",{children:p.jsx(Jt,{to:"/activities",className:"text-gray-300 hover:text-white transition-colors",children:"Activities"})}),p.jsx("li",{children:p.jsx(Jt,{to:"/culture",className:"text-gray-300 hover:text-white transition-colors",children:"Culture"})}),p.jsx("li",{children:p.jsx(Jt,{to:"/travel-tips",className:"text-gray-300 hover:text-white transition-colors",children:"Travel Tips"})})]})]}),p.jsxs("div",{children:[p.jsx("h4",{className:"text-lg font-semibold mb-4",children:"Contact"}),p.jsxs("ul",{className:"space-y-2 text-gray-300",children:[p.jsx("li",{children:"Email: <EMAIL>"}),p.jsx("li",{children:"Phone: +94 11 123 4567"}),p.jsx("li",{children:"Address: Colombo, Sri Lanka"})]})]})]}),p.jsx("div",{className:"border-t border-gray-800 mt-8 pt-8 text-center text-gray-400",children:p.jsx("p",{children:"© 2025 Sri Lanka Tourist Guide. All rights reserved."})})]})});function ps(i,e){for(var n=0;n<e.length;n++){var t=e[n];t.enumerable=t.enumerable||!1,t.configurable=!0,"value"in t&&(t.writable=!0),Object.defineProperty(i,t.key,t)}}function hs(i,e,n){return e&&ps(i.prototype,e),i}/*!
 * Observer 3.13.0
 * https://gsap.com
 *
 * @license Copyright 2008-2025, GreenSock. All rights reserved.
 * Subject to the terms at https://gsap.com/standard-license
 * @author: Jack Doyle, <EMAIL>
*/var be,dn,Ze,$t,zt,yr,Wi,Zt,Ir,Gi,kt,ut,Ki,Zi=function(){return be||typeof window<"u"&&(be=window.gsap)&&be.registerPlugin&&be},Qi=1,mr=[],M=[],_t=[],Nr=Date.now,Vn=function(e,n){return n},gs=function(){var e=Ir.core,n=e.bridge||{},t=e._scrollers,r=e._proxies;t.push.apply(t,M),r.push.apply(r,_t),M=t,_t=r,Vn=function(a,s){return n[a](s)}},Bt=function(e,n){return~_t.indexOf(e)&&_t[_t.indexOf(e)+1][n]},Hr=function(e){return!!~Gi.indexOf(e)},Ie=function(e,n,t,r,o){return e.addEventListener(n,t,{passive:r!==!1,capture:!!o})},Le=function(e,n,t,r){return e.removeEventListener(n,t,!!r)},Qr="scrollLeft",Jr="scrollTop",qn=function(){return kt&&kt.isPressed||M.cache++},vn=function(e,n){var t=function r(o){if(o||o===0){Qi&&(Ze.history.scrollRestoration="manual");var a=kt&&kt.isPressed;o=r.v=Math.round(o)||(kt&&kt.iOS?1:0),e(o),r.cacheID=M.cache,a&&Vn("ss",o)}else(n||M.cache!==r.cacheID||Vn("ref"))&&(r.cacheID=M.cache,r.v=e());return r.v+r.offset};return t.offset=0,e&&t},$e={s:Qr,p:"left",p2:"Left",os:"right",os2:"Right",d:"width",d2:"Width",a:"x",sc:vn(function(i){return arguments.length?Ze.scrollTo(i,ue.sc()):Ze.pageXOffset||$t[Qr]||zt[Qr]||yr[Qr]||0})},ue={s:Jr,p:"top",p2:"Top",os:"bottom",os2:"Bottom",d:"height",d2:"Height",a:"y",op:$e,sc:vn(function(i){return arguments.length?Ze.scrollTo($e.sc(),i):Ze.pageYOffset||$t[Jr]||zt[Jr]||yr[Jr]||0})},Xe=function(e,n){return(n&&n._ctx&&n._ctx.selector||be.utils.toArray)(e)[0]||(typeof e=="string"&&be.config().nullTargetWarn!==!1?console.warn("Element not found:",e):null)},ms=function(e,n){for(var t=n.length;t--;)if(n[t]===e||n[t].contains(e))return!0;return!1},Yt=function(e,n){var t=n.s,r=n.sc;Hr(e)&&(e=$t.scrollingElement||zt);var o=M.indexOf(e),a=r===ue.sc?1:2;!~o&&(o=M.push(e)-1),M[o+a]||Ie(e,"scroll",qn);var s=M[o+a],c=s||(M[o+a]=vn(Bt(e,t),!0)||(Hr(e)?r:vn(function(f){return arguments.length?e[t]=f:e[t]})));return c.target=e,s||(c.smooth=be.getProperty(e,"scrollBehavior")==="smooth"),c},Un=function(e,n,t){var r=e,o=e,a=Nr(),s=a,c=n||50,f=Math.max(500,c*3),h=function(y,Y){var I=Nr();Y||I-a>c?(o=r,r=y,s=a,a=I):t?r+=y:r=o+(y-o)/(I-s)*(a-s)},v=function(){o=r=t?0:r,s=a=0},g=function(y){var Y=s,I=o,W=Nr();return(y||y===0)&&y!==r&&h(y),a===s||W-s>f?0:(r+(t?I:-I))/((t?W:a)-Y)*1e3};return{update:h,reset:v,getVelocity:g}},Or=function(e,n){return n&&!e._gsapAllow&&e.preventDefault(),e.changedTouches?e.changedTouches[0]:e},Ti=function(e){var n=Math.max.apply(Math,e),t=Math.min.apply(Math,e);return Math.abs(n)>=Math.abs(t)?n:t},Ji=function(){Ir=be.core.globals().ScrollTrigger,Ir&&Ir.core&&gs()},eo=function(e){return be=e||Zi(),!dn&&be&&typeof document<"u"&&document.body&&(Ze=window,$t=document,zt=$t.documentElement,yr=$t.body,Gi=[Ze,$t,zt,yr],be.utils.clamp,Ki=be.core.context||function(){},Zt="onpointerenter"in yr?"pointer":"mouse",Wi=ee.isTouch=Ze.matchMedia&&Ze.matchMedia("(hover: none), (pointer: coarse)").matches?1:"ontouchstart"in Ze||navigator.maxTouchPoints>0||navigator.msMaxTouchPoints>0?2:0,ut=ee.eventTypes=("ontouchstart"in zt?"touchstart,touchmove,touchcancel,touchend":"onpointerdown"in zt?"pointerdown,pointermove,pointercancel,pointerup":"mousedown,mousemove,mouseup,mouseup").split(","),setTimeout(function(){return Qi=0},500),Ji(),dn=1),dn};$e.op=ue;M.cache=0;var ee=function(){function i(n){this.init(n)}var e=i.prototype;return e.init=function(t){dn||eo(be)||console.warn("Please gsap.registerPlugin(Observer)"),Ir||Ji();var r=t.tolerance,o=t.dragMinimum,a=t.type,s=t.target,c=t.lineHeight,f=t.debounce,h=t.preventDefault,v=t.onStop,g=t.onStopDelay,d=t.ignore,y=t.wheelSpeed,Y=t.event,I=t.onDragStart,W=t.onDragEnd,X=t.onDrag,_e=t.onPress,A=t.onRelease,Qe=t.onRight,V=t.onLeft,C=t.onUp,Pe=t.onDown,ze=t.onChangeX,x=t.onChangeY,fe=t.onChange,T=t.onToggleX,vt=t.onToggleY,se=t.onHover,Re=t.onHoverEnd,Me=t.onMove,$=t.ignoreCheck,te=t.isNormalizer,re=t.onGestureStart,l=t.onGestureEnd,ae=t.onWheel,Vt=t.onEnable,Ot=t.onDisable,Je=t.onClick,yt=t.scrollSpeed,we=t.capture,ne=t.allowClicks,De=t.lockAxis,Te=t.onLockAxis;this.target=s=Xe(s)||zt,this.vars=t,d&&(d=be.utils.toArray(d)),r=r||1e-9,o=o||0,y=y||1,yt=yt||1,a=a||"wheel,touch,pointer",f=f!==!1,c||(c=parseFloat(Ze.getComputedStyle(yr).lineHeight)||22);var Pt,je,Be,L,Z,Ye,Ve,u=this,qe=0,xt=0,Rt=t.passive||!h&&t.passive!==!1,G=Yt(s,$e),bt=Yt(s,ue),Mt=G(),qt=bt(),de=~a.indexOf("touch")&&!~a.indexOf("pointer")&&ut[0]==="pointerdown",Dt=Hr(s),Q=s.ownerDocument||$t,ot=[0,0,0],et=[0,0,0],wt=0,Tr=function(){return wt=Nr()},ie=function(w,N){return(u.event=w)&&d&&ms(w.target,d)||N&&de&&w.pointerType!=="touch"||$&&$(w,N)},Ur=function(){u._vx.reset(),u._vy.reset(),je.pause(),v&&v(u)},Tt=function(){var w=u.deltaX=Ti(ot),N=u.deltaY=Ti(et),m=Math.abs(w)>=r,E=Math.abs(N)>=r;fe&&(m||E)&&fe(u,w,N,ot,et),m&&(Qe&&u.deltaX>0&&Qe(u),V&&u.deltaX<0&&V(u),ze&&ze(u),T&&u.deltaX<0!=qe<0&&T(u),qe=u.deltaX,ot[0]=ot[1]=ot[2]=0),E&&(Pe&&u.deltaY>0&&Pe(u),C&&u.deltaY<0&&C(u),x&&x(u),vt&&u.deltaY<0!=xt<0&&vt(u),xt=u.deltaY,et[0]=et[1]=et[2]=0),(L||Be)&&(Me&&Me(u),Be&&(I&&Be===1&&I(u),X&&X(u),Be=0),L=!1),Ye&&!(Ye=!1)&&Te&&Te(u),Z&&(ae(u),Z=!1),Pt=0},lr=function(w,N,m){ot[m]+=w,et[m]+=N,u._vx.update(w),u._vy.update(N),f?Pt||(Pt=requestAnimationFrame(Tt)):Tt()},cr=function(w,N){De&&!Ve&&(u.axis=Ve=Math.abs(w)>Math.abs(N)?"x":"y",Ye=!0),Ve!=="y"&&(ot[2]+=w,u._vx.update(w,!0)),Ve!=="x"&&(et[2]+=N,u._vy.update(N,!0)),f?Pt||(Pt=requestAnimationFrame(Tt)):Tt()},jt=function(w){if(!ie(w,1)){w=Or(w,h);var N=w.clientX,m=w.clientY,E=N-u.x,b=m-u.y,S=u.isDragging;u.x=N,u.y=m,(S||(E||b)&&(Math.abs(u.startX-N)>=o||Math.abs(u.startY-m)>=o))&&(Be=S?2:1,S||(u.isDragging=!0),cr(E,b))}},Ut=u.onPress=function(k){ie(k,1)||k&&k.button||(u.axis=Ve=null,je.pause(),u.isPressed=!0,k=Or(k),qe=xt=0,u.startX=u.x=k.clientX,u.startY=u.y=k.clientY,u._vx.reset(),u._vy.reset(),Ie(te?s:Q,ut[1],jt,Rt,!0),u.deltaX=u.deltaY=0,_e&&_e(u))},D=u.onRelease=function(k){if(!ie(k,1)){Le(te?s:Q,ut[1],jt,!0);var w=!isNaN(u.y-u.startY),N=u.isDragging,m=N&&(Math.abs(u.x-u.startX)>3||Math.abs(u.y-u.startY)>3),E=Or(k);!m&&w&&(u._vx.reset(),u._vy.reset(),h&&ne&&be.delayedCall(.08,function(){if(Nr()-wt>300&&!k.defaultPrevented){if(k.target.click)k.target.click();else if(Q.createEvent){var b=Q.createEvent("MouseEvents");b.initMouseEvent("click",!0,!0,Ze,1,E.screenX,E.screenY,E.clientX,E.clientY,!1,!1,!1,!1,0,null),k.target.dispatchEvent(b)}}})),u.isDragging=u.isGesturing=u.isPressed=!1,v&&N&&!te&&je.restart(!0),Be&&Tt(),W&&N&&W(u),A&&A(u,m)}},Wt=function(w){return w.touches&&w.touches.length>1&&(u.isGesturing=!0)&&re(w,u.isDragging)},st=function(){return(u.isGesturing=!1)||l(u)},at=function(w){if(!ie(w)){var N=G(),m=bt();lr((N-Mt)*yt,(m-qt)*yt,1),Mt=N,qt=m,v&&je.restart(!0)}},lt=function(w){if(!ie(w)){w=Or(w,h),ae&&(Z=!0);var N=(w.deltaMode===1?c:w.deltaMode===2?Ze.innerHeight:1)*y;lr(w.deltaX*N,w.deltaY*N,0),v&&!te&&je.restart(!0)}},Gt=function(w){if(!ie(w)){var N=w.clientX,m=w.clientY,E=N-u.x,b=m-u.y;u.x=N,u.y=m,L=!0,v&&je.restart(!0),(E||b)&&cr(E,b)}},ur=function(w){u.event=w,se(u)},Ct=function(w){u.event=w,Re(u)},Cr=function(w){return ie(w)||Or(w,h)&&Je(u)};je=u._dc=be.delayedCall(g||.25,Ur).pause(),u.deltaX=u.deltaY=0,u._vx=Un(0,50,!0),u._vy=Un(0,50,!0),u.scrollX=G,u.scrollY=bt,u.isDragging=u.isGesturing=u.isPressed=!1,Ki(this),u.enable=function(k){return u.isEnabled||(Ie(Dt?Q:s,"scroll",qn),a.indexOf("scroll")>=0&&Ie(Dt?Q:s,"scroll",at,Rt,we),a.indexOf("wheel")>=0&&Ie(s,"wheel",lt,Rt,we),(a.indexOf("touch")>=0&&Wi||a.indexOf("pointer")>=0)&&(Ie(s,ut[0],Ut,Rt,we),Ie(Q,ut[2],D),Ie(Q,ut[3],D),ne&&Ie(s,"click",Tr,!0,!0),Je&&Ie(s,"click",Cr),re&&Ie(Q,"gesturestart",Wt),l&&Ie(Q,"gestureend",st),se&&Ie(s,Zt+"enter",ur),Re&&Ie(s,Zt+"leave",Ct),Me&&Ie(s,Zt+"move",Gt)),u.isEnabled=!0,u.isDragging=u.isGesturing=u.isPressed=L=Be=!1,u._vx.reset(),u._vy.reset(),Mt=G(),qt=bt(),k&&k.type&&Ut(k),Vt&&Vt(u)),u},u.disable=function(){u.isEnabled&&(mr.filter(function(k){return k!==u&&Hr(k.target)}).length||Le(Dt?Q:s,"scroll",qn),u.isPressed&&(u._vx.reset(),u._vy.reset(),Le(te?s:Q,ut[1],jt,!0)),Le(Dt?Q:s,"scroll",at,we),Le(s,"wheel",lt,we),Le(s,ut[0],Ut,we),Le(Q,ut[2],D),Le(Q,ut[3],D),Le(s,"click",Tr,!0),Le(s,"click",Cr),Le(Q,"gesturestart",Wt),Le(Q,"gestureend",st),Le(s,Zt+"enter",ur),Le(s,Zt+"leave",Ct),Le(s,Zt+"move",Gt),u.isEnabled=u.isPressed=u.isDragging=!1,Ot&&Ot(u))},u.kill=u.revert=function(){u.disable();var k=mr.indexOf(u);k>=0&&mr.splice(k,1),kt===u&&(kt=0)},mr.push(u),te&&Hr(s)&&(kt=u),u.enable(Y)},hs(i,[{key:"velocityX",get:function(){return this._vx.getVelocity()}},{key:"velocityY",get:function(){return this._vy.getVelocity()}}]),i}();ee.version="3.13.0";ee.create=function(i){return new ee(i)};ee.register=eo;ee.getAll=function(){return mr.slice()};ee.getById=function(i){return mr.filter(function(e){return e.vars.id===i})[0]};Zi()&&be.registerPlugin(ee);/*!
 * ScrollTrigger 3.13.0
 * https://gsap.com
 *
 * @license Copyright 2008-2025, GreenSock. All rights reserved.
 * Subject to the terms at https://gsap.com/standard-license
 * @author: Jack Doyle, <EMAIL>
*/var _,hr,R,B,Ke,F,ri,yn,Vr,Fr,Rr,en,ke,En,Wn,He,Ci,Ei,gr,to,Dn,ro,Ne,Gn,no,io,Ft,Kn,ni,xr,ii,xn,Zn,jn,tn=1,Ae=Date.now,Ln=Ae(),it=0,Mr=0,Si=function(e,n,t){var r=Ge(e)&&(e.substr(0,6)==="clamp("||e.indexOf("max")>-1);return t["_"+n+"Clamp"]=r,r?e.substr(6,e.length-7):e},ki=function(e,n){return n&&(!Ge(e)||e.substr(0,6)!=="clamp(")?"clamp("+e+")":e},_s=function i(){return Mr&&requestAnimationFrame(i)},Ai=function(){return En=1},Oi=function(){return En=0},gt=function(e){return e},Dr=function(e){return Math.round(e*1e5)/1e5||0},oo=function(){return typeof window<"u"},so=function(){return _||oo()&&(_=window.gsap)&&_.registerPlugin&&_},or=function(e){return!!~ri.indexOf(e)},ao=function(e){return(e==="Height"?ii:R["inner"+e])||Ke["client"+e]||F["client"+e]},lo=function(e){return Bt(e,"getBoundingClientRect")||(or(e)?function(){return _n.width=R.innerWidth,_n.height=ii,_n}:function(){return St(e)})},vs=function(e,n,t){var r=t.d,o=t.d2,a=t.a;return(a=Bt(e,"getBoundingClientRect"))?function(){return a()[r]}:function(){return(n?ao(o):e["client"+o])||0}},ys=function(e,n){return!n||~_t.indexOf(e)?lo(e):function(){return _n}},mt=function(e,n){var t=n.s,r=n.d2,o=n.d,a=n.a;return Math.max(0,(t="scroll"+r)&&(a=Bt(e,t))?a()-lo(e)()[o]:or(e)?(Ke[t]||F[t])-ao(r):e[t]-e["offset"+r])},rn=function(e,n){for(var t=0;t<gr.length;t+=3)(!n||~n.indexOf(gr[t+1]))&&e(gr[t],gr[t+1],gr[t+2])},Ge=function(e){return typeof e=="string"},Oe=function(e){return typeof e=="function"},jr=function(e){return typeof e=="number"},Qt=function(e){return typeof e=="object"},Pr=function(e,n,t){return e&&e.progress(n?0:1)&&t&&e.pause()},In=function(e,n){if(e.enabled){var t=e._ctx?e._ctx.add(function(){return n(e)}):n(e);t&&t.totalTime&&(e.callbackAnimation=t)}},dr=Math.abs,co="left",uo="top",oi="right",si="bottom",rr="width",nr="height",$r="Right",zr="Left",Br="Top",Yr="Bottom",oe="padding",rt="margin",wr="Width",ai="Height",ce="px",nt=function(e){return R.getComputedStyle(e)},xs=function(e){var n=nt(e).position;e.style.position=n==="absolute"||n==="fixed"?n:"relative"},Pi=function(e,n){for(var t in n)t in e||(e[t]=n[t]);return e},St=function(e,n){var t=n&&nt(e)[Wn]!=="matrix(1, 0, 0, 1, 0, 0)"&&_.to(e,{x:0,y:0,xPercent:0,yPercent:0,rotation:0,rotationX:0,rotationY:0,scale:1,skewX:0,skewY:0}).progress(1),r=e.getBoundingClientRect();return t&&t.progress(0).kill(),r},bn=function(e,n){var t=n.d2;return e["offset"+t]||e["client"+t]||0},fo=function(e){var n=[],t=e.labels,r=e.duration(),o;for(o in t)n.push(t[o]/r);return n},bs=function(e){return function(n){return _.utils.snap(fo(e),n)}},li=function(e){var n=_.utils.snap(e),t=Array.isArray(e)&&e.slice(0).sort(function(r,o){return r-o});return t?function(r,o,a){a===void 0&&(a=.001);var s;if(!o)return n(r);if(o>0){for(r-=a,s=0;s<t.length;s++)if(t[s]>=r)return t[s];return t[s-1]}else for(s=t.length,r+=a;s--;)if(t[s]<=r)return t[s];return t[0]}:function(r,o,a){a===void 0&&(a=.001);var s=n(r);return!o||Math.abs(s-r)<a||s-r<0==o<0?s:n(o<0?r-e:r+e)}},ws=function(e){return function(n,t){return li(fo(e))(n,t.direction)}},nn=function(e,n,t,r){return t.split(",").forEach(function(o){return e(n,o,r)})},ge=function(e,n,t,r,o){return e.addEventListener(n,t,{passive:!r,capture:!!o})},he=function(e,n,t,r){return e.removeEventListener(n,t,!!r)},on=function(e,n,t){t=t&&t.wheelHandler,t&&(e(n,"wheel",t),e(n,"touchmove",t))},Ri={startColor:"green",endColor:"red",indent:0,fontSize:"16px",fontWeight:"normal"},sn={toggleActions:"play",anticipatePin:0},wn={top:0,left:0,center:.5,bottom:1,right:1},pn=function(e,n){if(Ge(e)){var t=e.indexOf("="),r=~t?+(e.charAt(t-1)+1)*parseFloat(e.substr(t+1)):0;~t&&(e.indexOf("%")>t&&(r*=n/100),e=e.substr(0,t-1)),e=r+(e in wn?wn[e]*n:~e.indexOf("%")?parseFloat(e)*n/100:parseFloat(e)||0)}return e},an=function(e,n,t,r,o,a,s,c){var f=o.startColor,h=o.endColor,v=o.fontSize,g=o.indent,d=o.fontWeight,y=B.createElement("div"),Y=or(t)||Bt(t,"pinType")==="fixed",I=e.indexOf("scroller")!==-1,W=Y?F:t,X=e.indexOf("start")!==-1,_e=X?f:h,A="border-color:"+_e+";font-size:"+v+";color:"+_e+";font-weight:"+d+";pointer-events:none;white-space:nowrap;font-family:sans-serif,Arial;z-index:1000;padding:4px 8px;border-width:0;border-style:solid;";return A+="position:"+((I||c)&&Y?"fixed;":"absolute;"),(I||c||!Y)&&(A+=(r===ue?oi:si)+":"+(a+parseFloat(g))+"px;"),s&&(A+="box-sizing:border-box;text-align:left;width:"+s.offsetWidth+"px;"),y._isStart=X,y.setAttribute("class","gsap-marker-"+e+(n?" marker-"+n:"")),y.style.cssText=A,y.innerText=n||n===0?e+"-"+n:e,W.children[0]?W.insertBefore(y,W.children[0]):W.appendChild(y),y._offset=y["offset"+r.op.d2],hn(y,0,r,X),y},hn=function(e,n,t,r){var o={display:"block"},a=t[r?"os2":"p2"],s=t[r?"p2":"os2"];e._isFlipped=r,o[t.a+"Percent"]=r?-100:0,o[t.a]=r?"1px":0,o["border"+a+wr]=1,o["border"+s+wr]=0,o[t.p]=n+"px",_.set(e,o)},O=[],Qn={},qr,Mi=function(){return Ae()-it>34&&(qr||(qr=requestAnimationFrame(At)))},pr=function(){(!Ne||!Ne.isPressed||Ne.startX>F.clientWidth)&&(M.cache++,Ne?qr||(qr=requestAnimationFrame(At)):At(),it||ar("scrollStart"),it=Ae())},Nn=function(){io=R.innerWidth,no=R.innerHeight},Lr=function(e){M.cache++,(e===!0||!ke&&!ro&&!B.fullscreenElement&&!B.webkitFullscreenElement&&(!Gn||io!==R.innerWidth||Math.abs(R.innerHeight-no)>R.innerHeight*.25))&&yn.restart(!0)},sr={},Ts=[],po=function i(){return he(P,"scrollEnd",i)||er(!0)},ar=function(e){return sr[e]&&sr[e].map(function(n){return n()})||Ts},We=[],ho=function(e){for(var n=0;n<We.length;n+=5)(!e||We[n+4]&&We[n+4].query===e)&&(We[n].style.cssText=We[n+1],We[n].getBBox&&We[n].setAttribute("transform",We[n+2]||""),We[n+3].uncache=1)},ci=function(e,n){var t;for(He=0;He<O.length;He++)t=O[He],t&&(!n||t._ctx===n)&&(e?t.kill(1):t.revert(!0,!0));xn=!0,n&&ho(n),n||ar("revert")},go=function(e,n){M.cache++,(n||!Fe)&&M.forEach(function(t){return Oe(t)&&t.cacheID++&&(t.rec=0)}),Ge(e)&&(R.history.scrollRestoration=ni=e)},Fe,ir=0,Di,Cs=function(){if(Di!==ir){var e=Di=ir;requestAnimationFrame(function(){return e===ir&&er(!0)})}},mo=function(){F.appendChild(xr),ii=!Ne&&xr.offsetHeight||R.innerHeight,F.removeChild(xr)},ji=function(e){return Vr(".gsap-marker-start, .gsap-marker-end, .gsap-marker-scroller-start, .gsap-marker-scroller-end").forEach(function(n){return n.style.display=e?"none":"block"})},er=function(e,n){if(Ke=B.documentElement,F=B.body,ri=[R,B,Ke,F],it&&!e&&!xn){ge(P,"scrollEnd",po);return}mo(),Fe=P.isRefreshing=!0,M.forEach(function(r){return Oe(r)&&++r.cacheID&&(r.rec=r())});var t=ar("refreshInit");to&&P.sort(),n||ci(),M.forEach(function(r){Oe(r)&&(r.smooth&&(r.target.style.scrollBehavior="auto"),r(0))}),O.slice(0).forEach(function(r){return r.refresh()}),xn=!1,O.forEach(function(r){if(r._subPinOffset&&r.pin){var o=r.vars.horizontal?"offsetWidth":"offsetHeight",a=r.pin[o];r.revert(!0,1),r.adjustPinSpacing(r.pin[o]-a),r.refresh()}}),Zn=1,ji(!0),O.forEach(function(r){var o=mt(r.scroller,r._dir),a=r.vars.end==="max"||r._endClamp&&r.end>o,s=r._startClamp&&r.start>=o;(a||s)&&r.setPositions(s?o-1:r.start,a?Math.max(s?o:r.start+1,o):r.end,!0)}),ji(!1),Zn=0,t.forEach(function(r){return r&&r.render&&r.render(-1)}),M.forEach(function(r){Oe(r)&&(r.smooth&&requestAnimationFrame(function(){return r.target.style.scrollBehavior="smooth"}),r.rec&&r(r.rec))}),go(ni,1),yn.pause(),ir++,Fe=2,At(2),O.forEach(function(r){return Oe(r.vars.onRefresh)&&r.vars.onRefresh(r)}),Fe=P.isRefreshing=!1,ar("refresh")},Jn=0,gn=1,Xr,At=function(e){if(e===2||!Fe&&!xn){P.isUpdating=!0,Xr&&Xr.update(0);var n=O.length,t=Ae(),r=t-Ln>=50,o=n&&O[0].scroll();if(gn=Jn>o?-1:1,Fe||(Jn=o),r&&(it&&!En&&t-it>200&&(it=0,ar("scrollEnd")),Rr=Ln,Ln=t),gn<0){for(He=n;He-- >0;)O[He]&&O[He].update(0,r);gn=1}else for(He=0;He<n;He++)O[He]&&O[He].update(0,r);P.isUpdating=!1}qr=0},ei=[co,uo,si,oi,rt+Yr,rt+$r,rt+Br,rt+zr,"display","flexShrink","float","zIndex","gridColumnStart","gridColumnEnd","gridRowStart","gridRowEnd","gridArea","justifySelf","alignSelf","placeSelf","order"],mn=ei.concat([rr,nr,"boxSizing","max"+wr,"max"+ai,"position",rt,oe,oe+Br,oe+$r,oe+Yr,oe+zr]),Es=function(e,n,t){br(t);var r=e._gsap;if(r.spacerIsNative)br(r.spacerState);else if(e._gsap.swappedIn){var o=n.parentNode;o&&(o.insertBefore(e,n),o.removeChild(n))}e._gsap.swappedIn=!1},Hn=function(e,n,t,r){if(!e._gsap.swappedIn){for(var o=ei.length,a=n.style,s=e.style,c;o--;)c=ei[o],a[c]=t[c];a.position=t.position==="absolute"?"absolute":"relative",t.display==="inline"&&(a.display="inline-block"),s[si]=s[oi]="auto",a.flexBasis=t.flexBasis||"auto",a.overflow="visible",a.boxSizing="border-box",a[rr]=bn(e,$e)+ce,a[nr]=bn(e,ue)+ce,a[oe]=s[rt]=s[uo]=s[co]="0",br(r),s[rr]=s["max"+wr]=t[rr],s[nr]=s["max"+ai]=t[nr],s[oe]=t[oe],e.parentNode!==n&&(e.parentNode.insertBefore(n,e),n.appendChild(e)),e._gsap.swappedIn=!0}},Ss=/([A-Z])/g,br=function(e){if(e){var n=e.t.style,t=e.length,r=0,o,a;for((e.t._gsap||_.core.getCache(e.t)).uncache=1;r<t;r+=2)a=e[r+1],o=e[r],a?n[o]=a:n[o]&&n.removeProperty(o.replace(Ss,"-$1").toLowerCase())}},ln=function(e){for(var n=mn.length,t=e.style,r=[],o=0;o<n;o++)r.push(mn[o],t[mn[o]]);return r.t=e,r},ks=function(e,n,t){for(var r=[],o=e.length,a=t?8:0,s;a<o;a+=2)s=e[a],r.push(s,s in n?n[s]:e[a+1]);return r.t=e.t,r},_n={left:0,top:0},Li=function(e,n,t,r,o,a,s,c,f,h,v,g,d,y){Oe(e)&&(e=e(c)),Ge(e)&&e.substr(0,3)==="max"&&(e=g+(e.charAt(4)==="="?pn("0"+e.substr(3),t):0));var Y=d?d.time():0,I,W,X;if(d&&d.seek(0),isNaN(e)||(e=+e),jr(e))d&&(e=_.utils.mapRange(d.scrollTrigger.start,d.scrollTrigger.end,0,g,e)),s&&hn(s,t,r,!0);else{Oe(n)&&(n=n(c));var _e=(e||"0").split(" "),A,Qe,V,C;X=Xe(n,c)||F,A=St(X)||{},(!A||!A.left&&!A.top)&&nt(X).display==="none"&&(C=X.style.display,X.style.display="block",A=St(X),C?X.style.display=C:X.style.removeProperty("display")),Qe=pn(_e[0],A[r.d]),V=pn(_e[1]||"0",t),e=A[r.p]-f[r.p]-h+Qe+o-V,s&&hn(s,V,r,t-V<20||s._isStart&&V>20),t-=t-V}if(y&&(c[y]=e||-.001,e<0&&(e=0)),a){var Pe=e+t,ze=a._isStart;I="scroll"+r.d2,hn(a,Pe,r,ze&&Pe>20||!ze&&(v?Math.max(F[I],Ke[I]):a.parentNode[I])<=Pe+1),v&&(f=St(s),v&&(a.style[r.op.p]=f[r.op.p]-r.op.m-a._offset+ce))}return d&&X&&(I=St(X),d.seek(g),W=St(X),d._caScrollDist=I[r.p]-W[r.p],e=e/d._caScrollDist*g),d&&d.seek(Y),d?e:Math.round(e)},As=/(webkit|moz|length|cssText|inset)/i,Ii=function(e,n,t,r){if(e.parentNode!==n){var o=e.style,a,s;if(n===F){e._stOrig=o.cssText,s=nt(e);for(a in s)!+a&&!As.test(a)&&s[a]&&typeof o[a]=="string"&&a!=="0"&&(o[a]=s[a]);o.top=t,o.left=r}else o.cssText=e._stOrig;_.core.getCache(e).uncache=1,n.appendChild(e)}},_o=function(e,n,t){var r=n,o=r;return function(a){var s=Math.round(e());return s!==r&&s!==o&&Math.abs(s-r)>3&&Math.abs(s-o)>3&&(a=s,t&&t()),o=r,r=Math.round(a),r}},cn=function(e,n,t){var r={};r[n.p]="+="+t,_.set(e,r)},Ni=function(e,n){var t=Yt(e,n),r="_scroll"+n.p2,o=function a(s,c,f,h,v){var g=a.tween,d=c.onComplete,y={};f=f||t();var Y=_o(t,f,function(){g.kill(),a.tween=0});return v=h&&v||0,h=h||s-f,g&&g.kill(),c[r]=s,c.inherit=!1,c.modifiers=y,y[r]=function(){return Y(f+h*g.ratio+v*g.ratio*g.ratio)},c.onUpdate=function(){M.cache++,a.tween&&At()},c.onComplete=function(){a.tween=0,d&&d.call(g)},g=a.tween=_.to(e,c),g};return e[r]=t,t.wheelHandler=function(){return o.tween&&o.tween.kill()&&(o.tween=0)},ge(e,"wheel",t.wheelHandler),P.isTouch&&ge(e,"touchmove",t.wheelHandler),o},P=function(){function i(n,t){hr||i.register(_)||console.warn("Please gsap.registerPlugin(ScrollTrigger)"),Kn(this),this.init(n,t)}var e=i.prototype;return e.init=function(t,r){if(this.progress=this.start=0,this.vars&&this.kill(!0,!0),!Mr){this.update=this.refresh=this.kill=gt;return}t=Pi(Ge(t)||jr(t)||t.nodeType?{trigger:t}:t,sn);var o=t,a=o.onUpdate,s=o.toggleClass,c=o.id,f=o.onToggle,h=o.onRefresh,v=o.scrub,g=o.trigger,d=o.pin,y=o.pinSpacing,Y=o.invalidateOnRefresh,I=o.anticipatePin,W=o.onScrubComplete,X=o.onSnapComplete,_e=o.once,A=o.snap,Qe=o.pinReparent,V=o.pinSpacer,C=o.containerAnimation,Pe=o.fastScrollEnd,ze=o.preventOverlaps,x=t.horizontal||t.containerAnimation&&t.horizontal!==!1?$e:ue,fe=!v&&v!==0,T=Xe(t.scroller||R),vt=_.core.getCache(T),se=or(T),Re=("pinType"in t?t.pinType:Bt(T,"pinType")||se&&"fixed")==="fixed",Me=[t.onEnter,t.onLeave,t.onEnterBack,t.onLeaveBack],$=fe&&t.toggleActions.split(" "),te="markers"in t?t.markers:sn.markers,re=se?0:parseFloat(nt(T)["border"+x.p2+wr])||0,l=this,ae=t.onRefreshInit&&function(){return t.onRefreshInit(l)},Vt=vs(T,se,x),Ot=ys(T,se),Je=0,yt=0,we=0,ne=Yt(T,x),De,Te,Pt,je,Be,L,Z,Ye,Ve,u,qe,xt,Rt,G,bt,Mt,qt,de,Dt,Q,ot,et,wt,Tr,ie,Ur,Tt,lr,cr,jt,Ut,D,Wt,st,at,lt,Gt,ur,Ct;if(l._startClamp=l._endClamp=!1,l._dir=x,I*=45,l.scroller=T,l.scroll=C?C.time.bind(C):ne,je=ne(),l.vars=t,r=r||t.animation,"refreshPriority"in t&&(to=1,t.refreshPriority===-9999&&(Xr=l)),vt.tweenScroll=vt.tweenScroll||{top:Ni(T,ue),left:Ni(T,$e)},l.tweenTo=De=vt.tweenScroll[x.p],l.scrubDuration=function(m){Wt=jr(m)&&m,Wt?D?D.duration(m):D=_.to(r,{ease:"expo",totalProgress:"+=0",inherit:!1,duration:Wt,paused:!0,onComplete:function(){return W&&W(l)}}):(D&&D.progress(1).kill(),D=0)},r&&(r.vars.lazy=!1,r._initted&&!l.isReverted||r.vars.immediateRender!==!1&&t.immediateRender!==!1&&r.duration()&&r.render(0,!0,!0),l.animation=r.pause(),r.scrollTrigger=l,l.scrubDuration(v),jt=0,c||(c=r.vars.id)),A&&((!Qt(A)||A.push)&&(A={snapTo:A}),"scrollBehavior"in F.style&&_.set(se?[F,Ke]:T,{scrollBehavior:"auto"}),M.forEach(function(m){return Oe(m)&&m.target===(se?B.scrollingElement||Ke:T)&&(m.smooth=!1)}),Pt=Oe(A.snapTo)?A.snapTo:A.snapTo==="labels"?bs(r):A.snapTo==="labelsDirectional"?ws(r):A.directional!==!1?function(m,E){return li(A.snapTo)(m,Ae()-yt<500?0:E.direction)}:_.utils.snap(A.snapTo),st=A.duration||{min:.1,max:2},st=Qt(st)?Fr(st.min,st.max):Fr(st,st),at=_.delayedCall(A.delay||Wt/2||.1,function(){var m=ne(),E=Ae()-yt<500,b=De.tween;if((E||Math.abs(l.getVelocity())<10)&&!b&&!En&&Je!==m){var S=(m-L)/G,pe=r&&!fe?r.totalProgress():S,j=E?0:(pe-Ut)/(Ae()-Rr)*1e3||0,J=_.utils.clamp(-S,1-S,dr(j/2)*j/.185),Ce=S+(A.inertia===!1?0:J),K,q,z=A,ct=z.onStart,U=z.onInterrupt,Ue=z.onComplete;if(K=Pt(Ce,l),jr(K)||(K=Ce),q=Math.max(0,Math.round(L+K*G)),m<=Z&&m>=L&&q!==m){if(b&&!b._initted&&b.data<=dr(q-m))return;A.inertia===!1&&(J=K-S),De(q,{duration:st(dr(Math.max(dr(Ce-pe),dr(K-pe))*.185/j/.05||0)),ease:A.ease||"power3",data:dr(q-m),onInterrupt:function(){return at.restart(!0)&&U&&U(l)},onComplete:function(){l.update(),Je=ne(),r&&!fe&&(D?D.resetTo("totalProgress",K,r._tTime/r._tDur):r.progress(K)),jt=Ut=r&&!fe?r.totalProgress():l.progress,X&&X(l),Ue&&Ue(l)}},m,J*G,q-m-J*G),ct&&ct(l,De.tween)}}else l.isActive&&Je!==m&&at.restart(!0)}).pause()),c&&(Qn[c]=l),g=l.trigger=Xe(g||d!==!0&&d),Ct=g&&g._gsap&&g._gsap.stRevert,Ct&&(Ct=Ct(l)),d=d===!0?g:Xe(d),Ge(s)&&(s={targets:g,className:s}),d&&(y===!1||y===rt||(y=!y&&d.parentNode&&d.parentNode.style&&nt(d.parentNode).display==="flex"?!1:oe),l.pin=d,Te=_.core.getCache(d),Te.spacer?bt=Te.pinState:(V&&(V=Xe(V),V&&!V.nodeType&&(V=V.current||V.nativeElement),Te.spacerIsNative=!!V,V&&(Te.spacerState=ln(V))),Te.spacer=de=V||B.createElement("div"),de.classList.add("pin-spacer"),c&&de.classList.add("pin-spacer-"+c),Te.pinState=bt=ln(d)),t.force3D!==!1&&_.set(d,{force3D:!0}),l.spacer=de=Te.spacer,cr=nt(d),Tr=cr[y+x.os2],Q=_.getProperty(d),ot=_.quickSetter(d,x.a,ce),Hn(d,de,cr),qt=ln(d)),te){xt=Qt(te)?Pi(te,Ri):Ri,u=an("scroller-start",c,T,x,xt,0),qe=an("scroller-end",c,T,x,xt,0,u),Dt=u["offset"+x.op.d2];var Cr=Xe(Bt(T,"content")||T);Ye=this.markerStart=an("start",c,Cr,x,xt,Dt,0,C),Ve=this.markerEnd=an("end",c,Cr,x,xt,Dt,0,C),C&&(ur=_.quickSetter([Ye,Ve],x.a,ce)),!Re&&!(_t.length&&Bt(T,"fixedMarkers")===!0)&&(xs(se?F:T),_.set([u,qe],{force3D:!0}),Ur=_.quickSetter(u,x.a,ce),lr=_.quickSetter(qe,x.a,ce))}if(C){var k=C.vars.onUpdate,w=C.vars.onUpdateParams;C.eventCallback("onUpdate",function(){l.update(0,0,1),k&&k.apply(C,w||[])})}if(l.previous=function(){return O[O.indexOf(l)-1]},l.next=function(){return O[O.indexOf(l)+1]},l.revert=function(m,E){if(!E)return l.kill(!0);var b=m!==!1||!l.enabled,S=ke;b!==l.isReverted&&(b&&(lt=Math.max(ne(),l.scroll.rec||0),we=l.progress,Gt=r&&r.progress()),Ye&&[Ye,Ve,u,qe].forEach(function(pe){return pe.style.display=b?"none":"block"}),b&&(ke=l,l.update(b)),d&&(!Qe||!l.isActive)&&(b?Es(d,de,bt):Hn(d,de,nt(d),ie)),b||l.update(b),ke=S,l.isReverted=b)},l.refresh=function(m,E,b,S){if(!((ke||!l.enabled)&&!E)){if(d&&m&&it){ge(i,"scrollEnd",po);return}!Fe&&ae&&ae(l),ke=l,De.tween&&!b&&(De.tween.kill(),De.tween=0),D&&D.pause(),Y&&r&&(r.revert({kill:!1}).invalidate(),r.getChildren&&r.getChildren(!0,!0,!1).forEach(function(Lt){return Lt.vars.immediateRender&&Lt.render(0,!0,!0)})),l.isReverted||l.revert(!0,!0),l._subPinOffset=!1;var pe=Vt(),j=Ot(),J=C?C.duration():mt(T,x),Ce=G<=.01||!G,K=0,q=S||0,z=Qt(b)?b.end:t.end,ct=t.endTrigger||g,U=Qt(b)?b.start:t.start||(t.start===0||!g?0:d?"0 0":"0 100%"),Ue=l.pinnedContainer=t.pinnedContainer&&Xe(t.pinnedContainer,l),dt=g&&Math.max(0,O.indexOf(l))||0,ve=dt,ye,Ee,Kt,Wr,Se,le,pt,Sn,ui,Er,ht,Sr,Gr;for(te&&Qt(b)&&(Sr=_.getProperty(u,x.p),Gr=_.getProperty(qe,x.p));ve-- >0;)le=O[ve],le.end||le.refresh(0,1)||(ke=l),pt=le.pin,pt&&(pt===g||pt===d||pt===Ue)&&!le.isReverted&&(Er||(Er=[]),Er.unshift(le),le.revert(!0,!0)),le!==O[ve]&&(dt--,ve--);for(Oe(U)&&(U=U(l)),U=Si(U,"start",l),L=Li(U,g,pe,x,ne(),Ye,u,l,j,re,Re,J,C,l._startClamp&&"_startClamp")||(d?-.001:0),Oe(z)&&(z=z(l)),Ge(z)&&!z.indexOf("+=")&&(~z.indexOf(" ")?z=(Ge(U)?U.split(" ")[0]:"")+z:(K=pn(z.substr(2),pe),z=Ge(U)?U:(C?_.utils.mapRange(0,C.duration(),C.scrollTrigger.start,C.scrollTrigger.end,L):L)+K,ct=g)),z=Si(z,"end",l),Z=Math.max(L,Li(z||(ct?"100% 0":J),ct,pe,x,ne()+K,Ve,qe,l,j,re,Re,J,C,l._endClamp&&"_endClamp"))||-.001,K=0,ve=dt;ve--;)le=O[ve],pt=le.pin,pt&&le.start-le._pinPush<=L&&!C&&le.end>0&&(ye=le.end-(l._startClamp?Math.max(0,le.start):le.start),(pt===g&&le.start-le._pinPush<L||pt===Ue)&&isNaN(U)&&(K+=ye*(1-le.progress)),pt===d&&(q+=ye));if(L+=K,Z+=K,l._startClamp&&(l._startClamp+=K),l._endClamp&&!Fe&&(l._endClamp=Z||-.001,Z=Math.min(Z,mt(T,x))),G=Z-L||(L-=.01)&&.001,Ce&&(we=_.utils.clamp(0,1,_.utils.normalize(L,Z,lt))),l._pinPush=q,Ye&&K&&(ye={},ye[x.a]="+="+K,Ue&&(ye[x.p]="-="+ne()),_.set([Ye,Ve],ye)),d&&!(Zn&&l.end>=mt(T,x)))ye=nt(d),Wr=x===ue,Kt=ne(),et=parseFloat(Q(x.a))+q,!J&&Z>1&&(ht=(se?B.scrollingElement||Ke:T).style,ht={style:ht,value:ht["overflow"+x.a.toUpperCase()]},se&&nt(F)["overflow"+x.a.toUpperCase()]!=="scroll"&&(ht.style["overflow"+x.a.toUpperCase()]="scroll")),Hn(d,de,ye),qt=ln(d),Ee=St(d,!0),Sn=Re&&Yt(T,Wr?$e:ue)(),y?(ie=[y+x.os2,G+q+ce],ie.t=de,ve=y===oe?bn(d,x)+G+q:0,ve&&(ie.push(x.d,ve+ce),de.style.flexBasis!=="auto"&&(de.style.flexBasis=ve+ce)),br(ie),Ue&&O.forEach(function(Lt){Lt.pin===Ue&&Lt.vars.pinSpacing!==!1&&(Lt._subPinOffset=!0)}),Re&&ne(lt)):(ve=bn(d,x),ve&&de.style.flexBasis!=="auto"&&(de.style.flexBasis=ve+ce)),Re&&(Se={top:Ee.top+(Wr?Kt-L:Sn)+ce,left:Ee.left+(Wr?Sn:Kt-L)+ce,boxSizing:"border-box",position:"fixed"},Se[rr]=Se["max"+wr]=Math.ceil(Ee.width)+ce,Se[nr]=Se["max"+ai]=Math.ceil(Ee.height)+ce,Se[rt]=Se[rt+Br]=Se[rt+$r]=Se[rt+Yr]=Se[rt+zr]="0",Se[oe]=ye[oe],Se[oe+Br]=ye[oe+Br],Se[oe+$r]=ye[oe+$r],Se[oe+Yr]=ye[oe+Yr],Se[oe+zr]=ye[oe+zr],Mt=ks(bt,Se,Qe),Fe&&ne(0)),r?(ui=r._initted,Dn(1),r.render(r.duration(),!0,!0),wt=Q(x.a)-et+G+q,Tt=Math.abs(G-wt)>1,Re&&Tt&&Mt.splice(Mt.length-2,2),r.render(0,!0,!0),ui||r.invalidate(!0),r.parent||r.totalTime(r.totalTime()),Dn(0)):wt=G,ht&&(ht.value?ht.style["overflow"+x.a.toUpperCase()]=ht.value:ht.style.removeProperty("overflow-"+x.a));else if(g&&ne()&&!C)for(Ee=g.parentNode;Ee&&Ee!==F;)Ee._pinOffset&&(L-=Ee._pinOffset,Z-=Ee._pinOffset),Ee=Ee.parentNode;Er&&Er.forEach(function(Lt){return Lt.revert(!1,!0)}),l.start=L,l.end=Z,je=Be=Fe?lt:ne(),!C&&!Fe&&(je<lt&&ne(lt),l.scroll.rec=0),l.revert(!1,!0),yt=Ae(),at&&(Je=-1,at.restart(!0)),ke=0,r&&fe&&(r._initted||Gt)&&r.progress()!==Gt&&r.progress(Gt||0,!0).render(r.time(),!0,!0),(Ce||we!==l.progress||C||Y||r&&!r._initted)&&(r&&!fe&&(r._initted||we||r.vars.immediateRender!==!1)&&r.totalProgress(C&&L<-.001&&!we?_.utils.normalize(L,Z,0):we,!0),l.progress=Ce||(je-L)/G===we?0:we),d&&y&&(de._pinOffset=Math.round(l.progress*wt)),D&&D.invalidate(),isNaN(Sr)||(Sr-=_.getProperty(u,x.p),Gr-=_.getProperty(qe,x.p),cn(u,x,Sr),cn(Ye,x,Sr-(S||0)),cn(qe,x,Gr),cn(Ve,x,Gr-(S||0))),Ce&&!Fe&&l.update(),h&&!Fe&&!Rt&&(Rt=!0,h(l),Rt=!1)}},l.getVelocity=function(){return(ne()-Be)/(Ae()-Rr)*1e3||0},l.endAnimation=function(){Pr(l.callbackAnimation),r&&(D?D.progress(1):r.paused()?fe||Pr(r,l.direction<0,1):Pr(r,r.reversed()))},l.labelToScroll=function(m){return r&&r.labels&&(L||l.refresh()||L)+r.labels[m]/r.duration()*G||0},l.getTrailing=function(m){var E=O.indexOf(l),b=l.direction>0?O.slice(0,E).reverse():O.slice(E+1);return(Ge(m)?b.filter(function(S){return S.vars.preventOverlaps===m}):b).filter(function(S){return l.direction>0?S.end<=L:S.start>=Z})},l.update=function(m,E,b){if(!(C&&!b&&!m)){var S=Fe===!0?lt:l.scroll(),pe=m?0:(S-L)/G,j=pe<0?0:pe>1?1:pe||0,J=l.progress,Ce,K,q,z,ct,U,Ue,dt;if(E&&(Be=je,je=C?ne():S,A&&(Ut=jt,jt=r&&!fe?r.totalProgress():j)),I&&d&&!ke&&!tn&&it&&(!j&&L<S+(S-Be)/(Ae()-Rr)*I?j=1e-4:j===1&&Z>S+(S-Be)/(Ae()-Rr)*I&&(j=.9999)),j!==J&&l.enabled){if(Ce=l.isActive=!!j&&j<1,K=!!J&&J<1,U=Ce!==K,ct=U||!!j!=!!J,l.direction=j>J?1:-1,l.progress=j,ct&&!ke&&(q=j&&!J?0:j===1?1:J===1?2:3,fe&&(z=!U&&$[q+1]!=="none"&&$[q+1]||$[q],dt=r&&(z==="complete"||z==="reset"||z in r))),ze&&(U||dt)&&(dt||v||!r)&&(Oe(ze)?ze(l):l.getTrailing(ze).forEach(function(Kt){return Kt.endAnimation()})),fe||(D&&!ke&&!tn?(D._dp._time-D._start!==D._time&&D.render(D._dp._time-D._start),D.resetTo?D.resetTo("totalProgress",j,r._tTime/r._tDur):(D.vars.totalProgress=j,D.invalidate().restart())):r&&r.totalProgress(j,!!(ke&&(yt||m)))),d){if(m&&y&&(de.style[y+x.os2]=Tr),!Re)ot(Dr(et+wt*j));else if(ct){if(Ue=!m&&j>J&&Z+1>S&&S+1>=mt(T,x),Qe)if(!m&&(Ce||Ue)){var ve=St(d,!0),ye=S-L;Ii(d,F,ve.top+(x===ue?ye:0)+ce,ve.left+(x===ue?0:ye)+ce)}else Ii(d,de);br(Ce||Ue?Mt:qt),Tt&&j<1&&Ce||ot(et+(j===1&&!Ue?wt:0))}}A&&!De.tween&&!ke&&!tn&&at.restart(!0),s&&(U||_e&&j&&(j<1||!jn))&&Vr(s.targets).forEach(function(Kt){return Kt.classList[Ce||_e?"add":"remove"](s.className)}),a&&!fe&&!m&&a(l),ct&&!ke?(fe&&(dt&&(z==="complete"?r.pause().totalProgress(1):z==="reset"?r.restart(!0).pause():z==="restart"?r.restart(!0):r[z]()),a&&a(l)),(U||!jn)&&(f&&U&&In(l,f),Me[q]&&In(l,Me[q]),_e&&(j===1?l.kill(!1,1):Me[q]=0),U||(q=j===1?1:3,Me[q]&&In(l,Me[q]))),Pe&&!Ce&&Math.abs(l.getVelocity())>(jr(Pe)?Pe:2500)&&(Pr(l.callbackAnimation),D?D.progress(1):Pr(r,z==="reverse"?1:!j,1))):fe&&a&&!ke&&a(l)}if(lr){var Ee=C?S/C.duration()*(C._caScrollDist||0):S;Ur(Ee+(u._isFlipped?1:0)),lr(Ee)}ur&&ur(-S/C.duration()*(C._caScrollDist||0))}},l.enable=function(m,E){l.enabled||(l.enabled=!0,ge(T,"resize",Lr),se||ge(T,"scroll",pr),ae&&ge(i,"refreshInit",ae),m!==!1&&(l.progress=we=0,je=Be=Je=ne()),E!==!1&&l.refresh())},l.getTween=function(m){return m&&De?De.tween:D},l.setPositions=function(m,E,b,S){if(C){var pe=C.scrollTrigger,j=C.duration(),J=pe.end-pe.start;m=pe.start+J*m/j,E=pe.start+J*E/j}l.refresh(!1,!1,{start:ki(m,b&&!!l._startClamp),end:ki(E,b&&!!l._endClamp)},S),l.update()},l.adjustPinSpacing=function(m){if(ie&&m){var E=ie.indexOf(x.d)+1;ie[E]=parseFloat(ie[E])+m+ce,ie[1]=parseFloat(ie[1])+m+ce,br(ie)}},l.disable=function(m,E){if(l.enabled&&(m!==!1&&l.revert(!0,!0),l.enabled=l.isActive=!1,E||D&&D.pause(),lt=0,Te&&(Te.uncache=1),ae&&he(i,"refreshInit",ae),at&&(at.pause(),De.tween&&De.tween.kill()&&(De.tween=0)),!se)){for(var b=O.length;b--;)if(O[b].scroller===T&&O[b]!==l)return;he(T,"resize",Lr),se||he(T,"scroll",pr)}},l.kill=function(m,E){l.disable(m,E),D&&!E&&D.kill(),c&&delete Qn[c];var b=O.indexOf(l);b>=0&&O.splice(b,1),b===He&&gn>0&&He--,b=0,O.forEach(function(S){return S.scroller===l.scroller&&(b=1)}),b||Fe||(l.scroll.rec=0),r&&(r.scrollTrigger=null,m&&r.revert({kill:!1}),E||r.kill()),Ye&&[Ye,Ve,u,qe].forEach(function(S){return S.parentNode&&S.parentNode.removeChild(S)}),Xr===l&&(Xr=0),d&&(Te&&(Te.uncache=1),b=0,O.forEach(function(S){return S.pin===d&&b++}),b||(Te.spacer=0)),t.onKill&&t.onKill(l)},O.push(l),l.enable(!1,!1),Ct&&Ct(l),r&&r.add&&!G){var N=l.update;l.update=function(){l.update=N,M.cache++,L||Z||l.refresh()},_.delayedCall(.01,l.update),G=.01,L=Z=0}else l.refresh();d&&Cs()},i.register=function(t){return hr||(_=t||so(),oo()&&window.document&&i.enable(),hr=Mr),hr},i.defaults=function(t){if(t)for(var r in t)sn[r]=t[r];return sn},i.disable=function(t,r){Mr=0,O.forEach(function(a){return a[r?"kill":"disable"](t)}),he(R,"wheel",pr),he(B,"scroll",pr),clearInterval(en),he(B,"touchcancel",gt),he(F,"touchstart",gt),nn(he,B,"pointerdown,touchstart,mousedown",Ai),nn(he,B,"pointerup,touchend,mouseup",Oi),yn.kill(),rn(he);for(var o=0;o<M.length;o+=3)on(he,M[o],M[o+1]),on(he,M[o],M[o+2])},i.enable=function(){if(R=window,B=document,Ke=B.documentElement,F=B.body,_&&(Vr=_.utils.toArray,Fr=_.utils.clamp,Kn=_.core.context||gt,Dn=_.core.suppressOverwrites||gt,ni=R.history.scrollRestoration||"auto",Jn=R.pageYOffset||0,_.core.globals("ScrollTrigger",i),F)){Mr=1,xr=document.createElement("div"),xr.style.height="100vh",xr.style.position="absolute",mo(),_s(),ee.register(_),i.isTouch=ee.isTouch,Ft=ee.isTouch&&/(iPad|iPhone|iPod|Mac)/g.test(navigator.userAgent),Gn=ee.isTouch===1,ge(R,"wheel",pr),ri=[R,B,Ke,F],_.matchMedia?(i.matchMedia=function(f){var h=_.matchMedia(),v;for(v in f)h.add(v,f[v]);return h},_.addEventListener("matchMediaInit",function(){return ci()}),_.addEventListener("matchMediaRevert",function(){return ho()}),_.addEventListener("matchMedia",function(){er(0,1),ar("matchMedia")}),_.matchMedia().add("(orientation: portrait)",function(){return Nn(),Nn})):console.warn("Requires GSAP 3.11.0 or later"),Nn(),ge(B,"scroll",pr);var t=F.hasAttribute("style"),r=F.style,o=r.borderTopStyle,a=_.core.Animation.prototype,s,c;for(a.revert||Object.defineProperty(a,"revert",{value:function(){return this.time(-.01,!0)}}),r.borderTopStyle="solid",s=St(F),ue.m=Math.round(s.top+ue.sc())||0,$e.m=Math.round(s.left+$e.sc())||0,o?r.borderTopStyle=o:r.removeProperty("border-top-style"),t||(F.setAttribute("style",""),F.removeAttribute("style")),en=setInterval(Mi,250),_.delayedCall(.5,function(){return tn=0}),ge(B,"touchcancel",gt),ge(F,"touchstart",gt),nn(ge,B,"pointerdown,touchstart,mousedown",Ai),nn(ge,B,"pointerup,touchend,mouseup",Oi),Wn=_.utils.checkPrefix("transform"),mn.push(Wn),hr=Ae(),yn=_.delayedCall(.2,er).pause(),gr=[B,"visibilitychange",function(){var f=R.innerWidth,h=R.innerHeight;B.hidden?(Ci=f,Ei=h):(Ci!==f||Ei!==h)&&Lr()},B,"DOMContentLoaded",er,R,"load",er,R,"resize",Lr],rn(ge),O.forEach(function(f){return f.enable(0,1)}),c=0;c<M.length;c+=3)on(he,M[c],M[c+1]),on(he,M[c],M[c+2])}},i.config=function(t){"limitCallbacks"in t&&(jn=!!t.limitCallbacks);var r=t.syncInterval;r&&clearInterval(en)||(en=r)&&setInterval(Mi,r),"ignoreMobileResize"in t&&(Gn=i.isTouch===1&&t.ignoreMobileResize),"autoRefreshEvents"in t&&(rn(he)||rn(ge,t.autoRefreshEvents||"none"),ro=(t.autoRefreshEvents+"").indexOf("resize")===-1)},i.scrollerProxy=function(t,r){var o=Xe(t),a=M.indexOf(o),s=or(o);~a&&M.splice(a,s?6:2),r&&(s?_t.unshift(R,r,F,r,Ke,r):_t.unshift(o,r))},i.clearMatchMedia=function(t){O.forEach(function(r){return r._ctx&&r._ctx.query===t&&r._ctx.kill(!0,!0)})},i.isInViewport=function(t,r,o){var a=(Ge(t)?Xe(t):t).getBoundingClientRect(),s=a[o?rr:nr]*r||0;return o?a.right-s>0&&a.left+s<R.innerWidth:a.bottom-s>0&&a.top+s<R.innerHeight},i.positionInViewport=function(t,r,o){Ge(t)&&(t=Xe(t));var a=t.getBoundingClientRect(),s=a[o?rr:nr],c=r==null?s/2:r in wn?wn[r]*s:~r.indexOf("%")?parseFloat(r)*s/100:parseFloat(r)||0;return o?(a.left+c)/R.innerWidth:(a.top+c)/R.innerHeight},i.killAll=function(t){if(O.slice(0).forEach(function(o){return o.vars.id!=="ScrollSmoother"&&o.kill()}),t!==!0){var r=sr.killAll||[];sr={},r.forEach(function(o){return o()})}},i}();P.version="3.13.0";P.saveStyles=function(i){return i?Vr(i).forEach(function(e){if(e&&e.style){var n=We.indexOf(e);n>=0&&We.splice(n,5),We.push(e,e.style.cssText,e.getBBox&&e.getAttribute("transform"),_.core.getCache(e),Kn())}}):We};P.revert=function(i,e){return ci(!i,e)};P.create=function(i,e){return new P(i,e)};P.refresh=function(i){return i?Lr(!0):(hr||P.register())&&er(!0)};P.update=function(i){return++M.cache&&At(i===!0?2:0)};P.clearScrollMemory=go;P.maxScroll=function(i,e){return mt(i,e?$e:ue)};P.getScrollFunc=function(i,e){return Yt(Xe(i),e?$e:ue)};P.getById=function(i){return Qn[i]};P.getAll=function(){return O.filter(function(i){return i.vars.id!=="ScrollSmoother"})};P.isScrolling=function(){return!!it};P.snapDirectional=li;P.addEventListener=function(i,e){var n=sr[i]||(sr[i]=[]);~n.indexOf(e)||n.push(e)};P.removeEventListener=function(i,e){var n=sr[i],t=n&&n.indexOf(e);t>=0&&n.splice(t,1)};P.batch=function(i,e){var n=[],t={},r=e.interval||.016,o=e.batchMax||1e9,a=function(f,h){var v=[],g=[],d=_.delayedCall(r,function(){h(v,g),v=[],g=[]}).pause();return function(y){v.length||d.restart(!0),v.push(y.trigger),g.push(y),o<=v.length&&d.progress(1)}},s;for(s in e)t[s]=s.substr(0,2)==="on"&&Oe(e[s])&&s!=="onRefreshInit"?a(s,e[s]):e[s];return Oe(o)&&(o=o(),ge(P,"refresh",function(){return o=e.batchMax()})),Vr(i).forEach(function(c){var f={};for(s in t)f[s]=t[s];f.trigger=c,n.push(P.create(f))}),n};var Hi=function(e,n,t,r){return n>r?e(r):n<0&&e(0),t>r?(r-n)/(t-n):t<0?n/(n-t):1},Fn=function i(e,n){n===!0?e.style.removeProperty("touch-action"):e.style.touchAction=n===!0?"auto":n?"pan-"+n+(ee.isTouch?" pinch-zoom":""):"none",e===Ke&&i(F,n)},un={auto:1,scroll:1},Os=function(e){var n=e.event,t=e.target,r=e.axis,o=(n.changedTouches?n.changedTouches[0]:n).target,a=o._gsap||_.core.getCache(o),s=Ae(),c;if(!a._isScrollT||s-a._isScrollT>2e3){for(;o&&o!==F&&(o.scrollHeight<=o.clientHeight&&o.scrollWidth<=o.clientWidth||!(un[(c=nt(o)).overflowY]||un[c.overflowX]));)o=o.parentNode;a._isScroll=o&&o!==t&&!or(o)&&(un[(c=nt(o)).overflowY]||un[c.overflowX]),a._isScrollT=s}(a._isScroll||r==="x")&&(n.stopPropagation(),n._gsapAllow=!0)},vo=function(e,n,t,r){return ee.create({target:e,capture:!0,debounce:!1,lockAxis:!0,type:n,onWheel:r=r&&Os,onPress:r,onDrag:r,onScroll:r,onEnable:function(){return t&&ge(B,ee.eventTypes[0],$i,!1,!0)},onDisable:function(){return he(B,ee.eventTypes[0],$i,!0)}})},Ps=/(input|label|select|textarea)/i,Fi,$i=function(e){var n=Ps.test(e.target.tagName);(n||Fi)&&(e._gsapAllow=!0,Fi=n)},Rs=function(e){Qt(e)||(e={}),e.preventDefault=e.isNormalizer=e.allowClicks=!0,e.type||(e.type="wheel,touch"),e.debounce=!!e.debounce,e.id=e.id||"normalizer";var n=e,t=n.normalizeScrollX,r=n.momentum,o=n.allowNestedScroll,a=n.onRelease,s,c,f=Xe(e.target)||Ke,h=_.core.globals().ScrollSmoother,v=h&&h.get(),g=Ft&&(e.content&&Xe(e.content)||v&&e.content!==!1&&!v.smooth()&&v.content()),d=Yt(f,ue),y=Yt(f,$e),Y=1,I=(ee.isTouch&&R.visualViewport?R.visualViewport.scale*R.visualViewport.width:R.outerWidth)/R.innerWidth,W=0,X=Oe(r)?function(){return r(s)}:function(){return r||2.8},_e,A,Qe=vo(f,e.type,!0,o),V=function(){return A=!1},C=gt,Pe=gt,ze=function(){c=mt(f,ue),Pe=Fr(Ft?1:0,c),t&&(C=Fr(0,mt(f,$e))),_e=ir},x=function(){g._gsap.y=Dr(parseFloat(g._gsap.y)+d.offset)+"px",g.style.transform="matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, "+parseFloat(g._gsap.y)+", 0, 1)",d.offset=d.cacheID=0},fe=function(){if(A){requestAnimationFrame(V);var te=Dr(s.deltaY/2),re=Pe(d.v-te);if(g&&re!==d.v+d.offset){d.offset=re-d.v;var l=Dr((parseFloat(g&&g._gsap.y)||0)-d.offset);g.style.transform="matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, "+l+", 0, 1)",g._gsap.y=l+"px",d.cacheID=M.cache,At()}return!0}d.offset&&x(),A=!0},T,vt,se,Re,Me=function(){ze(),T.isActive()&&T.vars.scrollY>c&&(d()>c?T.progress(1)&&d(c):T.resetTo("scrollY",c))};return g&&_.set(g,{y:"+=0"}),e.ignoreCheck=function($){return Ft&&$.type==="touchmove"&&fe()||Y>1.05&&$.type!=="touchstart"||s.isGesturing||$.touches&&$.touches.length>1},e.onPress=function(){A=!1;var $=Y;Y=Dr((R.visualViewport&&R.visualViewport.scale||1)/I),T.pause(),$!==Y&&Fn(f,Y>1.01?!0:t?!1:"x"),vt=y(),se=d(),ze(),_e=ir},e.onRelease=e.onGestureStart=function($,te){if(d.offset&&x(),!te)Re.restart(!0);else{M.cache++;var re=X(),l,ae;t&&(l=y(),ae=l+re*.05*-$.velocityX/.227,re*=Hi(y,l,ae,mt(f,$e)),T.vars.scrollX=C(ae)),l=d(),ae=l+re*.05*-$.velocityY/.227,re*=Hi(d,l,ae,mt(f,ue)),T.vars.scrollY=Pe(ae),T.invalidate().duration(re).play(.01),(Ft&&T.vars.scrollY>=c||l>=c-1)&&_.to({},{onUpdate:Me,duration:re})}a&&a($)},e.onWheel=function(){T._ts&&T.pause(),Ae()-W>1e3&&(_e=0,W=Ae())},e.onChange=function($,te,re,l,ae){if(ir!==_e&&ze(),te&&t&&y(C(l[2]===te?vt+($.startX-$.x):y()+te-l[1])),re){d.offset&&x();var Vt=ae[2]===re,Ot=Vt?se+$.startY-$.y:d()+re-ae[1],Je=Pe(Ot);Vt&&Ot!==Je&&(se+=Je-Ot),d(Je)}(re||te)&&At()},e.onEnable=function(){Fn(f,t?!1:"x"),P.addEventListener("refresh",Me),ge(R,"resize",Me),d.smooth&&(d.target.style.scrollBehavior="auto",d.smooth=y.smooth=!1),Qe.enable()},e.onDisable=function(){Fn(f,!0),he(R,"resize",Me),P.removeEventListener("refresh",Me),Qe.kill()},e.lockAxis=e.lockAxis!==!1,s=new ee(e),s.iOS=Ft,Ft&&!d()&&d(1),Ft&&_.ticker.add(gt),Re=s._dc,T=_.to(s,{ease:"power4",paused:!0,inherit:!1,scrollX:t?"+=0.1":"+=0",scrollY:"+=0.1",modifiers:{scrollY:_o(d,d(),function(){return T.pause()})},onUpdate:At,onComplete:Re.vars.onComplete}),s};P.sort=function(i){if(Oe(i))return O.sort(i);var e=R.pageYOffset||0;return P.getAll().forEach(function(n){return n._sortY=n.trigger?e+n.trigger.getBoundingClientRect().top:n.start+R.innerHeight}),O.sort(i||function(n,t){return(n.vars.refreshPriority||0)*-1e6+(n.vars.containerAnimation?1e6:n._sortY)-((t.vars.containerAnimation?1e6:t._sortY)+(t.vars.refreshPriority||0)*-1e6)})};P.observe=function(i){return new ee(i)};P.normalizeScroll=function(i){if(typeof i>"u")return Ne;if(i===!0&&Ne)return Ne.enable();if(i===!1){Ne&&Ne.kill(),Ne=i;return}var e=i instanceof ee?i:Rs(i);return Ne&&Ne.target===e.target&&Ne.kill(),or(e.target)&&(Ne=e),e};P.core={_getVelocityProp:Un,_inputObserver:vo,_scrollers:M,_proxies:_t,bridge:{ss:function(){it||ar("scrollStart"),it=Ae()},ref:function(){return ke}}};so()&&_.registerPlugin(P);Tn.registerPlugin(P);const Ms=()=>{const i=H.useRef(null);return H.useEffect(()=>{if(!i.current)return;const e=i.current;return Tn.to(e,{scaleX:1,transformOrigin:"left center",ease:"none",scrollTrigger:{trigger:document.body,start:"top top",end:"bottom bottom",scrub:!0}}),()=>{P.getAll().forEach(n=>{n.trigger===document.body&&n.kill()})}},[]),p.jsx("div",{className:"fixed top-0 left-0 w-full h-1 bg-gray-200 z-50",children:p.jsx("div",{ref:i,className:"h-full bg-gradient-to-r from-primary-500 to-purple-500 transform scale-x-0"})})};Tn.registerPlugin(P);const Ds=()=>{const[i,e]=H.useState(!1),[n,t]=H.useState(!1),r=H.useRef(null);H.useEffect(()=>{const h=()=>e(window.scrollY>300);return window.addEventListener("scroll",h),h(),()=>window.removeEventListener("scroll",h)},[]);const o=()=>{Tn.to(window,{duration:1,scrollTo:{y:0},ease:"power2.out"})},a={hidden:{scale:0,opacity:0,rotate:-180},visible:{scale:1,opacity:1,rotate:0,transition:{type:"spring",stiffness:260,damping:20}}},s={hidden:{opacity:0,scale:.8},visible:{opacity:1,scale:1,transition:{staggerChildren:.1}}},c={hidden:{opacity:0,y:20,scale:.8},visible:{opacity:1,y:0,scale:1,transition:{type:"spring",stiffness:300,damping:20}}},f=[{icon:ls,label:"View Map",action:()=>console.log("Open map"),color:"bg-green-500 hover:bg-green-600"},{icon:ss,label:"Contact Us",action:()=>console.log("Open contact"),color:"bg-blue-500 hover:bg-blue-600"},{icon:wi,label:"Back to Top",action:o,color:"bg-primary-500 hover:bg-primary-600"}];return p.jsx(fi,{children:i&&p.jsxs(me.div,{ref:r,variants:a,initial:"hidden",animate:"visible",exit:"hidden",className:"fixed bottom-6 right-6 z-50",children:[p.jsx(fi,{children:n&&p.jsx(me.div,{variants:s,initial:"hidden",animate:"visible",exit:"hidden",className:"absolute bottom-16 right-0 space-y-3",children:f.slice(0,-1).map((h,v)=>p.jsx(me.button,{variants:c,onClick:h.action,className:`flex items-center justify-center w-12 h-12 rounded-full text-white shadow-lg transition-all duration-200 ${h.color}`,whileHover:{scale:1.1},whileTap:{scale:.9},title:h.label,children:p.jsx(h.icon,{className:"h-5 w-5"})},v))})}),p.jsx(me.button,{onClick:()=>{n?t(!1):o()},onMouseEnter:()=>t(!0),onMouseLeave:()=>t(!1),className:"flex items-center justify-center w-14 h-14 bg-primary-600 hover:bg-primary-700 text-white rounded-full shadow-lg transition-all duration-200",whileHover:{scale:1.1},whileTap:{scale:.9},children:p.jsx(me.div,{animate:{rotate:n?45:0},transition:{duration:.2},children:p.jsx(wi,{className:"h-6 w-6"})})}),p.jsx(me.div,{className:"absolute inset-0 bg-primary-400 rounded-full -z-10",initial:{scale:1,opacity:.3},animate:{scale:[1,1.5,1],opacity:[.3,0,.3]},transition:{duration:2,repeat:1/0,ease:"easeInOut"}})]})})},js=()=>{const i=()=>{const e=document.getElementById("main-content");e&&(e.focus(),e.scrollIntoView())};return p.jsx("button",{onClick:i,className:"sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 bg-primary-600 text-white px-4 py-2 rounded-md font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2",onKeyDown:e=>{(e.key==="Enter"||e.key===" ")&&(e.preventDefault(),i())},children:"Skip to main content"})},Ls=({children:i})=>p.jsxs("div",{className:"min-h-screen flex flex-col",children:[p.jsx(js,{}),p.jsx(Ms,{}),p.jsx(fs,{}),p.jsx("main",{id:"main-content",className:"flex-grow",tabIndex:-1,children:i}),p.jsx(ds,{}),p.jsx(Ds,{})]}),Is=()=>p.jsx("div",{className:"flex items-center justify-center min-h-screen",children:p.jsx(me.div,{className:"loading-spinner",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0}})}),Ns=xe.lazy(()=>Xt(()=>import("./Home-YUC6wHC2.js"),__vite__mapDeps([0,1,2,3,4,5]))),Hs=xe.lazy(()=>Xt(()=>import("./Destinations-DQD4JVHA.js"),__vite__mapDeps([6,1,2,3,5,4,7,8,9]))),Fs=xe.lazy(()=>Xt(()=>import("./DestinationDetail-CZutERg6.js"),__vite__mapDeps([10,1,2,3,5,4,8,9,11]))),$s=xe.lazy(()=>Xt(()=>import("./Activities-BZeeRuUO.js"),__vite__mapDeps([12,1,2,4,7,13,8,9,3]))),zs=xe.lazy(()=>Xt(()=>import("./Culture-fhcqi9hV.js"),__vite__mapDeps([14,1,2,15,3]))),Bs=xe.lazy(()=>Xt(()=>import("./TravelTips-D7-Oa4NF.js"),__vite__mapDeps([16,1,2,15,8,13,11,3]))),Ys=xe.lazy(()=>Xt(()=>import("./Contact-B6OcraIi.js"),__vite__mapDeps([17,1,2,15,8,9,3]))),Xs=xe.lazy(()=>Xt(()=>import("./NotFound-Dm9N144W.js"),__vite__mapDeps([18,1,2,3]))),Nt={initial:{opacity:0,y:20},in:{opacity:1,y:0},out:{opacity:0,y:-20}},Ht={duration:.5};function Vs(){return p.jsx(Ls,{children:p.jsx(H.Suspense,{fallback:p.jsx(Is,{}),children:p.jsxs(To,{children:[p.jsx(It,{path:"/",element:p.jsx(me.div,{initial:"initial",animate:"in",exit:"out",variants:Nt,transition:Ht,children:p.jsx(Ns,{})})}),p.jsx(It,{path:"/destinations",element:p.jsx(me.div,{initial:"initial",animate:"in",exit:"out",variants:Nt,transition:Ht,children:p.jsx(Hs,{})})}),p.jsx(It,{path:"/destinations/:id",element:p.jsx(me.div,{initial:"initial",animate:"in",exit:"out",variants:Nt,transition:Ht,children:p.jsx(Fs,{})})}),p.jsx(It,{path:"/activities",element:p.jsx(me.div,{initial:"initial",animate:"in",exit:"out",variants:Nt,transition:Ht,children:p.jsx($s,{})})}),p.jsx(It,{path:"/culture",element:p.jsx(me.div,{initial:"initial",animate:"in",exit:"out",variants:Nt,transition:Ht,children:p.jsx(zs,{})})}),p.jsx(It,{path:"/travel-tips",element:p.jsx(me.div,{initial:"initial",animate:"in",exit:"out",variants:Nt,transition:Ht,children:p.jsx(Bs,{})})}),p.jsx(It,{path:"/contact",element:p.jsx(me.div,{initial:"initial",animate:"in",exit:"out",variants:Nt,transition:Ht,children:p.jsx(Ys,{})})}),p.jsx(It,{path:"*",element:p.jsx(me.div,{initial:"initial",animate:"in",exit:"out",variants:Nt,transition:Ht,children:p.jsx(Xs,{})})})]})})})}ko.createRoot(document.getElementById("root")).render(p.jsx(xe.StrictMode,{children:p.jsx(Ui,{children:p.jsx(Co,{children:p.jsx(Vs,{})})})}));export{Ks as H,P as S};
