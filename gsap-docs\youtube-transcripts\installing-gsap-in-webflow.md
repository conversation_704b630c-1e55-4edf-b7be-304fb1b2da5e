# Installing GSAP in Webflow - YouTube

[Music]
Getting GAP installed in Web Flow is now
easier than ever. Thanks to Web Flow
support, GSAP and all of our plugins are
now free for everyone, whether you're
using Web Flow or not. You can also now
add GSAP directly in Web Flow settings,
including the previously paid plugins.
So, no more struggling to self-host club
plugins or fuffing about with messy text
file hacks. You can even preview your
custom code without publishing your
site. Let's step through the process
together. So, we've got a little Web
Flow demo site here just with some text
and some little shapes. So, let's load
in GSAP and the split text plugin and
then we'll animate these elements. So,
first we're going to go to the settings
panel down here. We'll click Gap and
you'll see we have a little toggle here
to switch Gap on and off. When we switch
it to on, Web Flow adds the core GAP
library to your site so it's ready to
use. This is all you need to make most
animations. But if you want extra
capabilities like text splitting or
scroll animations, you can head down to
this section and include the plugins
that you need. Make sure that you only
include the plugins that you're actually
using or you'll be loading all of that
extra JavaScript in for no reason and
losing out on performance. So, we want
to animate the text element on our demo
site. So, we're going to add split text
so we can chop up our heading into
letters and then animate them in. And
that's it. Gap and split text are loaded
in for us. So, let's go back to our site
and then we'll write some GSA code. So,
before we write any JavaScript, I just
want to take a look at the markup here.
So, we've added classes to all of these
elements. We've got a class of text on
the heading and then spiral and star for
the shapes. Some Web Flow folks prefer
to use IDs or even data attributes for
animation so that they can keep the
classes purely for styling, which is a
nice approach, too, but I'm just doing
this for simplicity. So, let's go to our
page settings. We're going to scroll
down to the section that says before
body. So, this is code that's going to
be put at the bottom of your web page
after all of your content and before the
closing body tag. And now we're going to
pop some code in here. First, we're
going to create a script tag to wrap our
code in. And then inside that, we're
going to add a DOM content loaded event
listener. This is basically just
checking that all of our elements are
loaded in and ready to be animated
before we write any animation code.
Inside this event listener, we're going
to add a little GSA tween to spin the
star around. No worries if you forget.
There's a snippet in the Gap and Web
Flow installation guide. I'll put a link
below in the YouTube description. Back
to our site. Okay, let's take a look and
see if we have some animation. We're
going to hit this preview button and
make sure that we have enable custom
code toggled on. And there we go, an
animation. Perfect. All right, let's get
split text working. So, if you go to the
Gap docs and go to whichever plugin you
want to use, there are these handy quick
start guides at the top. Um, and they've
got a little code snippet to get you
going. So, the first bit here registers
the plug-in with Gap's core. And then
this bit below is demonstrating a simple
usage of split text. So, let's copy both
of these and then we'll add them into
our custom code block.
So this class right here is already
correct. We named our heading element
text. But make sure that you target your
element by the right class. Let's check
out the animation then. So we're going
to do the same again. Hit preview.
Toggle is on. And we have animation.
Perfect. If you hit any walls with this
or you struggle at all, pop over to the
Gap forums and we'll give you a hand. I
hope you have fun and I'll see you next
time.
[Music]