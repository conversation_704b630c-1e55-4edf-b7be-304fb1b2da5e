{"hash": "1ae1e3bc", "configHash": "4c90ef46", "lockfileHash": "1a54196e", "browserHash": "017c72b9", "optimized": {"gsap": {"src": "../../gsap/index.js", "file": "gsap.js", "fileHash": "efd140ab", "needsInterop": false}, "framer-motion": {"src": "../../framer-motion/dist/es/index.mjs", "file": "framer-motion.js", "fileHash": "3ac86c81", "needsInterop": false}, "react-router-dom": {"src": "../../react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "0b76566a", "needsInterop": false}, "@headlessui/react": {"src": "../../@headlessui/react/dist/headlessui.esm.js", "file": "@headlessui_react.js", "fileHash": "e9206baa", "needsInterop": false}, "@heroicons/react/24/outline": {"src": "../../@heroicons/react/24/outline/esm/index.js", "file": "@heroicons_react_24_outline.js", "fileHash": "acaf6f65", "needsInterop": false}, "react-helmet-async": {"src": "../../react-helmet-async/lib/index.esm.js", "file": "react-helmet-async.js", "fileHash": "5e4d937f", "needsInterop": false}, "react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "1e62b8df", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "359f08bd", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "310173c7", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "f8da68f0", "needsInterop": true}, "gsap/ScrollTrigger": {"src": "../../gsap/ScrollTrigger.js", "file": "gsap_ScrollTrigger.js", "fileHash": "ccef9325", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "bcaea0b9", "needsInterop": true}}, "chunks": {"chunk-S725DACQ": {"file": "chunk-S725DACQ.js"}, "chunk-KDCVS43I": {"file": "chunk-KDCVS43I.js"}, "chunk-RLJ2RCJQ": {"file": "chunk-RLJ2RCJQ.js"}, "chunk-DC5AMYBS": {"file": "chunk-DC5AMYBS.js"}}}