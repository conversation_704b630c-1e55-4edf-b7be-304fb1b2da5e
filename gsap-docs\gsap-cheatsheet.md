# GSAP Cheat Sheet

This document contains a comprehensive cheat sheet for GSAP (GreenSock Animation Platform) based on the official documentation.

## Installation

```javascript
// Import and register GSAP
import { gsap } from 'gsap';

// Import a plugin
import { DrawSVGPlugin } from 'gsap/DrawSVGPlugin';

// Register the plugin
gsap.registerPlugin(DrawSVGPlugin);
```

## Basics

```javascript
// "to" tween - animate to provided values
// https://gsap.com/docs/v3/GSAP/gsap.to()/
gsap.to(".selector", {
  x: "100", // any properties (not limited to CSS)
  backgroundColor: "red", // camelCase
  duration: 1, // seconds
  delay: 0.5,
  ease: "power2.inOut",
  stagger: 0.1, // stagger start times
  paused: true, // default is false
  overwrite: "auto", // default is false
  repeat: 2, // number of repeats (-1 for infinite)
  repeatDelay: 1, // seconds between repeats
  repeatRefresh: true, // invalidates on each repeat
  yoyo: true, // if true > A-B-B-A, if false > A-B-A-B
  yoyoEase: true, // or ease like "power2"
  immediateRender: false,
  onComplete: () => {
    console.log("finished")
  }
});

// "from" tween - animate from provided values
// https://gsap.com/docs/v3/GSAP/gsap.from()/
gsap.from('.selector', {
  fromVars
});

// "fromTo" tween (define both start and end values)
// https://gsap.com/docs/v3/GSAP/gsap.fromTo()/
gsap.fromTo('.selector', { fromVars }, { toVars });
// special properties (duration, ease, etc.) go in toVars

// set values immediately (no animation)
// https://gsap.com/docs/v3/GSAP/gsap.set()/
gsap.set('.selector', { toVars });
```

## Timelines

```javascript
// Create a timeline
// https://gsap.com/docs/v3/GSAP/gsap.timeline()/
let tl = gsap.timeline({
  delay: 0.5,
  paused: true, // default is false
  repeat: 2, // number of repeats (-1 for infinite)
  repeatDelay: 1, // seconds between repeats
  repeatRefresh: true, // invalidates on each repeat
  yoyo: true, // if true > A-B-B-A, if false > A-B-A-B
  defaults: {
    duration: 1,
    ease: 'none' // children inherit these defaults
  },
  smoothChildTiming: true,
  autoRemoveChildren: true,
  onComplete: () => {
    console.log("finished")
  }
});

// Sequence multiple tweens
// https://gsap.com/docs/v3/GSAP/Timeline/to()/
tl.to('.selector', { duration: 1, x: 50, y: 0 })
  .to('#id', { autoAlpha: 0 })
  .to(elem, { duration: 1, backgroundColor: 'red' })
  .to([elem, elem2], { duration: 3, x: 100 });

// Position parameter (controls placement)
// https://gsap.com/resources/position-parameter/
tl.to(target, { toVars }, 0.7); // exactly 0.7 seconds into the timeline (absolute)
tl.to(target, { toVars }, '-=0.7'); // overlap with previous by 0.7 sec
tl.to(target, { toVars }, 'myLabel'); // insert at "myLabel" position
tl.to(target, { toVars }, 'myLabel+=0.2'); // 0.2 seconds after "myLabel"
tl.to(target, { toVars }, '<'); // align with start of most recently-added child
tl.to(target, { toVars }, '<0.2'); // 0.2 seconds after ^^
tl.to(target, { toVars }, '-=50%'); // overlap half of inserting animation's duration
tl.to(target, { toVars }, '<25%'); // 25% into the previous animation (from its start)
```

## Control Methods

```javascript
// Retain animation reference to control later
let anim = gsap.to(...); // or gsap.timeline(...);

// Most methods can be used as getters or setters
anim.play(); // plays forward
anim.pause();
anim.resume(); // respects direction
anim.reverse();
anim.restart();
anim.timeScale(2); // 2 = double speed, 0.5 = half speed
anim.seek(1.5); // jump to a time (in seconds) or label
anim.progress(0.5); // jump to halfway
anim.totalProgress(0.8); // includes repeats

// When used as setter, returns animation (chaining)
// Other useful methods (tween and timeline)
anim.kill(); // immediately destroy
anim.isActive(); // true if currently animating
anim.then(); // Promise
anim.invalidate(); // clear recorded start/end values
anim.eventCallback(); // get/set an event callback

// Timeline-specific methods
tl.getChildren(); // get an Array of the timeline's children
tl.clear(); // empties the timeline
tl.tweenTo(timeOrLabel, { vars }); // animate playhead to a position linearly
tl.tweenFromTo(from, to, { vars }); // with both start and end positions
```


## Eases

```javascript
// See greensock.com/ease-visualizer
// https://gsap.com/docs/v3/Eases/
const ease = 'none'; // no ease (same as "linear")

// Basic core eases
gsap.to(elem, {
  ease: 'power1.inOut', // also available: power2, power3, power4, circ, expo, sine
  duration: 1
});

// Each has .in, .out, and .inOut extensions
// i.e. "power1.inOut"

// Expressive core eases
gsap.to(elem, {
  ease: 'elastic',
  duration: 1
});

// Available expressive core eases:
// 'elastic', 'back', 'bounce', 'steps(n)'

// In EasePack plugin (not core)
gsap.to(elem, {
  ease: 'rough',
  duration: 1
});

// Available EasePack plugins:
// 'rough', 'slow', 'expoScale(1, 2)'

// Expressive plugin eases
// CustomEase, CustomWiggle, CustomBounce
```


## ScrollTrigger

```javascript
// https://gsap.com/docs/v3/Plugins/ScrollTrigger/
gsap.to('.selector', {
  scrollTrigger: {
    trigger: ".selector", // selector or element
    start: "top center", // [trigger] [scroller] positions
    end: "20px 80%", // [trigger] [scroller] positions
    // or relative amount: "+=500"
    scrub: true, // or time (in seconds) to catch up
    pin: true, // or selector or element to pin
    markers: true, // only during development!
    toggleActions: "play pause resume reset", // other actions: complete reverse none
    fastScrollEnd: true, // or velocity number
    containerAnimation: tween, // linear animation
    id: "my-id",
    anticipatePin: 1, // may help avoid jump
    snap: {
      snapTo: 1, // progress increment or "labels" or function or Array
      duration: 0.5,
      directional: true,
      ease: "power3"
    },
    pinReparent: true, // moves to documentElement during pin
    pinSpacing: false,
    pinType: "transform", // or "fixed"
    pinnedContainer: ".selector",
    preventOverlaps: true, // or arbitrary string
    once: true,
    endTrigger: ".selector", // selector or element
    horizontal: true, // switches mode
    invalidateOnRefresh: true, // clears start values on refresh
    refreshPriority: 1, // influence refresh order
    onEnter: callback,
    onLeave: callback,
    onEnterBack: callback,
    onLeaveBack: callback,
    onUpdate: callback,
    onToggle: callback,
    onRefresh: callback,
    onRefreshInit: callback,
    onScrubComplete: callback
  }
});
```

## Plugins

```javascript
// Register GSAP plugins (once) before using them
// https://gsap.com/docs/v3/GSAP/gsap.registerPlugin()/
gsap.registerPlugin(Draggable, TextPlugin);

// Available plugins:
// Draggable, DrawSVGPlugin, EaselPlugin, Flip, GSDevTools, InertiaPlugin, MorphSVGPlugin,
// MotionPathPlugin, MotionPathHelper, Observer, Physics2DPlugin, PhysicsPropsPlugin,
// PixiPlugin, ScrambleTextPlugin, ScrollToPlugin, ScrollTrigger, ScrollSmoother, SplitText, TextPlugin
```

## Utility Methods

```javascript
// Accessible through gsap.utils.foo()

// Get the current value of a property
gsap.getProperty("#id", "x"); // 20
gsap.getProperty("#id", "x", "px"); // "20px"

// Set GSAP's global tween defaults
gsap.defaults({
  ease: "power2.in",
  duration: 1
});

// Configure GSAP's non-tween-related settings
gsap.config({
  autoSleep: 60,
  force3D: false,
  nullTargetWarn: false,
  trialWarn: false,
  units: {
    left: "%",
    top: "%",
    rotation: "rad"
  }
});

// Register an effect for reuse
gsap.registerEffect({
  name: "fade",
  effect: (targets, config) => {
    return gsap.to(targets, {
      duration: config.duration,
      opacity: 0
    });
  },
  defaults: { duration: 2 },
  extendTimeline: true
});

// Now we can use it like this
gsap.effects.fade(".box");

// Or directly on timelines
tl.fade(".box", { duration: 3 });

// Add listener with gsap.ticker
// https://gsap.com/docs/v3/GSAP/gsap.ticker()/
gsap.ticker.add(myFunction);

function myFunction(time, deltaTime, frame) {
  // Executes on every tick after the core engine updates
}

// To remove the listener later...
gsap.ticker.remove(myFunction);

// Faster way to repeatedly set property than .set()
let setX = gsap.quickSetter("#id", "x", "px");
document.addEventListener("mousemove", e => {
  setX(e.clientX);
});

// QuickTo - for animation!
let xTo = gsap.quickTo("#id", "x", { duration: 0.4, ease: "power3" });
document.addEventListener("mousemove", e => {
  xTo(e.pageX);
});
```

## Additional Resources

1. [GSAP Official Documentation](https://gsap.com/docs/v3)
2. [ScrollTrigger Documentation](https://gsap.com/docs/v3/Plugins/ScrollTrigger/)
3. [GSAP GitHub Repository](https://github.com/greensock/GreenSock-JS)
4. [GSAP Community Forums](https://gsap.com/community/)
5. [GSAP Cheat Sheet Source](https://gsap.com/cheatsheet/)
6. [GSAP Utility Methods](https://gsap.com/docs/v3/GSAP/UtilityMethods)
7. [GSAP Installation Guide](https://gsap.com/docs/v3/Installation)
8. [GSAP Showreel 2023](https://gsap.com/docs/v3/Showreel-2023/)
9. [GSAP Learning Center](https://gsap.com/resources/get-started/)
10. [GSAP & Webflow Integration](https://gsap.com/pricing)

![GSAP Logo](https://gsap.com/img/logo.svg)

> This documentation was extracted from the GSAP website using Playwright and organized into a single markdown file for easy reference.
