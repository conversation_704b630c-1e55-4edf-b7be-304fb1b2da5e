# Sri Lanka Tourist Guide 🇱🇰

A modern, interactive, and comprehensive tourist guide for Sri Lanka built with cutting-edge web technologies. This application provides travelers with detailed information about destinations, activities, culture, and travel tips for exploring the Pearl of the Indian Ocean.

## ✨ Features

### 🏝️ Core Functionality
- **Interactive Destination Explorer** - Browse 15+ carefully curated destinations with detailed information
- **Activity Recommendations** - Discover 20+ activities across different categories (adventure, cultural, wildlife, etc.)
- **Cultural Heritage Guide** - Learn about Sri Lanka's rich 2,500-year history and traditions
- **Comprehensive Travel Tips** - Essential information for planning your trip
- **Smart Search & Filtering** - Find destinations and activities by category, province, difficulty, and price
- **Detailed Destination Pages** - In-depth information including transportation, accommodation, and tips

### 🎨 User Experience
- **Responsive Design** - Optimized for desktop, tablet, and mobile devices
- **Professional Animations** - Smooth GSAP and Framer Motion animations
- **Accessibility First** - WCAG 2.1 AA compliant with screen reader support
- **Performance Optimized** - Fast loading with code splitting and lazy loading
- **Modern UI/UX** - Clean, intuitive interface with excellent visual hierarchy

### 🚀 Technical Features
- **Progressive Web App Ready** - Optimized for performance and user experience
- **SEO Optimized** - Proper meta tags, structured data, and social sharing
- **Error Handling** - Comprehensive error boundaries and user feedback
- **Type Safety** - Full TypeScript implementation for reliability
- **Modern Build System** - Vite for fast development and optimized production builds

## 🛠️ Tech Stack

### Frontend Framework
- **React 18** - Latest React with concurrent features
- **TypeScript** - Full type safety and developer experience
- **Vite** - Lightning-fast build tool and development server

### Styling & UI
- **Tailwind CSS** - Utility-first CSS framework
- **Headless UI** - Unstyled, accessible UI components
- **Heroicons** - Beautiful hand-crafted SVG icons

### Animation Libraries
- **GSAP 3.13** - Professional-grade animations and scroll triggers
- **Framer Motion 12** - React-specific animations and gestures
- **Lottie React** - After Effects animations (ready for integration)

### Routing & Navigation
- **React Router 6** - Declarative routing with data loading
- **React Helmet Async** - Document head management for SEO

### Development Tools
- **ESLint** - Code linting and quality assurance
- **Prettier** - Code formatting
- **Husky** - Git hooks for quality control

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- npm or yarn package manager

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd sri-lanka-tourist-guide
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start development server**
   ```bash
   npm run dev
   ```
   The application will be available at `http://localhost:5173`

4. **Build for production**
   ```bash
   npm run build
   ```

5. **Preview production build**
   ```bash
   npm run preview
   ```

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run build:analyze` - Build with bundle analysis
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint
- `npm run type-check` - Run TypeScript type checking

## 🎯 Key Features Implemented

### 1. Destinations (15+ Locations)
- **Cultural Sites**: Sigiriya, Anuradhapura, Polonnaruwa, Dambulla, Kandy
- **Natural Wonders**: Ella, Nuwara Eliya, Yala National Park, Horton Plains
- **Coastal Areas**: Galle, Mirissa, Unawatuna, Arugam Bay
- **Adventure Spots**: Adam's Peak, Knuckles Mountain Range

### 2. Activities (20+ Experiences)
- **Adventure**: Hiking, rock climbing, white water rafting
- **Wildlife**: Safari tours, whale watching, bird watching
- **Cultural**: Temple visits, traditional performances, cooking classes
- **Water Sports**: Surfing, diving, snorkeling
- **Wellness**: Ayurveda treatments, yoga retreats

### 3. Cultural Information
- **Historical Timeline** - 2,500 years of Sri Lankan history
- **Religious Heritage** - Buddhism, Hinduism, and other traditions
- **Arts & Crafts** - Traditional dance, music, and handicrafts
- **Festivals** - Major celebrations throughout the year
- **Cuisine** - Traditional dishes and culinary experiences

### 4. Travel Planning Tools
- **Comprehensive Travel Tips** - Visa, budget, safety, health information
- **Transportation Guide** - Getting around Sri Lanka
- **Accommodation Recommendations** - Budget to luxury options
- **Best Time to Visit** - Seasonal information and weather patterns
- **Emergency Information** - Important contacts and safety tips

## 🎨 Animation & Performance

### Animation Libraries Integration
- **GSAP ScrollTrigger** - Scroll-based animations with performance optimization
- **Framer Motion** - Component animations, page transitions, and micro-interactions
- **Custom Animation Components** - Reusable animated sections with configurable options

### Performance Optimizations
- **Code Splitting** - Automatic route-based code splitting
- **Lazy Loading** - Images and components loaded on demand
- **Bundle Optimization** - Optimized chunk sizes and asset loading
- **Performance Monitoring** - Real-time performance metrics tracking

### Accessibility Features
- **WCAG 2.1 AA Compliance** - Full accessibility standard compliance
- **Screen Reader Support** - Proper ARIA labels and semantic HTML
- **Keyboard Navigation** - Full keyboard accessibility
- **Reduced Motion Support** - Respects user motion preferences
- **Focus Management** - Proper focus handling for interactive elements

## 📚 Documentation

- **[Animation Guide](./ANIMATION_GUIDE.md)** - Comprehensive animation implementation guide
- **[Deployment Guide](./DEPLOYMENT.md)** - Step-by-step deployment instructions

## 🚀 Deployment Ready

The application is optimized for deployment on major platforms:
- **Netlify** (Recommended) - Automatic deployments
- **Vercel** - Optimized for React applications
- **GitHub Pages** - Free hosting option
- **Firebase Hosting** - Google's hosting platform

## 📄 License

This project is licensed under the MIT License.

---

**Built with ❤️ for travelers exploring the beautiful island of Sri Lanka** 🌴
- **Lottie React** - Complex animations from After Effects

### Routing & State
- **React Router DOM** - Client-side routing
- **React Helmet Async** - SEO and meta tag management

### Forms & Validation
- **React Hook Form** - Performant form handling
- **Zod** - TypeScript-first schema validation

## 📁 Project Structure

```
sri-lanka-tourist-guide/
├── public/                 # Static assets
├── src/
│   ├── components/        # Reusable UI components
│   ├── pages/            # Page components
│   ├── hooks/            # Custom React hooks
│   ├── utils/            # Utility functions
│   ├── data/             # Static data and content
│   ├── assets/           # Images, icons, etc.
│   ├── styles/           # Global styles and CSS
│   ├── App.tsx           # Main app component
│   └── main.tsx          # App entry point
├── index.html            # HTML template
├── vite.config.ts        # Vite configuration
├── tailwind.config.js    # Tailwind CSS configuration
└── tsconfig.json         # TypeScript configuration
```

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ 
- npm or yarn

### Installation

1. Clone the repository
```bash
git clone <repository-url>
cd sri-lanka-tourist-guide
```

2. Install dependencies
```bash
npm install
```

3. Start the development server
```bash
npm run dev
```

4. Open your browser and visit `http://localhost:5173`

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

## 🎨 Design System

### Colors
- **Primary**: Blue tones for main branding
- **Secondary**: Yellow/Gold for accents
- **Accent**: Green for highlights

### Typography
- **Headings**: Playfair Display (serif)
- **Body**: Inter (sans-serif)

### Components
- Consistent button styles
- Card components with hover effects
- Responsive navigation
- Loading states and transitions

## 📱 Responsive Design

The website is built with a mobile-first approach:
- **Mobile**: 320px - 768px
- **Tablet**: 768px - 1024px  
- **Desktop**: 1024px+

## 🎬 Animation Strategy

### GSAP Usage
- Scroll-triggered animations
- Complex timeline sequences
- Performance-optimized transforms

### Framer Motion Usage
- Component enter/exit animations
- Hover and tap interactions
- Layout animations
- Page transitions

## 🚀 Deployment

### Static Hosting (Recommended)
The app is optimized for static hosting platforms:

#### Netlify
1. Build the project: `npm run build`
2. Deploy the `dist` folder to Netlify
3. Configure redirects for SPA routing

#### Vercel
1. Connect your repository to Vercel
2. Vercel will automatically detect Vite and configure build settings
3. Deploy with automatic CI/CD

### Build Configuration
- Optimized bundle splitting
- Asset optimization
- Tree shaking for smaller bundles

## 🔧 Performance Optimizations

- **Code Splitting**: Lazy loading of route components
- **Bundle Analysis**: Separate chunks for vendor libraries
- **Image Optimization**: WebP/AVIF support
- **Animation Performance**: GPU-accelerated transforms
- **Caching**: Optimized for CDN caching

## 🌐 SEO Considerations

- React Helmet for dynamic meta tags
- Semantic HTML structure
- Proper heading hierarchy
- Alt tags for images
- Open Graph and Twitter Card support

## 🧪 Testing

Testing setup will be added in future iterations:
- Unit tests with Vitest
- Component testing with React Testing Library
- E2E tests with Playwright

## 📄 License

This project is licensed under the MIT License.

## 🤝 Contributing

Contributions are welcome! Please read the contributing guidelines before submitting PRs.

## 📞 Support

For support and questions, please contact the development team.

---

Built with ❤️ by **Tera Works**
