import React, { useEffect, useRef, useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import { ArrowUpIcon, ChatBubbleLeftIcon, MapIcon } from '@heroicons/react/24/outline'

gsap.registerPlugin(ScrollTrigger)

const FloatingActionButton: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false)
  const [isExpanded, setIsExpanded] = useState(false)
  const fabRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    // Show/hide FAB based on scroll position
    const showFab = () => setIsVisible(window.scrollY > 300)
    
    window.addEventListener('scroll', showFab)
    showFab() // Check initial position

    return () => window.removeEventListener('scroll', showFab)
  }, [])

  const scrollToTop = () => {
    gsap.to(window, {
      duration: 1,
      scrollTo: { y: 0 },
      ease: 'power2.out'
    })
  }

  const fabVariants = {
    hidden: {
      scale: 0,
      opacity: 0,
      rotate: -180
    },
    visible: {
      scale: 1,
      opacity: 1,
      rotate: 0,
      transition: {
        type: 'spring' as const,
        stiffness: 260,
        damping: 20
      }
    }
  }

  const menuVariants = {
    hidden: { opacity: 0, scale: 0.8 },
    visible: { 
      opacity: 1, 
      scale: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20, scale: 0.8 },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        type: 'spring' as const,
        stiffness: 300,
        damping: 20
      }
    }
  }

  const actions = [
    {
      icon: MapIcon,
      label: 'View Map',
      action: () => console.log('Open map'),
      color: 'bg-green-500 hover:bg-green-600'
    },
    {
      icon: ChatBubbleLeftIcon,
      label: 'Contact Us',
      action: () => console.log('Open contact'),
      color: 'bg-blue-500 hover:bg-blue-600'
    },
    {
      icon: ArrowUpIcon,
      label: 'Back to Top',
      action: scrollToTop,
      color: 'bg-primary-500 hover:bg-primary-600'
    }
  ]

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          ref={fabRef}
          variants={fabVariants}
          initial="hidden"
          animate="visible"
          exit="hidden"
          className="fixed bottom-6 right-6 z-50"
        >
          {/* Expanded Menu */}
          <AnimatePresence>
            {isExpanded && (
              <motion.div
                variants={menuVariants}
                initial="hidden"
                animate="visible"
                exit="hidden"
                className="absolute bottom-16 right-0 space-y-3"
              >
                {actions.slice(0, -1).map((action, index) => (
                  <motion.button
                    key={index}
                    variants={itemVariants}
                    onClick={action.action}
                    className={`flex items-center justify-center w-12 h-12 rounded-full text-white shadow-lg transition-all duration-200 ${action.color}`}
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    title={action.label}
                  >
                    <action.icon className="h-5 w-5" />
                  </motion.button>
                ))}
              </motion.div>
            )}
          </AnimatePresence>

          {/* Main FAB */}
          <motion.button
            onClick={() => {
              if (isExpanded) {
                setIsExpanded(false)
              } else {
                scrollToTop()
              }
            }}
            onMouseEnter={() => setIsExpanded(true)}
            onMouseLeave={() => setIsExpanded(false)}
            className="flex items-center justify-center w-14 h-14 bg-primary-600 hover:bg-primary-700 text-white rounded-full shadow-lg transition-all duration-200"
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
          >
            <motion.div
              animate={{ rotate: isExpanded ? 45 : 0 }}
              transition={{ duration: 0.2 }}
            >
              <ArrowUpIcon className="h-6 w-6" />
            </motion.div>
          </motion.button>

          {/* Ripple Effect */}
          <motion.div
            className="absolute inset-0 bg-primary-400 rounded-full -z-10"
            initial={{ scale: 1, opacity: 0.3 }}
            animate={{ 
              scale: [1, 1.5, 1],
              opacity: [0.3, 0, 0.3]
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: 'easeInOut'
            }}
          />
        </motion.div>
      )}
    </AnimatePresence>
  )
}

export default FloatingActionButton
