// Image optimization utilities for better performance

export interface ImageConfig {
  width?: number
  height?: number
  quality?: number
  format?: 'webp' | 'avif' | 'jpeg' | 'png'
  blur?: boolean
}

// Generate optimized image URLs (for future integration with image CDN)
export const getOptimizedImageUrl = (
  src: string,
  config: ImageConfig = {}
): string => {
  // For now, return the original src
  // In production, this would integrate with a CDN like Cloudinary, ImageKit, etc.

  // For future CDN integration, we would use these config options
  // Currently returning original src as placeholder
  void config // Suppress unused parameter warning

  // Example CDN URL generation (commented out for now)
  /*
  const baseUrl = 'https://your-cdn.com/image'
  const params = new URLSearchParams()
  
  if (width) params.append('w', width.toString())
  if (height) params.append('h', height.toString())
  params.append('q', quality.toString())
  params.append('f', format)
  if (blur) params.append('blur', '10')
  
  return `${baseUrl}/${encodeURIComponent(src)}?${params.toString()}`
  */

  return src
}

// Generate responsive image srcSet
export const generateSrcSet = (
  src: string,
  sizes: number[] = [320, 640, 768, 1024, 1280, 1920]
): string => {
  return sizes
    .map(size => `${getOptimizedImageUrl(src, { width: size })} ${size}w`)
    .join(', ')
}

// Generate blur placeholder data URL
export const generateBlurDataURL = (
  width: number = 10,
  height: number = 10
): string => {
  const canvas = document.createElement('canvas')
  canvas.width = width
  canvas.height = height
  
  const ctx = canvas.getContext('2d')
  if (!ctx) return ''
  
  // Create a simple gradient placeholder
  const gradient = ctx.createLinearGradient(0, 0, width, height)
  gradient.addColorStop(0, '#f3f4f6')
  gradient.addColorStop(1, '#e5e7eb')
  
  ctx.fillStyle = gradient
  ctx.fillRect(0, 0, width, height)
  
  return canvas.toDataURL('image/jpeg', 0.1)
}

// Preload critical images
export const preloadImage = (src: string): Promise<void> => {
  return new Promise((resolve, reject) => {
    const img = new Image()
    img.onload = () => resolve()
    img.onerror = reject
    img.src = src
  })
}

// Preload multiple images
export const preloadImages = async (sources: string[]): Promise<void> => {
  try {
    await Promise.all(sources.map(preloadImage))
  } catch (error) {
    console.warn('Some images failed to preload:', error)
  }
}

// Check if WebP is supported
export const isWebPSupported = (): boolean => {
  const canvas = document.createElement('canvas')
  canvas.width = 1
  canvas.height = 1
  return canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0
}

// Check if AVIF is supported
export const isAVIFSupported = (): boolean => {
  const canvas = document.createElement('canvas')
  canvas.width = 1
  canvas.height = 1
  return canvas.toDataURL('image/avif').indexOf('data:image/avif') === 0
}

// Get the best supported format
export const getBestImageFormat = (): 'avif' | 'webp' | 'jpeg' => {
  if (isAVIFSupported()) return 'avif'
  if (isWebPSupported()) return 'webp'
  return 'jpeg'
}

// Image compression utility (client-side)
export const compressImage = (
  file: File,
  maxWidth: number = 1920,
  maxHeight: number = 1080,
  quality: number = 0.8
): Promise<Blob> => {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    const img = new Image()

    img.onload = () => {
      // Calculate new dimensions
      let { width, height } = img
      
      if (width > height) {
        if (width > maxWidth) {
          height = (height * maxWidth) / width
          width = maxWidth
        }
      } else {
        if (height > maxHeight) {
          width = (width * maxHeight) / height
          height = maxHeight
        }
      }

      canvas.width = width
      canvas.height = height

      // Draw and compress
      ctx?.drawImage(img, 0, 0, width, height)
      
      canvas.toBlob(
        (blob) => {
          if (blob) {
            resolve(blob)
          } else {
            reject(new Error('Canvas to Blob conversion failed'))
          }
        },
        'image/jpeg',
        quality
      )
    }

    img.onerror = reject
    img.src = URL.createObjectURL(file)
  })
}

// Lazy loading intersection observer options
export const lazyLoadOptions: IntersectionObserverInit = {
  threshold: 0.1,
  rootMargin: '50px 0px'
}

// Image loading states
export type ImageLoadingState = 'idle' | 'loading' | 'loaded' | 'error'

// Common image sizes for responsive design
export const imageSizes = {
  thumbnail: { width: 150, height: 150 },
  small: { width: 300, height: 200 },
  medium: { width: 600, height: 400 },
  large: { width: 1200, height: 800 },
  hero: { width: 1920, height: 1080 }
}

// Generate responsive sizes attribute
export const generateSizesAttribute = (breakpoints: Record<string, string>): string => {
  return Object.entries(breakpoints)
    .map(([breakpoint, size]) => `(max-width: ${breakpoint}) ${size}`)
    .join(', ')
}

// Default responsive sizes for common use cases
export const responsiveSizes = {
  hero: '100vw',
  card: '(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 33vw',
  thumbnail: '150px',
  gallery: '(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 25vw'
}

// Performance monitoring for images
export const trackImagePerformance = (src: string, startTime: number) => {
  const endTime = performance.now()
  const loadTime = endTime - startTime
  
  if (import.meta.env.DEV) {
    console.log(`Image loaded: ${src} in ${loadTime.toFixed(2)}ms`)
  }
  
  // In production, you might want to send this data to analytics
  // analytics.track('image_load_time', { src, loadTime })
}
