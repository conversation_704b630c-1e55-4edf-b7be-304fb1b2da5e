import React, { useEffect, useRef } from 'react'
import { Helmet } from 'react-helmet-async'
import { motion } from 'framer-motion'
import { Link } from 'react-router-dom'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import { useStaggerChildren } from '@/hooks/useGSAP'
import { destinations } from '@/data/destinations'

gsap.registerPlugin(ScrollTrigger)

// Structured data for SEO
const structuredData = {
  "@context": "https://schema.org",
  "@type": "TravelAgency",
  "name": "Sri Lanka Tourist Guide",
  "description": "Comprehensive travel guide for Sri Lanka featuring destinations, activities, cultural insights, and practical travel tips for 2025",
  "url": "https://srilankaguide.com",
  "logo": "https://srilankaguide.com/logo.png",
  "sameAs": [
    "https://facebook.com/srilankaguide",
    "https://instagram.com/srilankaguide",
    "https://twitter.com/srilankaguide"
  ],
  "areaServed": {
    "@type": "Country",
    "name": "Sri Lanka"
  },
  "hasOfferCatalog": {
    "@type": "OfferCatalog",
    "name": "Sri Lanka Travel Destinations",
    "itemListElement": destinations.slice(0, 5).map((dest, index) => ({
      "@type": "TouristDestination",
      "position": index + 1,
      "name": dest.name,
      "description": dest.shortDescription,
      "geo": {
        "@type": "GeoCoordinates",
        "latitude": dest.coordinates.lat,
        "longitude": dest.coordinates.lng
      }
    }))
  }
}

const Home: React.FC = () => {
  const heroRef = useRef<HTMLDivElement>(null)
  const featuredRef = useStaggerChildren('fadeIn', 0.2)
  const statsRef = useStaggerChildren('scaleIn', 0.1)

  useEffect(() => {
    // Hero background parallax
    if (heroRef.current) {
      gsap.to(heroRef.current, {
        yPercent: -50,
        ease: 'none',
        scrollTrigger: {
          trigger: heroRef.current,
          start: 'top bottom',
          end: 'bottom top',
          scrub: true,
        },
      })
    }

    // Cleanup on unmount
    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill())
    }
  }, [])

  return (
    <>
      <Helmet>
        <title>Sri Lanka Travel Guide 2025 - Best Destinations, Activities & Tips | Pearl of Indian Ocean</title>
        <meta name="description" content="Comprehensive Sri Lanka travel guide 2025 featuring top destinations like Sigiriya, Kandy, Ella, cultural sites, wildlife safaris, beaches, travel tips, and practical information for your perfect Sri Lankan adventure." />
        <meta name="keywords" content="Sri Lanka travel guide 2025, Sri Lanka tourism, best places visit Sri Lanka, Sigiriya, Kandy, Ella, Galle Fort, Sri Lanka destinations, Sri Lanka activities, Sri Lanka culture, travel tips Sri Lanka" />

        {/* Open Graph / Facebook */}
        <meta property="og:type" content="website" />
        <meta property="og:url" content="https://srilankaguide.com/" />
        <meta property="og:title" content="Sri Lanka Travel Guide 2025 - Discover the Pearl of Indian Ocean" />
        <meta property="og:description" content="Explore Sri Lanka's stunning destinations, rich culture, and unforgettable experiences with our comprehensive travel guide featuring top attractions, activities, and insider tips." />
        <meta property="og:image" content="https://srilankaguide.com/images/sri-lanka-hero.jpg" />
        <meta property="og:image:width" content="1200" />
        <meta property="og:image:height" content="630" />
        <meta property="og:site_name" content="Sri Lanka Tourist Guide" />
        <meta property="og:locale" content="en_US" />

        {/* Twitter */}
        <meta property="twitter:card" content="summary_large_image" />
        <meta property="twitter:url" content="https://srilankaguide.com/" />
        <meta property="twitter:title" content="Sri Lanka Travel Guide 2025 - Discover the Pearl of Indian Ocean" />
        <meta property="twitter:description" content="Explore Sri Lanka's stunning destinations, rich culture, and unforgettable experiences with our comprehensive travel guide." />
        <meta property="twitter:image" content="https://srilankaguide.com/images/sri-lanka-hero.jpg" />

        {/* Additional SEO Meta Tags */}
        <meta name="robots" content="index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1" />
        <meta name="author" content="Sri Lanka Tourist Guide" />
        <meta name="publisher" content="Sri Lanka Tourist Guide" />
        <meta name="language" content="English" />
        <meta name="geo.region" content="LK" />
        <meta name="geo.country" content="Sri Lanka" />
        <meta name="geo.placename" content="Sri Lanka" />

        {/* Canonical URL */}
        <link rel="canonical" href="https://srilankaguide.com/" />

        {/* Structured Data */}
        <script type="application/ld+json">
          {JSON.stringify(structuredData)}
        </script>
      </Helmet>

      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center overflow-hidden">
        <div
          ref={heroRef}
          className="absolute inset-0 hero-gradient"
          style={{
            backgroundImage: 'linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(147, 51, 234, 0.1) 100%)',
          }}
        />
        <div className="container-max section-padding relative z-10">
          <div className="text-center">
            <motion.h1
              initial={{ opacity: 0, y: 50, scale: 0.9 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              transition={{ duration: 1, ease: 'easeOut' }}
              className="text-4xl md:text-6xl lg:text-7xl font-bold text-gray-900 mb-6"
            >
              Sri Lanka Travel Guide 2025:{' '}
              <span className="text-gradient bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Pearl of the Indian Ocean
              </span>
            </motion.h1>

            <motion.p
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
              className="text-xl md:text-2xl text-gray-600 mb-8 max-w-4xl mx-auto leading-relaxed"
            >
              Discover Sri Lanka's best destinations, cultural treasures, wildlife adventures, and pristine beaches.
              Your complete guide to exploring ancient temples, UNESCO World Heritage sites, tea plantations,
              and unforgettable experiences in 2025.
            </motion.p>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              className="flex flex-col sm:flex-row gap-4 justify-center"
            >
              <Link to="/destinations" className="btn-primary transform hover:scale-105 transition-transform">
                Explore Destinations
              </Link>
              <Link to="/activities" className="btn-secondary transform hover:scale-105 transition-transform">
                Discover Activities
              </Link>
            </motion.div>
          </div>
        </div>

        {/* Floating elements */}
        <div className="absolute top-20 left-10 w-20 h-20 bg-blue-200 rounded-full opacity-20 animate-bounce" style={{ animationDelay: '0s' }} />
        <div className="absolute top-40 right-20 w-16 h-16 bg-purple-200 rounded-full opacity-20 animate-bounce" style={{ animationDelay: '1s' }} />
        <div className="absolute bottom-40 left-20 w-12 h-12 bg-yellow-200 rounded-full opacity-20 animate-bounce" style={{ animationDelay: '2s' }} />
      </section>

      {/* Statistics Section */}
      <section className="section-padding bg-gray-50" aria-labelledby="sri-lanka-facts">
        <div className="container-max">
          <div className="text-center mb-12">
            <h2 id="sri-lanka-facts" className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Sri Lanka by the Numbers
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Discover what makes Sri Lanka one of the world's most diverse and fascinating travel destinations
            </p>
          </div>
          <div ref={statsRef} className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <div className="gsap-scale-in">
              <div className="text-4xl md:text-5xl font-bold text-primary-600 mb-2">8</div>
              <div className="text-gray-600 font-medium">UNESCO World Heritage Sites</div>
              <div className="text-sm text-gray-500 mt-1">Including Sigiriya & Kandy</div>
            </div>
            <div className="gsap-scale-in">
              <div className="text-4xl md:text-5xl font-bold text-primary-600 mb-2">26</div>
              <div className="text-gray-600 font-medium">National Parks & Reserves</div>
              <div className="text-sm text-gray-500 mt-1">Wildlife & Nature Protection</div>
            </div>
            <div className="gsap-scale-in">
              <div className="text-4xl md:text-5xl font-bold text-primary-600 mb-2">1,340</div>
              <div className="text-gray-600 font-medium">Kilometers of Coastline</div>
              <div className="text-sm text-gray-500 mt-1">Pristine Beaches & Surfing</div>
            </div>
            <div className="gsap-scale-in">
              <div className="text-4xl md:text-5xl font-bold text-primary-600 mb-2">2,500</div>
              <div className="text-gray-600 font-medium">Years of Recorded History</div>
              <div className="text-sm text-gray-500 mt-1">Ancient Civilization</div>
            </div>
          </div>
        </div>
      </section>

      {/* Quick Overview Section */}
      <section className="section-padding bg-white">
        <div className="container-max">
          <div className="text-center mb-16">
            <motion.h2
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="text-3xl md:text-5xl font-bold text-gray-900 mb-6"
            >
              Why Visit Sri Lanka?
            </motion.h2>
            <motion.p
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              viewport={{ once: true }}
              className="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto"
            >
              Sri Lanka offers an incredible diversity of experiences in a compact island nation,
              making it the perfect destination for every type of traveler.
            </motion.p>
          </div>

          <div ref={featuredRef} className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="gsap-fade-in card p-8 text-center group hover:shadow-2xl transition-all duration-300">
              <div className="text-6xl mb-6 group-hover:scale-110 transition-transform duration-300">🏖️</div>
              <h3 className="text-2xl font-semibold mb-4 text-gray-900">Beautiful Beaches</h3>
              <p className="text-gray-600 leading-relaxed">
                Pristine coastlines with golden sands and crystal-clear waters perfect for relaxation,
                surfing, and water sports along both east and west coasts.
              </p>
            </div>

            <div className="gsap-fade-in card p-8 text-center group hover:shadow-2xl transition-all duration-300">
              <div className="text-6xl mb-6 group-hover:scale-110 transition-transform duration-300">🏛️</div>
              <h3 className="text-2xl font-semibold mb-4 text-gray-900">Rich Heritage</h3>
              <p className="text-gray-600 leading-relaxed">
                Ancient temples, historical sites, and UNESCO World Heritage locations showcasing
                2,500 years of continuous civilization and cultural heritage.
              </p>
            </div>

            <div className="gsap-fade-in card p-8 text-center group hover:shadow-2xl transition-all duration-300">
              <div className="text-6xl mb-6 group-hover:scale-110 transition-transform duration-300">🌿</div>
              <h3 className="text-2xl font-semibold mb-4 text-gray-900">Natural Wonders</h3>
              <p className="text-gray-600 leading-relaxed">
                Lush rainforests, tea plantations, wildlife parks, and breathtaking landscapes
                offering incredible biodiversity in a compact area.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Destinations */}
      <section className="section-padding bg-gradient-to-br from-blue-50 to-purple-50">
        <div className="container-max">
          <div className="text-center mb-16">
            <motion.h2
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="text-3xl md:text-5xl font-bold text-gray-900 mb-6"
            >
              Featured Destinations
            </motion.h2>
            <motion.p
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              viewport={{ once: true }}
              className="text-lg text-gray-600 max-w-2xl mx-auto"
            >
              Discover some of Sri Lanka's most iconic and breathtaking destinations
            </motion.p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {destinations.slice(0, 3).map((destination, index) => (
              <motion.div
                key={destination.id}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                viewport={{ once: true }}
                className="card overflow-hidden group"
              >
                <div className="relative h-64 bg-gradient-to-br from-blue-400 to-purple-500 overflow-hidden">
                  <div className="absolute inset-0 bg-black bg-opacity-20 group-hover:bg-opacity-10 transition-all duration-300" />
                  <div className="absolute bottom-4 left-4 text-white">
                    <span className="inline-block px-3 py-1 bg-white bg-opacity-20 rounded-full text-sm font-medium mb-2">
                      {destination.category}
                    </span>
                  </div>
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-semibold mb-2 text-gray-900">{destination.name}</h3>
                  <p className="text-gray-600 mb-4 line-clamp-3">{destination.shortDescription}</p>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">{destination.province} Province</span>
                    <Link
                      to={`/destinations/${destination.id}`}
                      className="text-primary-600 hover:text-primary-700 font-medium text-sm"
                    >
                      Learn More →
                    </Link>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          <div className="text-center mt-12">
            <Link to="/destinations" className="btn-primary">
              View All Destinations
            </Link>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="section-padding bg-gradient-to-r from-primary-600 to-purple-600 text-white">
        <div className="container-max text-center">
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-3xl md:text-4xl font-bold mb-6"
          >
            Ready to Start Your Sri Lankan Adventure?
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="text-xl mb-8 max-w-2xl mx-auto opacity-90"
          >
            Plan your perfect trip with our comprehensive guides, insider tips, and local recommendations.
          </motion.p>
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
            className="flex flex-col sm:flex-row gap-4 justify-center"
          >
            <Link to="/travel-tips" className="bg-white text-primary-600 hover:bg-gray-100 font-medium py-3 px-8 rounded-lg transition-colors">
              Get Travel Tips
            </Link>
            <Link to="/contact" className="border-2 border-white text-white hover:bg-white hover:text-primary-600 font-medium py-3 px-8 rounded-lg transition-colors">
              Contact Us
            </Link>
          </motion.div>
        </div>
      </section>
    </>
  )
}

export default Home
