import{r as z,a as ai,j as l,A as us,m as Lt}from"./animations-Dls6NBzi.js";import{F as ls,H as cs}from"./index-Bxt4fWYT.js";import{r as ds,L as fs}from"./router-Bcnr4W6T.js";import{d as ht}from"./destinations-C88Q9Vk3.js";import{u as _s}from"./useGSAP-CAvLNQrn.js";import{F as zn}from"./MagnifyingGlassIcon-DvDOtkAW.js";import{F as ms}from"./FunnelIcon-BMhenk7y.js";import{F as En}from"./MapPinIcon-DxqRjeFE.js";import{F as ps}from"./ClockIcon-Bop423fN.js";import"./vendor-BtP0CW_r.js";function kn(d,f){const a=z.useRef(f);z.useEffect(function(){f!==a.current&&d.attributionControl!=null&&(a.current!=null&&d.attributionControl.removeAttribution(a.current),f!=null&&d.attributionControl.addAttribution(f)),a.current=f},[d,f])}const gs=1;function vs(d){return Object.freeze({__version:gs,map:d})}function ys(d,f){return Object.freeze({...d,...f})}const On=z.createContext(null),Zn=On.Provider;function ui(){const d=z.useContext(On);if(d==null)throw new Error("No context provided: useLeafletContext() can only be used in a descendant of <MapContainer>");return d}function xs(d){function f(a,m){const{instance:_,context:O}=d(a).current;return z.useImperativeHandle(m,()=>_),a.children==null?null:ai.createElement(Zn,{value:O},a.children)}return z.forwardRef(f)}function ws(d){function f(a,m){const[_,O]=z.useState(!1),{instance:v}=d(a,O).current;z.useImperativeHandle(m,()=>v),z.useEffect(function(){_&&v.update()},[v,_,a.children]);const G=v._contentNode;return G?ds.createPortal(a.children,G):null}return z.forwardRef(f)}function Ps(d){function f(a,m){const{instance:_}=d(a).current;return z.useImperativeHandle(m,()=>_),null}return z.forwardRef(f)}function In(d,f){const a=z.useRef();z.useEffect(function(){return f!=null&&d.instance.on(f),a.current=f,function(){a.current!=null&&d.instance.off(a.current),a.current=null}},[d,f])}function li(d,f){const a=d.pane??f.pane;return a?{...d,pane:a}:d}function Ls(d,f){return function(m,_){const O=ui(),v=d(li(m,O),O);return kn(O.map,m.attribution),In(v.current,m.eventHandlers),f(v.current,O,m,_),v}}var ee={exports:{}};/* @preserve
 * Leaflet 1.9.4, a JS library for interactive maps. https://leafletjs.com
 * (c) 2010-2023 Vladimir Agafonkin, (c) 2010-2011 CloudMade
 */var bs=ee.exports,Sn;function Ts(){return Sn||(Sn=1,function(d,f){(function(a,m){m(f)})(bs,function(a){var m="1.9.4";function _(t){var e,i,n,o;for(i=1,n=arguments.length;i<n;i++){o=arguments[i];for(e in o)t[e]=o[e]}return t}var O=Object.create||function(){function t(){}return function(e){return t.prototype=e,new t}}();function v(t,e){var i=Array.prototype.slice;if(t.bind)return t.bind.apply(t,i.call(arguments,1));var n=i.call(arguments,2);return function(){return t.apply(e,n.length?n.concat(i.call(arguments)):arguments)}}var G=0;function w(t){return"_leaflet_id"in t||(t._leaflet_id=++G),t._leaflet_id}function U(t,e,i){var n,o,s,r;return r=function(){n=!1,o&&(s.apply(i,o),o=!1)},s=function(){n?o=arguments:(t.apply(i,arguments),setTimeout(r,e),n=!0)},s}function M(t,e,i){var n=e[1],o=e[0],s=n-o;return t===n&&i?t:((t-o)%s+s)%s+o}function g(){return!1}function j(t,e){if(e===!1)return t;var i=Math.pow(10,e===void 0?6:e);return Math.round(t*i)/i}function q(t){return t.trim?t.trim():t.replace(/^\s+|\s+$/g,"")}function ft(t){return q(t).split(/\s+/)}function A(t,e){Object.prototype.hasOwnProperty.call(t,"options")||(t.options=t.options?O(t.options):{});for(var i in e)t.options[i]=e[i];return t.options}function ie(t,e,i){var n=[];for(var o in t)n.push(encodeURIComponent(i?o.toUpperCase():o)+"="+encodeURIComponent(t[o]));return(!e||e.indexOf("?")===-1?"?":"&")+n.join("&")}var ne=/\{ *([\w_ -]+) *\}/g;function Tt(t,e){return t.replace(ne,function(i,n){var o=e[n];if(o===void 0)throw new Error("No value provided for variable "+i);return typeof o=="function"&&(o=o(e)),o})}var st=Array.isArray||function(t){return Object.prototype.toString.call(t)==="[object Array]"};function be(t,e){for(var i=0;i<t.length;i++)if(t[i]===e)return i;return-1}var oe="data:image/gif;base64,R0lGODlhAQABAAD/ACwAAAAAAQABAAACADs=";function Te(t){return window["webkit"+t]||window["moz"+t]||window["ms"+t]}var fi=0;function _i(t){var e=+new Date,i=Math.max(0,16-(e-fi));return fi=e+i,window.setTimeout(t,i)}var Ce=window.requestAnimationFrame||Te("RequestAnimationFrame")||_i,mi=window.cancelAnimationFrame||Te("CancelAnimationFrame")||Te("CancelRequestAnimationFrame")||function(t){window.clearTimeout(t)};function X(t,e,i){if(i&&Ce===_i)t.call(e);else return Ce.call(window,v(t,e))}function et(t){t&&mi.call(window,t)}var Bn={__proto__:null,extend:_,create:O,bind:v,get lastId(){return G},stamp:w,throttle:U,wrapNum:M,falseFn:g,formatNum:j,trim:q,splitWords:ft,setOptions:A,getParamString:ie,template:Tt,isArray:st,indexOf:be,emptyImageUrl:oe,requestFn:Ce,cancelFn:mi,requestAnimFrame:X,cancelAnimFrame:et};function _t(){}_t.extend=function(t){var e=function(){A(this),this.initialize&&this.initialize.apply(this,arguments),this.callInitHooks()},i=e.__super__=this.prototype,n=O(i);n.constructor=e,e.prototype=n;for(var o in this)Object.prototype.hasOwnProperty.call(this,o)&&o!=="prototype"&&o!=="__super__"&&(e[o]=this[o]);return t.statics&&_(e,t.statics),t.includes&&(Nn(t.includes),_.apply(null,[n].concat(t.includes))),_(n,t),delete n.statics,delete n.includes,n.options&&(n.options=i.options?O(i.options):{},_(n.options,t.options)),n._initHooks=[],n.callInitHooks=function(){if(!this._initHooksCalled){i.callInitHooks&&i.callInitHooks.call(this),this._initHooksCalled=!0;for(var s=0,r=n._initHooks.length;s<r;s++)n._initHooks[s].call(this)}},e},_t.include=function(t){var e=this.prototype.options;return _(this.prototype,t),t.options&&(this.prototype.options=e,this.mergeOptions(t.options)),this},_t.mergeOptions=function(t){return _(this.prototype.options,t),this},_t.addInitHook=function(t){var e=Array.prototype.slice.call(arguments,1),i=typeof t=="function"?t:function(){this[t].apply(this,e)};return this.prototype._initHooks=this.prototype._initHooks||[],this.prototype._initHooks.push(i),this};function Nn(t){if(!(typeof L>"u"||!L||!L.Mixin)){t=st(t)?t:[t];for(var e=0;e<t.length;e++)t[e]===L.Mixin.Events&&console.warn("Deprecated include of L.Mixin.Events: this property will be removed in future releases, please inherit from L.Evented instead.",new Error().stack)}}var tt={on:function(t,e,i){if(typeof t=="object")for(var n in t)this._on(n,t[n],e);else{t=ft(t);for(var o=0,s=t.length;o<s;o++)this._on(t[o],e,i)}return this},off:function(t,e,i){if(!arguments.length)delete this._events;else if(typeof t=="object")for(var n in t)this._off(n,t[n],e);else{t=ft(t);for(var o=arguments.length===1,s=0,r=t.length;s<r;s++)o?this._off(t[s]):this._off(t[s],e,i)}return this},_on:function(t,e,i,n){if(typeof e!="function"){console.warn("wrong listener type: "+typeof e);return}if(this._listens(t,e,i)===!1){i===this&&(i=void 0);var o={fn:e,ctx:i};n&&(o.once=!0),this._events=this._events||{},this._events[t]=this._events[t]||[],this._events[t].push(o)}},_off:function(t,e,i){var n,o,s;if(this._events&&(n=this._events[t],!!n)){if(arguments.length===1){if(this._firingCount)for(o=0,s=n.length;o<s;o++)n[o].fn=g;delete this._events[t];return}if(typeof e!="function"){console.warn("wrong listener type: "+typeof e);return}var r=this._listens(t,e,i);if(r!==!1){var h=n[r];this._firingCount&&(h.fn=g,this._events[t]=n=n.slice()),n.splice(r,1)}}},fire:function(t,e,i){if(!this.listens(t,i))return this;var n=_({},e,{type:t,target:this,sourceTarget:e&&e.sourceTarget||this});if(this._events){var o=this._events[t];if(o){this._firingCount=this._firingCount+1||1;for(var s=0,r=o.length;s<r;s++){var h=o[s],u=h.fn;h.once&&this.off(t,u,h.ctx),u.call(h.ctx||this,n)}this._firingCount--}}return i&&this._propagateEvent(n),this},listens:function(t,e,i,n){typeof t!="string"&&console.warn('"string" type argument expected');var o=e;typeof e!="function"&&(n=!!e,o=void 0,i=void 0);var s=this._events&&this._events[t];if(s&&s.length&&this._listens(t,o,i)!==!1)return!0;if(n){for(var r in this._eventParents)if(this._eventParents[r].listens(t,e,i,n))return!0}return!1},_listens:function(t,e,i){if(!this._events)return!1;var n=this._events[t]||[];if(!e)return!!n.length;i===this&&(i=void 0);for(var o=0,s=n.length;o<s;o++)if(n[o].fn===e&&n[o].ctx===i)return o;return!1},once:function(t,e,i){if(typeof t=="object")for(var n in t)this._on(n,t[n],e,!0);else{t=ft(t);for(var o=0,s=t.length;o<s;o++)this._on(t[o],e,i,!0)}return this},addEventParent:function(t){return this._eventParents=this._eventParents||{},this._eventParents[w(t)]=t,this},removeEventParent:function(t){return this._eventParents&&delete this._eventParents[w(t)],this},_propagateEvent:function(t){for(var e in this._eventParents)this._eventParents[e].fire(t.type,_({layer:t.target,propagatedFrom:t.target},t),!0)}};tt.addEventListener=tt.on,tt.removeEventListener=tt.clearAllEventListeners=tt.off,tt.addOneTimeEventListener=tt.once,tt.fireEvent=tt.fire,tt.hasEventListeners=tt.listens;var jt=_t.extend(tt);function b(t,e,i){this.x=i?Math.round(t):t,this.y=i?Math.round(e):e}var pi=Math.trunc||function(t){return t>0?Math.floor(t):Math.ceil(t)};b.prototype={clone:function(){return new b(this.x,this.y)},add:function(t){return this.clone()._add(P(t))},_add:function(t){return this.x+=t.x,this.y+=t.y,this},subtract:function(t){return this.clone()._subtract(P(t))},_subtract:function(t){return this.x-=t.x,this.y-=t.y,this},divideBy:function(t){return this.clone()._divideBy(t)},_divideBy:function(t){return this.x/=t,this.y/=t,this},multiplyBy:function(t){return this.clone()._multiplyBy(t)},_multiplyBy:function(t){return this.x*=t,this.y*=t,this},scaleBy:function(t){return new b(this.x*t.x,this.y*t.y)},unscaleBy:function(t){return new b(this.x/t.x,this.y/t.y)},round:function(){return this.clone()._round()},_round:function(){return this.x=Math.round(this.x),this.y=Math.round(this.y),this},floor:function(){return this.clone()._floor()},_floor:function(){return this.x=Math.floor(this.x),this.y=Math.floor(this.y),this},ceil:function(){return this.clone()._ceil()},_ceil:function(){return this.x=Math.ceil(this.x),this.y=Math.ceil(this.y),this},trunc:function(){return this.clone()._trunc()},_trunc:function(){return this.x=pi(this.x),this.y=pi(this.y),this},distanceTo:function(t){t=P(t);var e=t.x-this.x,i=t.y-this.y;return Math.sqrt(e*e+i*i)},equals:function(t){return t=P(t),t.x===this.x&&t.y===this.y},contains:function(t){return t=P(t),Math.abs(t.x)<=Math.abs(this.x)&&Math.abs(t.y)<=Math.abs(this.y)},toString:function(){return"Point("+j(this.x)+", "+j(this.y)+")"}};function P(t,e,i){return t instanceof b?t:st(t)?new b(t[0],t[1]):t==null?t:typeof t=="object"&&"x"in t&&"y"in t?new b(t.x,t.y):new b(t,e,i)}function N(t,e){if(t)for(var i=e?[t,e]:t,n=0,o=i.length;n<o;n++)this.extend(i[n])}N.prototype={extend:function(t){var e,i;if(!t)return this;if(t instanceof b||typeof t[0]=="number"||"x"in t)e=i=P(t);else if(t=$(t),e=t.min,i=t.max,!e||!i)return this;return!this.min&&!this.max?(this.min=e.clone(),this.max=i.clone()):(this.min.x=Math.min(e.x,this.min.x),this.max.x=Math.max(i.x,this.max.x),this.min.y=Math.min(e.y,this.min.y),this.max.y=Math.max(i.y,this.max.y)),this},getCenter:function(t){return P((this.min.x+this.max.x)/2,(this.min.y+this.max.y)/2,t)},getBottomLeft:function(){return P(this.min.x,this.max.y)},getTopRight:function(){return P(this.max.x,this.min.y)},getTopLeft:function(){return this.min},getBottomRight:function(){return this.max},getSize:function(){return this.max.subtract(this.min)},contains:function(t){var e,i;return typeof t[0]=="number"||t instanceof b?t=P(t):t=$(t),t instanceof N?(e=t.min,i=t.max):e=i=t,e.x>=this.min.x&&i.x<=this.max.x&&e.y>=this.min.y&&i.y<=this.max.y},intersects:function(t){t=$(t);var e=this.min,i=this.max,n=t.min,o=t.max,s=o.x>=e.x&&n.x<=i.x,r=o.y>=e.y&&n.y<=i.y;return s&&r},overlaps:function(t){t=$(t);var e=this.min,i=this.max,n=t.min,o=t.max,s=o.x>e.x&&n.x<i.x,r=o.y>e.y&&n.y<i.y;return s&&r},isValid:function(){return!!(this.min&&this.max)},pad:function(t){var e=this.min,i=this.max,n=Math.abs(e.x-i.x)*t,o=Math.abs(e.y-i.y)*t;return $(P(e.x-n,e.y-o),P(i.x+n,i.y+o))},equals:function(t){return t?(t=$(t),this.min.equals(t.getTopLeft())&&this.max.equals(t.getBottomRight())):!1}};function $(t,e){return!t||t instanceof N?t:new N(t,e)}function J(t,e){if(t)for(var i=e?[t,e]:t,n=0,o=i.length;n<o;n++)this.extend(i[n])}J.prototype={extend:function(t){var e=this._southWest,i=this._northEast,n,o;if(t instanceof I)n=t,o=t;else if(t instanceof J){if(n=t._southWest,o=t._northEast,!n||!o)return this}else return t?this.extend(E(t)||F(t)):this;return!e&&!i?(this._southWest=new I(n.lat,n.lng),this._northEast=new I(o.lat,o.lng)):(e.lat=Math.min(n.lat,e.lat),e.lng=Math.min(n.lng,e.lng),i.lat=Math.max(o.lat,i.lat),i.lng=Math.max(o.lng,i.lng)),this},pad:function(t){var e=this._southWest,i=this._northEast,n=Math.abs(e.lat-i.lat)*t,o=Math.abs(e.lng-i.lng)*t;return new J(new I(e.lat-n,e.lng-o),new I(i.lat+n,i.lng+o))},getCenter:function(){return new I((this._southWest.lat+this._northEast.lat)/2,(this._southWest.lng+this._northEast.lng)/2)},getSouthWest:function(){return this._southWest},getNorthEast:function(){return this._northEast},getNorthWest:function(){return new I(this.getNorth(),this.getWest())},getSouthEast:function(){return new I(this.getSouth(),this.getEast())},getWest:function(){return this._southWest.lng},getSouth:function(){return this._southWest.lat},getEast:function(){return this._northEast.lng},getNorth:function(){return this._northEast.lat},contains:function(t){typeof t[0]=="number"||t instanceof I||"lat"in t?t=E(t):t=F(t);var e=this._southWest,i=this._northEast,n,o;return t instanceof J?(n=t.getSouthWest(),o=t.getNorthEast()):n=o=t,n.lat>=e.lat&&o.lat<=i.lat&&n.lng>=e.lng&&o.lng<=i.lng},intersects:function(t){t=F(t);var e=this._southWest,i=this._northEast,n=t.getSouthWest(),o=t.getNorthEast(),s=o.lat>=e.lat&&n.lat<=i.lat,r=o.lng>=e.lng&&n.lng<=i.lng;return s&&r},overlaps:function(t){t=F(t);var e=this._southWest,i=this._northEast,n=t.getSouthWest(),o=t.getNorthEast(),s=o.lat>e.lat&&n.lat<i.lat,r=o.lng>e.lng&&n.lng<i.lng;return s&&r},toBBoxString:function(){return[this.getWest(),this.getSouth(),this.getEast(),this.getNorth()].join(",")},equals:function(t,e){return t?(t=F(t),this._southWest.equals(t.getSouthWest(),e)&&this._northEast.equals(t.getNorthEast(),e)):!1},isValid:function(){return!!(this._southWest&&this._northEast)}};function F(t,e){return t instanceof J?t:new J(t,e)}function I(t,e,i){if(isNaN(t)||isNaN(e))throw new Error("Invalid LatLng object: ("+t+", "+e+")");this.lat=+t,this.lng=+e,i!==void 0&&(this.alt=+i)}I.prototype={equals:function(t,e){if(!t)return!1;t=E(t);var i=Math.max(Math.abs(this.lat-t.lat),Math.abs(this.lng-t.lng));return i<=(e===void 0?1e-9:e)},toString:function(t){return"LatLng("+j(this.lat,t)+", "+j(this.lng,t)+")"},distanceTo:function(t){return xt.distance(this,E(t))},wrap:function(){return xt.wrapLatLng(this)},toBounds:function(t){var e=180*t/40075017,i=e/Math.cos(Math.PI/180*this.lat);return F([this.lat-e,this.lng-i],[this.lat+e,this.lng+i])},clone:function(){return new I(this.lat,this.lng,this.alt)}};function E(t,e,i){return t instanceof I?t:st(t)&&typeof t[0]!="object"?t.length===3?new I(t[0],t[1],t[2]):t.length===2?new I(t[0],t[1]):null:t==null?t:typeof t=="object"&&"lat"in t?new I(t.lat,"lng"in t?t.lng:t.lon,t.alt):e===void 0?null:new I(t,e,i)}var mt={latLngToPoint:function(t,e){var i=this.projection.project(t),n=this.scale(e);return this.transformation._transform(i,n)},pointToLatLng:function(t,e){var i=this.scale(e),n=this.transformation.untransform(t,i);return this.projection.unproject(n)},project:function(t){return this.projection.project(t)},unproject:function(t){return this.projection.unproject(t)},scale:function(t){return 256*Math.pow(2,t)},zoom:function(t){return Math.log(t/256)/Math.LN2},getProjectedBounds:function(t){if(this.infinite)return null;var e=this.projection.bounds,i=this.scale(t),n=this.transformation.transform(e.min,i),o=this.transformation.transform(e.max,i);return new N(n,o)},infinite:!1,wrapLatLng:function(t){var e=this.wrapLng?M(t.lng,this.wrapLng,!0):t.lng,i=this.wrapLat?M(t.lat,this.wrapLat,!0):t.lat,n=t.alt;return new I(i,e,n)},wrapLatLngBounds:function(t){var e=t.getCenter(),i=this.wrapLatLng(e),n=e.lat-i.lat,o=e.lng-i.lng;if(n===0&&o===0)return t;var s=t.getSouthWest(),r=t.getNorthEast(),h=new I(s.lat-n,s.lng-o),u=new I(r.lat-n,r.lng-o);return new J(h,u)}},xt=_({},mt,{wrapLng:[-180,180],R:6371e3,distance:function(t,e){var i=Math.PI/180,n=t.lat*i,o=e.lat*i,s=Math.sin((e.lat-t.lat)*i/2),r=Math.sin((e.lng-t.lng)*i/2),h=s*s+Math.cos(n)*Math.cos(o)*r*r,u=2*Math.atan2(Math.sqrt(h),Math.sqrt(1-h));return this.R*u}}),gi=6378137,Me={R:gi,MAX_LATITUDE:85.0511287798,project:function(t){var e=Math.PI/180,i=this.MAX_LATITUDE,n=Math.max(Math.min(i,t.lat),-i),o=Math.sin(n*e);return new b(this.R*t.lng*e,this.R*Math.log((1+o)/(1-o))/2)},unproject:function(t){var e=180/Math.PI;return new I((2*Math.atan(Math.exp(t.y/this.R))-Math.PI/2)*e,t.x*e/this.R)},bounds:function(){var t=gi*Math.PI;return new N([-t,-t],[t,t])}()};function Se(t,e,i,n){if(st(t)){this._a=t[0],this._b=t[1],this._c=t[2],this._d=t[3];return}this._a=t,this._b=e,this._c=i,this._d=n}Se.prototype={transform:function(t,e){return this._transform(t.clone(),e)},_transform:function(t,e){return e=e||1,t.x=e*(this._a*t.x+this._b),t.y=e*(this._c*t.y+this._d),t},untransform:function(t,e){return e=e||1,new b((t.x/e-this._b)/this._a,(t.y/e-this._d)/this._c)}};function Ft(t,e,i,n){return new Se(t,e,i,n)}var ze=_({},xt,{code:"EPSG:3857",projection:Me,transformation:function(){var t=.5/(Math.PI*Me.R);return Ft(t,.5,-t,.5)}()}),Rn=_({},ze,{code:"EPSG:900913"});function vi(t){return document.createElementNS("http://www.w3.org/2000/svg",t)}function yi(t,e){var i="",n,o,s,r,h,u;for(n=0,s=t.length;n<s;n++){for(h=t[n],o=0,r=h.length;o<r;o++)u=h[o],i+=(o?"L":"M")+u.x+" "+u.y;i+=e?y.svg?"z":"x":""}return i||"M0 0"}var Ee=document.documentElement.style,se="ActiveXObject"in window,Dn=se&&!document.addEventListener,xi="msLaunchUri"in navigator&&!("documentMode"in document),ke=ut("webkit"),wi=ut("android"),Pi=ut("android 2")||ut("android 3"),jn=parseInt(/WebKit\/([0-9]+)|$/.exec(navigator.userAgent)[1],10),Fn=wi&&ut("Google")&&jn<537&&!("AudioNode"in window),Oe=!!window.opera,Li=!xi&&ut("chrome"),bi=ut("gecko")&&!ke&&!Oe&&!se,Hn=!Li&&ut("safari"),Ti=ut("phantom"),Ci="OTransition"in Ee,Wn=navigator.platform.indexOf("Win")===0,Mi=se&&"transition"in Ee,Ze="WebKitCSSMatrix"in window&&"m11"in new window.WebKitCSSMatrix&&!Pi,Si="MozPerspective"in Ee,Un=!window.L_DISABLE_3D&&(Mi||Ze||Si)&&!Ci&&!Ti,Ht=typeof orientation<"u"||ut("mobile"),Vn=Ht&&ke,Gn=Ht&&Ze,zi=!window.PointerEvent&&window.MSPointerEvent,Ei=!!(window.PointerEvent||zi),ki="ontouchstart"in window||!!window.TouchEvent,qn=!window.L_NO_TOUCH&&(ki||Ei),Kn=Ht&&Oe,Yn=Ht&&bi,Xn=(window.devicePixelRatio||window.screen.deviceXDPI/window.screen.logicalXDPI)>1,$n=function(){var t=!1;try{var e=Object.defineProperty({},"passive",{get:function(){t=!0}});window.addEventListener("testPassiveEventSupport",g,e),window.removeEventListener("testPassiveEventSupport",g,e)}catch{}return t}(),Jn=function(){return!!document.createElement("canvas").getContext}(),Ie=!!(document.createElementNS&&vi("svg").createSVGRect),Qn=!!Ie&&function(){var t=document.createElement("div");return t.innerHTML="<svg/>",(t.firstChild&&t.firstChild.namespaceURI)==="http://www.w3.org/2000/svg"}(),to=!Ie&&function(){try{var t=document.createElement("div");t.innerHTML='<v:shape adj="1"/>';var e=t.firstChild;return e.style.behavior="url(#default#VML)",e&&typeof e.adj=="object"}catch{return!1}}(),eo=navigator.platform.indexOf("Mac")===0,io=navigator.platform.indexOf("Linux")===0;function ut(t){return navigator.userAgent.toLowerCase().indexOf(t)>=0}var y={ie:se,ielt9:Dn,edge:xi,webkit:ke,android:wi,android23:Pi,androidStock:Fn,opera:Oe,chrome:Li,gecko:bi,safari:Hn,phantom:Ti,opera12:Ci,win:Wn,ie3d:Mi,webkit3d:Ze,gecko3d:Si,any3d:Un,mobile:Ht,mobileWebkit:Vn,mobileWebkit3d:Gn,msPointer:zi,pointer:Ei,touch:qn,touchNative:ki,mobileOpera:Kn,mobileGecko:Yn,retina:Xn,passiveEvents:$n,canvas:Jn,svg:Ie,vml:to,inlineSvg:Qn,mac:eo,linux:io},Oi=y.msPointer?"MSPointerDown":"pointerdown",Zi=y.msPointer?"MSPointerMove":"pointermove",Ii=y.msPointer?"MSPointerUp":"pointerup",Ai=y.msPointer?"MSPointerCancel":"pointercancel",Ae={touchstart:Oi,touchmove:Zi,touchend:Ii,touchcancel:Ai},Bi={touchstart:ho,touchmove:re,touchend:re,touchcancel:re},kt={},Ni=!1;function no(t,e,i){return e==="touchstart"&&ao(),Bi[e]?(i=Bi[e].bind(this,i),t.addEventListener(Ae[e],i,!1),i):(console.warn("wrong event specified:",e),g)}function oo(t,e,i){if(!Ae[e]){console.warn("wrong event specified:",e);return}t.removeEventListener(Ae[e],i,!1)}function so(t){kt[t.pointerId]=t}function ro(t){kt[t.pointerId]&&(kt[t.pointerId]=t)}function Ri(t){delete kt[t.pointerId]}function ao(){Ni||(document.addEventListener(Oi,so,!0),document.addEventListener(Zi,ro,!0),document.addEventListener(Ii,Ri,!0),document.addEventListener(Ai,Ri,!0),Ni=!0)}function re(t,e){if(e.pointerType!==(e.MSPOINTER_TYPE_MOUSE||"mouse")){e.touches=[];for(var i in kt)e.touches.push(kt[i]);e.changedTouches=[e],t(e)}}function ho(t,e){e.MSPOINTER_TYPE_TOUCH&&e.pointerType===e.MSPOINTER_TYPE_TOUCH&&K(e),re(t,e)}function uo(t){var e={},i,n;for(n in t)i=t[n],e[n]=i&&i.bind?i.bind(t):i;return t=e,e.type="dblclick",e.detail=2,e.isTrusted=!1,e._simulated=!0,e}var lo=200;function co(t,e){t.addEventListener("dblclick",e);var i=0,n;function o(s){if(s.detail!==1){n=s.detail;return}if(!(s.pointerType==="mouse"||s.sourceCapabilities&&!s.sourceCapabilities.firesTouchEvents)){var r=Wi(s);if(!(r.some(function(u){return u instanceof HTMLLabelElement&&u.attributes.for})&&!r.some(function(u){return u instanceof HTMLInputElement||u instanceof HTMLSelectElement}))){var h=Date.now();h-i<=lo?(n++,n===2&&e(uo(s))):n=1,i=h}}}return t.addEventListener("click",o),{dblclick:e,simDblclick:o}}function fo(t,e){t.removeEventListener("dblclick",e.dblclick),t.removeEventListener("click",e.simDblclick)}var Be=ue(["transform","webkitTransform","OTransform","MozTransform","msTransform"]),Wt=ue(["webkitTransition","transition","OTransition","MozTransition","msTransition"]),Di=Wt==="webkitTransition"||Wt==="OTransition"?Wt+"End":"transitionend";function ji(t){return typeof t=="string"?document.getElementById(t):t}function Ut(t,e){var i=t.style[e]||t.currentStyle&&t.currentStyle[e];if((!i||i==="auto")&&document.defaultView){var n=document.defaultView.getComputedStyle(t,null);i=n?n[e]:null}return i==="auto"?null:i}function Z(t,e,i){var n=document.createElement(t);return n.className=e||"",i&&i.appendChild(n),n}function R(t){var e=t.parentNode;e&&e.removeChild(t)}function ae(t){for(;t.firstChild;)t.removeChild(t.firstChild)}function Ot(t){var e=t.parentNode;e&&e.lastChild!==t&&e.appendChild(t)}function Zt(t){var e=t.parentNode;e&&e.firstChild!==t&&e.insertBefore(t,e.firstChild)}function Ne(t,e){if(t.classList!==void 0)return t.classList.contains(e);var i=he(t);return i.length>0&&new RegExp("(^|\\s)"+e+"(\\s|$)").test(i)}function C(t,e){if(t.classList!==void 0)for(var i=ft(e),n=0,o=i.length;n<o;n++)t.classList.add(i[n]);else if(!Ne(t,e)){var s=he(t);Re(t,(s?s+" ":"")+e)}}function D(t,e){t.classList!==void 0?t.classList.remove(e):Re(t,q((" "+he(t)+" ").replace(" "+e+" "," ")))}function Re(t,e){t.className.baseVal===void 0?t.className=e:t.className.baseVal=e}function he(t){return t.correspondingElement&&(t=t.correspondingElement),t.className.baseVal===void 0?t.className:t.className.baseVal}function it(t,e){"opacity"in t.style?t.style.opacity=e:"filter"in t.style&&_o(t,e)}function _o(t,e){var i=!1,n="DXImageTransform.Microsoft.Alpha";try{i=t.filters.item(n)}catch{if(e===1)return}e=Math.round(e*100),i?(i.Enabled=e!==100,i.Opacity=e):t.style.filter+=" progid:"+n+"(opacity="+e+")"}function ue(t){for(var e=document.documentElement.style,i=0;i<t.length;i++)if(t[i]in e)return t[i];return!1}function Ct(t,e,i){var n=e||new b(0,0);t.style[Be]=(y.ie3d?"translate("+n.x+"px,"+n.y+"px)":"translate3d("+n.x+"px,"+n.y+"px,0)")+(i?" scale("+i+")":"")}function H(t,e){t._leaflet_pos=e,y.any3d?Ct(t,e):(t.style.left=e.x+"px",t.style.top=e.y+"px")}function Mt(t){return t._leaflet_pos||new b(0,0)}var Vt,Gt,De;if("onselectstart"in document)Vt=function(){T(window,"selectstart",K)},Gt=function(){B(window,"selectstart",K)};else{var qt=ue(["userSelect","WebkitUserSelect","OUserSelect","MozUserSelect","msUserSelect"]);Vt=function(){if(qt){var t=document.documentElement.style;De=t[qt],t[qt]="none"}},Gt=function(){qt&&(document.documentElement.style[qt]=De,De=void 0)}}function je(){T(window,"dragstart",K)}function Fe(){B(window,"dragstart",K)}var le,He;function We(t){for(;t.tabIndex===-1;)t=t.parentNode;t.style&&(ce(),le=t,He=t.style.outlineStyle,t.style.outlineStyle="none",T(window,"keydown",ce))}function ce(){le&&(le.style.outlineStyle=He,le=void 0,He=void 0,B(window,"keydown",ce))}function Fi(t){do t=t.parentNode;while((!t.offsetWidth||!t.offsetHeight)&&t!==document.body);return t}function Ue(t){var e=t.getBoundingClientRect();return{x:e.width/t.offsetWidth||1,y:e.height/t.offsetHeight||1,boundingClientRect:e}}var mo={__proto__:null,TRANSFORM:Be,TRANSITION:Wt,TRANSITION_END:Di,get:ji,getStyle:Ut,create:Z,remove:R,empty:ae,toFront:Ot,toBack:Zt,hasClass:Ne,addClass:C,removeClass:D,setClass:Re,getClass:he,setOpacity:it,testProp:ue,setTransform:Ct,setPosition:H,getPosition:Mt,get disableTextSelection(){return Vt},get enableTextSelection(){return Gt},disableImageDrag:je,enableImageDrag:Fe,preventOutline:We,restoreOutline:ce,getSizedParentNode:Fi,getScale:Ue};function T(t,e,i,n){if(e&&typeof e=="object")for(var o in e)Ge(t,o,e[o],i);else{e=ft(e);for(var s=0,r=e.length;s<r;s++)Ge(t,e[s],i,n)}return this}var lt="_leaflet_events";function B(t,e,i,n){if(arguments.length===1)Hi(t),delete t[lt];else if(e&&typeof e=="object")for(var o in e)qe(t,o,e[o],i);else if(e=ft(e),arguments.length===2)Hi(t,function(h){return be(e,h)!==-1});else for(var s=0,r=e.length;s<r;s++)qe(t,e[s],i,n);return this}function Hi(t,e){for(var i in t[lt]){var n=i.split(/\d/)[0];(!e||e(n))&&qe(t,n,null,null,i)}}var Ve={mouseenter:"mouseover",mouseleave:"mouseout",wheel:!("onwheel"in window)&&"mousewheel"};function Ge(t,e,i,n){var o=e+w(i)+(n?"_"+w(n):"");if(t[lt]&&t[lt][o])return this;var s=function(h){return i.call(n||t,h||window.event)},r=s;!y.touchNative&&y.pointer&&e.indexOf("touch")===0?s=no(t,e,s):y.touch&&e==="dblclick"?s=co(t,s):"addEventListener"in t?e==="touchstart"||e==="touchmove"||e==="wheel"||e==="mousewheel"?t.addEventListener(Ve[e]||e,s,y.passiveEvents?{passive:!1}:!1):e==="mouseenter"||e==="mouseleave"?(s=function(h){h=h||window.event,Ye(t,h)&&r(h)},t.addEventListener(Ve[e],s,!1)):t.addEventListener(e,r,!1):t.attachEvent("on"+e,s),t[lt]=t[lt]||{},t[lt][o]=s}function qe(t,e,i,n,o){o=o||e+w(i)+(n?"_"+w(n):"");var s=t[lt]&&t[lt][o];if(!s)return this;!y.touchNative&&y.pointer&&e.indexOf("touch")===0?oo(t,e,s):y.touch&&e==="dblclick"?fo(t,s):"removeEventListener"in t?t.removeEventListener(Ve[e]||e,s,!1):t.detachEvent("on"+e,s),t[lt][o]=null}function St(t){return t.stopPropagation?t.stopPropagation():t.originalEvent?t.originalEvent._stopped=!0:t.cancelBubble=!0,this}function Ke(t){return Ge(t,"wheel",St),this}function Kt(t){return T(t,"mousedown touchstart dblclick contextmenu",St),t._leaflet_disable_click=!0,this}function K(t){return t.preventDefault?t.preventDefault():t.returnValue=!1,this}function zt(t){return K(t),St(t),this}function Wi(t){if(t.composedPath)return t.composedPath();for(var e=[],i=t.target;i;)e.push(i),i=i.parentNode;return e}function Ui(t,e){if(!e)return new b(t.clientX,t.clientY);var i=Ue(e),n=i.boundingClientRect;return new b((t.clientX-n.left)/i.x-e.clientLeft,(t.clientY-n.top)/i.y-e.clientTop)}var po=y.linux&&y.chrome?window.devicePixelRatio:y.mac?window.devicePixelRatio*3:window.devicePixelRatio>0?2*window.devicePixelRatio:1;function Vi(t){return y.edge?t.wheelDeltaY/2:t.deltaY&&t.deltaMode===0?-t.deltaY/po:t.deltaY&&t.deltaMode===1?-t.deltaY*20:t.deltaY&&t.deltaMode===2?-t.deltaY*60:t.deltaX||t.deltaZ?0:t.wheelDelta?(t.wheelDeltaY||t.wheelDelta)/2:t.detail&&Math.abs(t.detail)<32765?-t.detail*20:t.detail?t.detail/-32765*60:0}function Ye(t,e){var i=e.relatedTarget;if(!i)return!0;try{for(;i&&i!==t;)i=i.parentNode}catch{return!1}return i!==t}var go={__proto__:null,on:T,off:B,stopPropagation:St,disableScrollPropagation:Ke,disableClickPropagation:Kt,preventDefault:K,stop:zt,getPropagationPath:Wi,getMousePosition:Ui,getWheelDelta:Vi,isExternalTarget:Ye,addListener:T,removeListener:B},Gi=jt.extend({run:function(t,e,i,n){this.stop(),this._el=t,this._inProgress=!0,this._duration=i||.25,this._easeOutPower=1/Math.max(n||.5,.2),this._startPos=Mt(t),this._offset=e.subtract(this._startPos),this._startTime=+new Date,this.fire("start"),this._animate()},stop:function(){this._inProgress&&(this._step(!0),this._complete())},_animate:function(){this._animId=X(this._animate,this),this._step()},_step:function(t){var e=+new Date-this._startTime,i=this._duration*1e3;e<i?this._runFrame(this._easeOut(e/i),t):(this._runFrame(1),this._complete())},_runFrame:function(t,e){var i=this._startPos.add(this._offset.multiplyBy(t));e&&i._round(),H(this._el,i),this.fire("step")},_complete:function(){et(this._animId),this._inProgress=!1,this.fire("end")},_easeOut:function(t){return 1-Math.pow(1-t,this._easeOutPower)}}),k=jt.extend({options:{crs:ze,center:void 0,zoom:void 0,minZoom:void 0,maxZoom:void 0,layers:[],maxBounds:void 0,renderer:void 0,zoomAnimation:!0,zoomAnimationThreshold:4,fadeAnimation:!0,markerZoomAnimation:!0,transform3DLimit:8388608,zoomSnap:1,zoomDelta:1,trackResize:!0},initialize:function(t,e){e=A(this,e),this._handlers=[],this._layers={},this._zoomBoundLayers={},this._sizeChanged=!0,this._initContainer(t),this._initLayout(),this._onResize=v(this._onResize,this),this._initEvents(),e.maxBounds&&this.setMaxBounds(e.maxBounds),e.zoom!==void 0&&(this._zoom=this._limitZoom(e.zoom)),e.center&&e.zoom!==void 0&&this.setView(E(e.center),e.zoom,{reset:!0}),this.callInitHooks(),this._zoomAnimated=Wt&&y.any3d&&!y.mobileOpera&&this.options.zoomAnimation,this._zoomAnimated&&(this._createAnimProxy(),T(this._proxy,Di,this._catchTransitionEnd,this)),this._addLayers(this.options.layers)},setView:function(t,e,i){if(e=e===void 0?this._zoom:this._limitZoom(e),t=this._limitCenter(E(t),e,this.options.maxBounds),i=i||{},this._stop(),this._loaded&&!i.reset&&i!==!0){i.animate!==void 0&&(i.zoom=_({animate:i.animate},i.zoom),i.pan=_({animate:i.animate,duration:i.duration},i.pan));var n=this._zoom!==e?this._tryAnimatedZoom&&this._tryAnimatedZoom(t,e,i.zoom):this._tryAnimatedPan(t,i.pan);if(n)return clearTimeout(this._sizeTimer),this}return this._resetView(t,e,i.pan&&i.pan.noMoveStart),this},setZoom:function(t,e){return this._loaded?this.setView(this.getCenter(),t,{zoom:e}):(this._zoom=t,this)},zoomIn:function(t,e){return t=t||(y.any3d?this.options.zoomDelta:1),this.setZoom(this._zoom+t,e)},zoomOut:function(t,e){return t=t||(y.any3d?this.options.zoomDelta:1),this.setZoom(this._zoom-t,e)},setZoomAround:function(t,e,i){var n=this.getZoomScale(e),o=this.getSize().divideBy(2),s=t instanceof b?t:this.latLngToContainerPoint(t),r=s.subtract(o).multiplyBy(1-1/n),h=this.containerPointToLatLng(o.add(r));return this.setView(h,e,{zoom:i})},_getBoundsCenterZoom:function(t,e){e=e||{},t=t.getBounds?t.getBounds():F(t);var i=P(e.paddingTopLeft||e.padding||[0,0]),n=P(e.paddingBottomRight||e.padding||[0,0]),o=this.getBoundsZoom(t,!1,i.add(n));if(o=typeof e.maxZoom=="number"?Math.min(e.maxZoom,o):o,o===1/0)return{center:t.getCenter(),zoom:o};var s=n.subtract(i).divideBy(2),r=this.project(t.getSouthWest(),o),h=this.project(t.getNorthEast(),o),u=this.unproject(r.add(h).divideBy(2).add(s),o);return{center:u,zoom:o}},fitBounds:function(t,e){if(t=F(t),!t.isValid())throw new Error("Bounds are not valid.");var i=this._getBoundsCenterZoom(t,e);return this.setView(i.center,i.zoom,e)},fitWorld:function(t){return this.fitBounds([[-90,-180],[90,180]],t)},panTo:function(t,e){return this.setView(t,this._zoom,{pan:e})},panBy:function(t,e){if(t=P(t).round(),e=e||{},!t.x&&!t.y)return this.fire("moveend");if(e.animate!==!0&&!this.getSize().contains(t))return this._resetView(this.unproject(this.project(this.getCenter()).add(t)),this.getZoom()),this;if(this._panAnim||(this._panAnim=new Gi,this._panAnim.on({step:this._onPanTransitionStep,end:this._onPanTransitionEnd},this)),e.noMoveStart||this.fire("movestart"),e.animate!==!1){C(this._mapPane,"leaflet-pan-anim");var i=this._getMapPanePos().subtract(t).round();this._panAnim.run(this._mapPane,i,e.duration||.25,e.easeLinearity)}else this._rawPanBy(t),this.fire("move").fire("moveend");return this},flyTo:function(t,e,i){if(i=i||{},i.animate===!1||!y.any3d)return this.setView(t,e,i);this._stop();var n=this.project(this.getCenter()),o=this.project(t),s=this.getSize(),r=this._zoom;t=E(t),e=e===void 0?r:e;var h=Math.max(s.x,s.y),u=h*this.getZoomScale(r,e),c=o.distanceTo(n)||1,p=1.42,x=p*p;function S(W){var Le=W?-1:1,ss=W?u:h,rs=u*u-h*h+Le*x*x*c*c,as=2*ss*x*c,ri=rs/as,Mn=Math.sqrt(ri*ri+1)-ri,hs=Mn<1e-9?-18:Math.log(Mn);return hs}function Y(W){return(Math.exp(W)-Math.exp(-W))/2}function V(W){return(Math.exp(W)+Math.exp(-W))/2}function ot(W){return Y(W)/V(W)}var Q=S(0);function Dt(W){return h*(V(Q)/V(Q+p*W))}function es(W){return h*(V(Q)*ot(Q+p*W)-Y(Q))/x}function is(W){return 1-Math.pow(1-W,1.5)}var ns=Date.now(),Tn=(S(1)-Q)/p,os=i.duration?1e3*i.duration:1e3*Tn*.8;function Cn(){var W=(Date.now()-ns)/os,Le=is(W)*Tn;W<=1?(this._flyToFrame=X(Cn,this),this._move(this.unproject(n.add(o.subtract(n).multiplyBy(es(Le)/c)),r),this.getScaleZoom(h/Dt(Le),r),{flyTo:!0})):this._move(t,e)._moveEnd(!0)}return this._moveStart(!0,i.noMoveStart),Cn.call(this),this},flyToBounds:function(t,e){var i=this._getBoundsCenterZoom(t,e);return this.flyTo(i.center,i.zoom,e)},setMaxBounds:function(t){return t=F(t),this.listens("moveend",this._panInsideMaxBounds)&&this.off("moveend",this._panInsideMaxBounds),t.isValid()?(this.options.maxBounds=t,this._loaded&&this._panInsideMaxBounds(),this.on("moveend",this._panInsideMaxBounds)):(this.options.maxBounds=null,this)},setMinZoom:function(t){var e=this.options.minZoom;return this.options.minZoom=t,this._loaded&&e!==t&&(this.fire("zoomlevelschange"),this.getZoom()<this.options.minZoom)?this.setZoom(t):this},setMaxZoom:function(t){var e=this.options.maxZoom;return this.options.maxZoom=t,this._loaded&&e!==t&&(this.fire("zoomlevelschange"),this.getZoom()>this.options.maxZoom)?this.setZoom(t):this},panInsideBounds:function(t,e){this._enforcingBounds=!0;var i=this.getCenter(),n=this._limitCenter(i,this._zoom,F(t));return i.equals(n)||this.panTo(n,e),this._enforcingBounds=!1,this},panInside:function(t,e){e=e||{};var i=P(e.paddingTopLeft||e.padding||[0,0]),n=P(e.paddingBottomRight||e.padding||[0,0]),o=this.project(this.getCenter()),s=this.project(t),r=this.getPixelBounds(),h=$([r.min.add(i),r.max.subtract(n)]),u=h.getSize();if(!h.contains(s)){this._enforcingBounds=!0;var c=s.subtract(h.getCenter()),p=h.extend(s).getSize().subtract(u);o.x+=c.x<0?-p.x:p.x,o.y+=c.y<0?-p.y:p.y,this.panTo(this.unproject(o),e),this._enforcingBounds=!1}return this},invalidateSize:function(t){if(!this._loaded)return this;t=_({animate:!1,pan:!0},t===!0?{animate:!0}:t);var e=this.getSize();this._sizeChanged=!0,this._lastCenter=null;var i=this.getSize(),n=e.divideBy(2).round(),o=i.divideBy(2).round(),s=n.subtract(o);return!s.x&&!s.y?this:(t.animate&&t.pan?this.panBy(s):(t.pan&&this._rawPanBy(s),this.fire("move"),t.debounceMoveend?(clearTimeout(this._sizeTimer),this._sizeTimer=setTimeout(v(this.fire,this,"moveend"),200)):this.fire("moveend")),this.fire("resize",{oldSize:e,newSize:i}))},stop:function(){return this.setZoom(this._limitZoom(this._zoom)),this.options.zoomSnap||this.fire("viewreset"),this._stop()},locate:function(t){if(t=this._locateOptions=_({timeout:1e4,watch:!1},t),!("geolocation"in navigator))return this._handleGeolocationError({code:0,message:"Geolocation not supported."}),this;var e=v(this._handleGeolocationResponse,this),i=v(this._handleGeolocationError,this);return t.watch?this._locationWatchId=navigator.geolocation.watchPosition(e,i,t):navigator.geolocation.getCurrentPosition(e,i,t),this},stopLocate:function(){return navigator.geolocation&&navigator.geolocation.clearWatch&&navigator.geolocation.clearWatch(this._locationWatchId),this._locateOptions&&(this._locateOptions.setView=!1),this},_handleGeolocationError:function(t){if(this._container._leaflet_id){var e=t.code,i=t.message||(e===1?"permission denied":e===2?"position unavailable":"timeout");this._locateOptions.setView&&!this._loaded&&this.fitWorld(),this.fire("locationerror",{code:e,message:"Geolocation error: "+i+"."})}},_handleGeolocationResponse:function(t){if(this._container._leaflet_id){var e=t.coords.latitude,i=t.coords.longitude,n=new I(e,i),o=n.toBounds(t.coords.accuracy*2),s=this._locateOptions;if(s.setView){var r=this.getBoundsZoom(o);this.setView(n,s.maxZoom?Math.min(r,s.maxZoom):r)}var h={latlng:n,bounds:o,timestamp:t.timestamp};for(var u in t.coords)typeof t.coords[u]=="number"&&(h[u]=t.coords[u]);this.fire("locationfound",h)}},addHandler:function(t,e){if(!e)return this;var i=this[t]=new e(this);return this._handlers.push(i),this.options[t]&&i.enable(),this},remove:function(){if(this._initEvents(!0),this.options.maxBounds&&this.off("moveend",this._panInsideMaxBounds),this._containerId!==this._container._leaflet_id)throw new Error("Map container is being reused by another instance");try{delete this._container._leaflet_id,delete this._containerId}catch{this._container._leaflet_id=void 0,this._containerId=void 0}this._locationWatchId!==void 0&&this.stopLocate(),this._stop(),R(this._mapPane),this._clearControlPos&&this._clearControlPos(),this._resizeRequest&&(et(this._resizeRequest),this._resizeRequest=null),this._clearHandlers(),this._loaded&&this.fire("unload");var t;for(t in this._layers)this._layers[t].remove();for(t in this._panes)R(this._panes[t]);return this._layers=[],this._panes=[],delete this._mapPane,delete this._renderer,this},createPane:function(t,e){var i="leaflet-pane"+(t?" leaflet-"+t.replace("Pane","")+"-pane":""),n=Z("div",i,e||this._mapPane);return t&&(this._panes[t]=n),n},getCenter:function(){return this._checkIfLoaded(),this._lastCenter&&!this._moved()?this._lastCenter.clone():this.layerPointToLatLng(this._getCenterLayerPoint())},getZoom:function(){return this._zoom},getBounds:function(){var t=this.getPixelBounds(),e=this.unproject(t.getBottomLeft()),i=this.unproject(t.getTopRight());return new J(e,i)},getMinZoom:function(){return this.options.minZoom===void 0?this._layersMinZoom||0:this.options.minZoom},getMaxZoom:function(){return this.options.maxZoom===void 0?this._layersMaxZoom===void 0?1/0:this._layersMaxZoom:this.options.maxZoom},getBoundsZoom:function(t,e,i){t=F(t),i=P(i||[0,0]);var n=this.getZoom()||0,o=this.getMinZoom(),s=this.getMaxZoom(),r=t.getNorthWest(),h=t.getSouthEast(),u=this.getSize().subtract(i),c=$(this.project(h,n),this.project(r,n)).getSize(),p=y.any3d?this.options.zoomSnap:1,x=u.x/c.x,S=u.y/c.y,Y=e?Math.max(x,S):Math.min(x,S);return n=this.getScaleZoom(Y,n),p&&(n=Math.round(n/(p/100))*(p/100),n=e?Math.ceil(n/p)*p:Math.floor(n/p)*p),Math.max(o,Math.min(s,n))},getSize:function(){return(!this._size||this._sizeChanged)&&(this._size=new b(this._container.clientWidth||0,this._container.clientHeight||0),this._sizeChanged=!1),this._size.clone()},getPixelBounds:function(t,e){var i=this._getTopLeftPoint(t,e);return new N(i,i.add(this.getSize()))},getPixelOrigin:function(){return this._checkIfLoaded(),this._pixelOrigin},getPixelWorldBounds:function(t){return this.options.crs.getProjectedBounds(t===void 0?this.getZoom():t)},getPane:function(t){return typeof t=="string"?this._panes[t]:t},getPanes:function(){return this._panes},getContainer:function(){return this._container},getZoomScale:function(t,e){var i=this.options.crs;return e=e===void 0?this._zoom:e,i.scale(t)/i.scale(e)},getScaleZoom:function(t,e){var i=this.options.crs;e=e===void 0?this._zoom:e;var n=i.zoom(t*i.scale(e));return isNaN(n)?1/0:n},project:function(t,e){return e=e===void 0?this._zoom:e,this.options.crs.latLngToPoint(E(t),e)},unproject:function(t,e){return e=e===void 0?this._zoom:e,this.options.crs.pointToLatLng(P(t),e)},layerPointToLatLng:function(t){var e=P(t).add(this.getPixelOrigin());return this.unproject(e)},latLngToLayerPoint:function(t){var e=this.project(E(t))._round();return e._subtract(this.getPixelOrigin())},wrapLatLng:function(t){return this.options.crs.wrapLatLng(E(t))},wrapLatLngBounds:function(t){return this.options.crs.wrapLatLngBounds(F(t))},distance:function(t,e){return this.options.crs.distance(E(t),E(e))},containerPointToLayerPoint:function(t){return P(t).subtract(this._getMapPanePos())},layerPointToContainerPoint:function(t){return P(t).add(this._getMapPanePos())},containerPointToLatLng:function(t){var e=this.containerPointToLayerPoint(P(t));return this.layerPointToLatLng(e)},latLngToContainerPoint:function(t){return this.layerPointToContainerPoint(this.latLngToLayerPoint(E(t)))},mouseEventToContainerPoint:function(t){return Ui(t,this._container)},mouseEventToLayerPoint:function(t){return this.containerPointToLayerPoint(this.mouseEventToContainerPoint(t))},mouseEventToLatLng:function(t){return this.layerPointToLatLng(this.mouseEventToLayerPoint(t))},_initContainer:function(t){var e=this._container=ji(t);if(e){if(e._leaflet_id)throw new Error("Map container is already initialized.")}else throw new Error("Map container not found.");T(e,"scroll",this._onScroll,this),this._containerId=w(e)},_initLayout:function(){var t=this._container;this._fadeAnimated=this.options.fadeAnimation&&y.any3d,C(t,"leaflet-container"+(y.touch?" leaflet-touch":"")+(y.retina?" leaflet-retina":"")+(y.ielt9?" leaflet-oldie":"")+(y.safari?" leaflet-safari":"")+(this._fadeAnimated?" leaflet-fade-anim":""));var e=Ut(t,"position");e!=="absolute"&&e!=="relative"&&e!=="fixed"&&e!=="sticky"&&(t.style.position="relative"),this._initPanes(),this._initControlPos&&this._initControlPos()},_initPanes:function(){var t=this._panes={};this._paneRenderers={},this._mapPane=this.createPane("mapPane",this._container),H(this._mapPane,new b(0,0)),this.createPane("tilePane"),this.createPane("overlayPane"),this.createPane("shadowPane"),this.createPane("markerPane"),this.createPane("tooltipPane"),this.createPane("popupPane"),this.options.markerZoomAnimation||(C(t.markerPane,"leaflet-zoom-hide"),C(t.shadowPane,"leaflet-zoom-hide"))},_resetView:function(t,e,i){H(this._mapPane,new b(0,0));var n=!this._loaded;this._loaded=!0,e=this._limitZoom(e),this.fire("viewprereset");var o=this._zoom!==e;this._moveStart(o,i)._move(t,e)._moveEnd(o),this.fire("viewreset"),n&&this.fire("load")},_moveStart:function(t,e){return t&&this.fire("zoomstart"),e||this.fire("movestart"),this},_move:function(t,e,i,n){e===void 0&&(e=this._zoom);var o=this._zoom!==e;return this._zoom=e,this._lastCenter=t,this._pixelOrigin=this._getNewPixelOrigin(t),n?i&&i.pinch&&this.fire("zoom",i):((o||i&&i.pinch)&&this.fire("zoom",i),this.fire("move",i)),this},_moveEnd:function(t){return t&&this.fire("zoomend"),this.fire("moveend")},_stop:function(){return et(this._flyToFrame),this._panAnim&&this._panAnim.stop(),this},_rawPanBy:function(t){H(this._mapPane,this._getMapPanePos().subtract(t))},_getZoomSpan:function(){return this.getMaxZoom()-this.getMinZoom()},_panInsideMaxBounds:function(){this._enforcingBounds||this.panInsideBounds(this.options.maxBounds)},_checkIfLoaded:function(){if(!this._loaded)throw new Error("Set map center and zoom first.")},_initEvents:function(t){this._targets={},this._targets[w(this._container)]=this;var e=t?B:T;e(this._container,"click dblclick mousedown mouseup mouseover mouseout mousemove contextmenu keypress keydown keyup",this._handleDOMEvent,this),this.options.trackResize&&e(window,"resize",this._onResize,this),y.any3d&&this.options.transform3DLimit&&(t?this.off:this.on).call(this,"moveend",this._onMoveEnd)},_onResize:function(){et(this._resizeRequest),this._resizeRequest=X(function(){this.invalidateSize({debounceMoveend:!0})},this)},_onScroll:function(){this._container.scrollTop=0,this._container.scrollLeft=0},_onMoveEnd:function(){var t=this._getMapPanePos();Math.max(Math.abs(t.x),Math.abs(t.y))>=this.options.transform3DLimit&&this._resetView(this.getCenter(),this.getZoom())},_findEventTargets:function(t,e){for(var i=[],n,o=e==="mouseout"||e==="mouseover",s=t.target||t.srcElement,r=!1;s;){if(n=this._targets[w(s)],n&&(e==="click"||e==="preclick")&&this._draggableMoved(n)){r=!0;break}if(n&&n.listens(e,!0)&&(o&&!Ye(s,t)||(i.push(n),o))||s===this._container)break;s=s.parentNode}return!i.length&&!r&&!o&&this.listens(e,!0)&&(i=[this]),i},_isClickDisabled:function(t){for(;t&&t!==this._container;){if(t._leaflet_disable_click)return!0;t=t.parentNode}},_handleDOMEvent:function(t){var e=t.target||t.srcElement;if(!(!this._loaded||e._leaflet_disable_events||t.type==="click"&&this._isClickDisabled(e))){var i=t.type;i==="mousedown"&&We(e),this._fireDOMEvent(t,i)}},_mouseEvents:["click","dblclick","mouseover","mouseout","contextmenu"],_fireDOMEvent:function(t,e,i){if(t.type==="click"){var n=_({},t);n.type="preclick",this._fireDOMEvent(n,n.type,i)}var o=this._findEventTargets(t,e);if(i){for(var s=[],r=0;r<i.length;r++)i[r].listens(e,!0)&&s.push(i[r]);o=s.concat(o)}if(o.length){e==="contextmenu"&&K(t);var h=o[0],u={originalEvent:t};if(t.type!=="keypress"&&t.type!=="keydown"&&t.type!=="keyup"){var c=h.getLatLng&&(!h._radius||h._radius<=10);u.containerPoint=c?this.latLngToContainerPoint(h.getLatLng()):this.mouseEventToContainerPoint(t),u.layerPoint=this.containerPointToLayerPoint(u.containerPoint),u.latlng=c?h.getLatLng():this.layerPointToLatLng(u.layerPoint)}for(r=0;r<o.length;r++)if(o[r].fire(e,u,!0),u.originalEvent._stopped||o[r].options.bubblingMouseEvents===!1&&be(this._mouseEvents,e)!==-1)return}},_draggableMoved:function(t){return t=t.dragging&&t.dragging.enabled()?t:this,t.dragging&&t.dragging.moved()||this.boxZoom&&this.boxZoom.moved()},_clearHandlers:function(){for(var t=0,e=this._handlers.length;t<e;t++)this._handlers[t].disable()},whenReady:function(t,e){return this._loaded?t.call(e||this,{target:this}):this.on("load",t,e),this},_getMapPanePos:function(){return Mt(this._mapPane)||new b(0,0)},_moved:function(){var t=this._getMapPanePos();return t&&!t.equals([0,0])},_getTopLeftPoint:function(t,e){var i=t&&e!==void 0?this._getNewPixelOrigin(t,e):this.getPixelOrigin();return i.subtract(this._getMapPanePos())},_getNewPixelOrigin:function(t,e){var i=this.getSize()._divideBy(2);return this.project(t,e)._subtract(i)._add(this._getMapPanePos())._round()},_latLngToNewLayerPoint:function(t,e,i){var n=this._getNewPixelOrigin(i,e);return this.project(t,e)._subtract(n)},_latLngBoundsToNewLayerBounds:function(t,e,i){var n=this._getNewPixelOrigin(i,e);return $([this.project(t.getSouthWest(),e)._subtract(n),this.project(t.getNorthWest(),e)._subtract(n),this.project(t.getSouthEast(),e)._subtract(n),this.project(t.getNorthEast(),e)._subtract(n)])},_getCenterLayerPoint:function(){return this.containerPointToLayerPoint(this.getSize()._divideBy(2))},_getCenterOffset:function(t){return this.latLngToLayerPoint(t).subtract(this._getCenterLayerPoint())},_limitCenter:function(t,e,i){if(!i)return t;var n=this.project(t,e),o=this.getSize().divideBy(2),s=new N(n.subtract(o),n.add(o)),r=this._getBoundsOffset(s,i,e);return Math.abs(r.x)<=1&&Math.abs(r.y)<=1?t:this.unproject(n.add(r),e)},_limitOffset:function(t,e){if(!e)return t;var i=this.getPixelBounds(),n=new N(i.min.add(t),i.max.add(t));return t.add(this._getBoundsOffset(n,e))},_getBoundsOffset:function(t,e,i){var n=$(this.project(e.getNorthEast(),i),this.project(e.getSouthWest(),i)),o=n.min.subtract(t.min),s=n.max.subtract(t.max),r=this._rebound(o.x,-s.x),h=this._rebound(o.y,-s.y);return new b(r,h)},_rebound:function(t,e){return t+e>0?Math.round(t-e)/2:Math.max(0,Math.ceil(t))-Math.max(0,Math.floor(e))},_limitZoom:function(t){var e=this.getMinZoom(),i=this.getMaxZoom(),n=y.any3d?this.options.zoomSnap:1;return n&&(t=Math.round(t/n)*n),Math.max(e,Math.min(i,t))},_onPanTransitionStep:function(){this.fire("move")},_onPanTransitionEnd:function(){D(this._mapPane,"leaflet-pan-anim"),this.fire("moveend")},_tryAnimatedPan:function(t,e){var i=this._getCenterOffset(t)._trunc();return(e&&e.animate)!==!0&&!this.getSize().contains(i)?!1:(this.panBy(i,e),!0)},_createAnimProxy:function(){var t=this._proxy=Z("div","leaflet-proxy leaflet-zoom-animated");this._panes.mapPane.appendChild(t),this.on("zoomanim",function(e){var i=Be,n=this._proxy.style[i];Ct(this._proxy,this.project(e.center,e.zoom),this.getZoomScale(e.zoom,1)),n===this._proxy.style[i]&&this._animatingZoom&&this._onZoomTransitionEnd()},this),this.on("load moveend",this._animMoveEnd,this),this._on("unload",this._destroyAnimProxy,this)},_destroyAnimProxy:function(){R(this._proxy),this.off("load moveend",this._animMoveEnd,this),delete this._proxy},_animMoveEnd:function(){var t=this.getCenter(),e=this.getZoom();Ct(this._proxy,this.project(t,e),this.getZoomScale(e,1))},_catchTransitionEnd:function(t){this._animatingZoom&&t.propertyName.indexOf("transform")>=0&&this._onZoomTransitionEnd()},_nothingToAnimate:function(){return!this._container.getElementsByClassName("leaflet-zoom-animated").length},_tryAnimatedZoom:function(t,e,i){if(this._animatingZoom)return!0;if(i=i||{},!this._zoomAnimated||i.animate===!1||this._nothingToAnimate()||Math.abs(e-this._zoom)>this.options.zoomAnimationThreshold)return!1;var n=this.getZoomScale(e),o=this._getCenterOffset(t)._divideBy(1-1/n);return i.animate!==!0&&!this.getSize().contains(o)?!1:(X(function(){this._moveStart(!0,i.noMoveStart||!1)._animateZoom(t,e,!0)},this),!0)},_animateZoom:function(t,e,i,n){this._mapPane&&(i&&(this._animatingZoom=!0,this._animateToCenter=t,this._animateToZoom=e,C(this._mapPane,"leaflet-zoom-anim")),this.fire("zoomanim",{center:t,zoom:e,noUpdate:n}),this._tempFireZoomEvent||(this._tempFireZoomEvent=this._zoom!==this._animateToZoom),this._move(this._animateToCenter,this._animateToZoom,void 0,!0),setTimeout(v(this._onZoomTransitionEnd,this),250))},_onZoomTransitionEnd:function(){this._animatingZoom&&(this._mapPane&&D(this._mapPane,"leaflet-zoom-anim"),this._animatingZoom=!1,this._move(this._animateToCenter,this._animateToZoom,void 0,!0),this._tempFireZoomEvent&&this.fire("zoom"),delete this._tempFireZoomEvent,this.fire("move"),this._moveEnd(!0))}});function vo(t,e){return new k(t,e)}var rt=_t.extend({options:{position:"topright"},initialize:function(t){A(this,t)},getPosition:function(){return this.options.position},setPosition:function(t){var e=this._map;return e&&e.removeControl(this),this.options.position=t,e&&e.addControl(this),this},getContainer:function(){return this._container},addTo:function(t){this.remove(),this._map=t;var e=this._container=this.onAdd(t),i=this.getPosition(),n=t._controlCorners[i];return C(e,"leaflet-control"),i.indexOf("bottom")!==-1?n.insertBefore(e,n.firstChild):n.appendChild(e),this._map.on("unload",this.remove,this),this},remove:function(){return this._map?(R(this._container),this.onRemove&&this.onRemove(this._map),this._map.off("unload",this.remove,this),this._map=null,this):this},_refocusOnMap:function(t){this._map&&t&&t.screenX>0&&t.screenY>0&&this._map.getContainer().focus()}}),Yt=function(t){return new rt(t)};k.include({addControl:function(t){return t.addTo(this),this},removeControl:function(t){return t.remove(),this},_initControlPos:function(){var t=this._controlCorners={},e="leaflet-",i=this._controlContainer=Z("div",e+"control-container",this._container);function n(o,s){var r=e+o+" "+e+s;t[o+s]=Z("div",r,i)}n("top","left"),n("top","right"),n("bottom","left"),n("bottom","right")},_clearControlPos:function(){for(var t in this._controlCorners)R(this._controlCorners[t]);R(this._controlContainer),delete this._controlCorners,delete this._controlContainer}});var qi=rt.extend({options:{collapsed:!0,position:"topright",autoZIndex:!0,hideSingleBase:!1,sortLayers:!1,sortFunction:function(t,e,i,n){return i<n?-1:n<i?1:0}},initialize:function(t,e,i){A(this,i),this._layerControlInputs=[],this._layers=[],this._lastZIndex=0,this._handlingClick=!1,this._preventClick=!1;for(var n in t)this._addLayer(t[n],n);for(n in e)this._addLayer(e[n],n,!0)},onAdd:function(t){this._initLayout(),this._update(),this._map=t,t.on("zoomend",this._checkDisabledLayers,this);for(var e=0;e<this._layers.length;e++)this._layers[e].layer.on("add remove",this._onLayerChange,this);return this._container},addTo:function(t){return rt.prototype.addTo.call(this,t),this._expandIfNotCollapsed()},onRemove:function(){this._map.off("zoomend",this._checkDisabledLayers,this);for(var t=0;t<this._layers.length;t++)this._layers[t].layer.off("add remove",this._onLayerChange,this)},addBaseLayer:function(t,e){return this._addLayer(t,e),this._map?this._update():this},addOverlay:function(t,e){return this._addLayer(t,e,!0),this._map?this._update():this},removeLayer:function(t){t.off("add remove",this._onLayerChange,this);var e=this._getLayer(w(t));return e&&this._layers.splice(this._layers.indexOf(e),1),this._map?this._update():this},expand:function(){C(this._container,"leaflet-control-layers-expanded"),this._section.style.height=null;var t=this._map.getSize().y-(this._container.offsetTop+50);return t<this._section.clientHeight?(C(this._section,"leaflet-control-layers-scrollbar"),this._section.style.height=t+"px"):D(this._section,"leaflet-control-layers-scrollbar"),this._checkDisabledLayers(),this},collapse:function(){return D(this._container,"leaflet-control-layers-expanded"),this},_initLayout:function(){var t="leaflet-control-layers",e=this._container=Z("div",t),i=this.options.collapsed;e.setAttribute("aria-haspopup",!0),Kt(e),Ke(e);var n=this._section=Z("section",t+"-list");i&&(this._map.on("click",this.collapse,this),T(e,{mouseenter:this._expandSafely,mouseleave:this.collapse},this));var o=this._layersLink=Z("a",t+"-toggle",e);o.href="#",o.title="Layers",o.setAttribute("role","button"),T(o,{keydown:function(s){s.keyCode===13&&this._expandSafely()},click:function(s){K(s),this._expandSafely()}},this),i||this.expand(),this._baseLayersList=Z("div",t+"-base",n),this._separator=Z("div",t+"-separator",n),this._overlaysList=Z("div",t+"-overlays",n),e.appendChild(n)},_getLayer:function(t){for(var e=0;e<this._layers.length;e++)if(this._layers[e]&&w(this._layers[e].layer)===t)return this._layers[e]},_addLayer:function(t,e,i){this._map&&t.on("add remove",this._onLayerChange,this),this._layers.push({layer:t,name:e,overlay:i}),this.options.sortLayers&&this._layers.sort(v(function(n,o){return this.options.sortFunction(n.layer,o.layer,n.name,o.name)},this)),this.options.autoZIndex&&t.setZIndex&&(this._lastZIndex++,t.setZIndex(this._lastZIndex)),this._expandIfNotCollapsed()},_update:function(){if(!this._container)return this;ae(this._baseLayersList),ae(this._overlaysList),this._layerControlInputs=[];var t,e,i,n,o=0;for(i=0;i<this._layers.length;i++)n=this._layers[i],this._addItem(n),e=e||n.overlay,t=t||!n.overlay,o+=n.overlay?0:1;return this.options.hideSingleBase&&(t=t&&o>1,this._baseLayersList.style.display=t?"":"none"),this._separator.style.display=e&&t?"":"none",this},_onLayerChange:function(t){this._handlingClick||this._update();var e=this._getLayer(w(t.target)),i=e.overlay?t.type==="add"?"overlayadd":"overlayremove":t.type==="add"?"baselayerchange":null;i&&this._map.fire(i,e)},_createRadioElement:function(t,e){var i='<input type="radio" class="leaflet-control-layers-selector" name="'+t+'"'+(e?' checked="checked"':"")+"/>",n=document.createElement("div");return n.innerHTML=i,n.firstChild},_addItem:function(t){var e=document.createElement("label"),i=this._map.hasLayer(t.layer),n;t.overlay?(n=document.createElement("input"),n.type="checkbox",n.className="leaflet-control-layers-selector",n.defaultChecked=i):n=this._createRadioElement("leaflet-base-layers_"+w(this),i),this._layerControlInputs.push(n),n.layerId=w(t.layer),T(n,"click",this._onInputClick,this);var o=document.createElement("span");o.innerHTML=" "+t.name;var s=document.createElement("span");e.appendChild(s),s.appendChild(n),s.appendChild(o);var r=t.overlay?this._overlaysList:this._baseLayersList;return r.appendChild(e),this._checkDisabledLayers(),e},_onInputClick:function(){if(!this._preventClick){var t=this._layerControlInputs,e,i,n=[],o=[];this._handlingClick=!0;for(var s=t.length-1;s>=0;s--)e=t[s],i=this._getLayer(e.layerId).layer,e.checked?n.push(i):e.checked||o.push(i);for(s=0;s<o.length;s++)this._map.hasLayer(o[s])&&this._map.removeLayer(o[s]);for(s=0;s<n.length;s++)this._map.hasLayer(n[s])||this._map.addLayer(n[s]);this._handlingClick=!1,this._refocusOnMap()}},_checkDisabledLayers:function(){for(var t=this._layerControlInputs,e,i,n=this._map.getZoom(),o=t.length-1;o>=0;o--)e=t[o],i=this._getLayer(e.layerId).layer,e.disabled=i.options.minZoom!==void 0&&n<i.options.minZoom||i.options.maxZoom!==void 0&&n>i.options.maxZoom},_expandIfNotCollapsed:function(){return this._map&&!this.options.collapsed&&this.expand(),this},_expandSafely:function(){var t=this._section;this._preventClick=!0,T(t,"click",K),this.expand();var e=this;setTimeout(function(){B(t,"click",K),e._preventClick=!1})}}),yo=function(t,e,i){return new qi(t,e,i)},Xe=rt.extend({options:{position:"topleft",zoomInText:'<span aria-hidden="true">+</span>',zoomInTitle:"Zoom in",zoomOutText:'<span aria-hidden="true">&#x2212;</span>',zoomOutTitle:"Zoom out"},onAdd:function(t){var e="leaflet-control-zoom",i=Z("div",e+" leaflet-bar"),n=this.options;return this._zoomInButton=this._createButton(n.zoomInText,n.zoomInTitle,e+"-in",i,this._zoomIn),this._zoomOutButton=this._createButton(n.zoomOutText,n.zoomOutTitle,e+"-out",i,this._zoomOut),this._updateDisabled(),t.on("zoomend zoomlevelschange",this._updateDisabled,this),i},onRemove:function(t){t.off("zoomend zoomlevelschange",this._updateDisabled,this)},disable:function(){return this._disabled=!0,this._updateDisabled(),this},enable:function(){return this._disabled=!1,this._updateDisabled(),this},_zoomIn:function(t){!this._disabled&&this._map._zoom<this._map.getMaxZoom()&&this._map.zoomIn(this._map.options.zoomDelta*(t.shiftKey?3:1))},_zoomOut:function(t){!this._disabled&&this._map._zoom>this._map.getMinZoom()&&this._map.zoomOut(this._map.options.zoomDelta*(t.shiftKey?3:1))},_createButton:function(t,e,i,n,o){var s=Z("a",i,n);return s.innerHTML=t,s.href="#",s.title=e,s.setAttribute("role","button"),s.setAttribute("aria-label",e),Kt(s),T(s,"click",zt),T(s,"click",o,this),T(s,"click",this._refocusOnMap,this),s},_updateDisabled:function(){var t=this._map,e="leaflet-disabled";D(this._zoomInButton,e),D(this._zoomOutButton,e),this._zoomInButton.setAttribute("aria-disabled","false"),this._zoomOutButton.setAttribute("aria-disabled","false"),(this._disabled||t._zoom===t.getMinZoom())&&(C(this._zoomOutButton,e),this._zoomOutButton.setAttribute("aria-disabled","true")),(this._disabled||t._zoom===t.getMaxZoom())&&(C(this._zoomInButton,e),this._zoomInButton.setAttribute("aria-disabled","true"))}});k.mergeOptions({zoomControl:!0}),k.addInitHook(function(){this.options.zoomControl&&(this.zoomControl=new Xe,this.addControl(this.zoomControl))});var xo=function(t){return new Xe(t)},Ki=rt.extend({options:{position:"bottomleft",maxWidth:100,metric:!0,imperial:!0},onAdd:function(t){var e="leaflet-control-scale",i=Z("div",e),n=this.options;return this._addScales(n,e+"-line",i),t.on(n.updateWhenIdle?"moveend":"move",this._update,this),t.whenReady(this._update,this),i},onRemove:function(t){t.off(this.options.updateWhenIdle?"moveend":"move",this._update,this)},_addScales:function(t,e,i){t.metric&&(this._mScale=Z("div",e,i)),t.imperial&&(this._iScale=Z("div",e,i))},_update:function(){var t=this._map,e=t.getSize().y/2,i=t.distance(t.containerPointToLatLng([0,e]),t.containerPointToLatLng([this.options.maxWidth,e]));this._updateScales(i)},_updateScales:function(t){this.options.metric&&t&&this._updateMetric(t),this.options.imperial&&t&&this._updateImperial(t)},_updateMetric:function(t){var e=this._getRoundNum(t),i=e<1e3?e+" m":e/1e3+" km";this._updateScale(this._mScale,i,e/t)},_updateImperial:function(t){var e=t*3.2808399,i,n,o;e>5280?(i=e/5280,n=this._getRoundNum(i),this._updateScale(this._iScale,n+" mi",n/i)):(o=this._getRoundNum(e),this._updateScale(this._iScale,o+" ft",o/e))},_updateScale:function(t,e,i){t.style.width=Math.round(this.options.maxWidth*i)+"px",t.innerHTML=e},_getRoundNum:function(t){var e=Math.pow(10,(Math.floor(t)+"").length-1),i=t/e;return i=i>=10?10:i>=5?5:i>=3?3:i>=2?2:1,e*i}}),wo=function(t){return new Ki(t)},Po='<svg aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="12" height="8" viewBox="0 0 12 8" class="leaflet-attribution-flag"><path fill="#4C7BE1" d="M0 0h12v4H0z"/><path fill="#FFD500" d="M0 4h12v3H0z"/><path fill="#E0BC00" d="M0 7h12v1H0z"/></svg>',$e=rt.extend({options:{position:"bottomright",prefix:'<a href="https://leafletjs.com" title="A JavaScript library for interactive maps">'+(y.inlineSvg?Po+" ":"")+"Leaflet</a>"},initialize:function(t){A(this,t),this._attributions={}},onAdd:function(t){t.attributionControl=this,this._container=Z("div","leaflet-control-attribution"),Kt(this._container);for(var e in t._layers)t._layers[e].getAttribution&&this.addAttribution(t._layers[e].getAttribution());return this._update(),t.on("layeradd",this._addAttribution,this),this._container},onRemove:function(t){t.off("layeradd",this._addAttribution,this)},_addAttribution:function(t){t.layer.getAttribution&&(this.addAttribution(t.layer.getAttribution()),t.layer.once("remove",function(){this.removeAttribution(t.layer.getAttribution())},this))},setPrefix:function(t){return this.options.prefix=t,this._update(),this},addAttribution:function(t){return t?(this._attributions[t]||(this._attributions[t]=0),this._attributions[t]++,this._update(),this):this},removeAttribution:function(t){return t?(this._attributions[t]&&(this._attributions[t]--,this._update()),this):this},_update:function(){if(this._map){var t=[];for(var e in this._attributions)this._attributions[e]&&t.push(e);var i=[];this.options.prefix&&i.push(this.options.prefix),t.length&&i.push(t.join(", ")),this._container.innerHTML=i.join(' <span aria-hidden="true">|</span> ')}}});k.mergeOptions({attributionControl:!0}),k.addInitHook(function(){this.options.attributionControl&&new $e().addTo(this)});var Lo=function(t){return new $e(t)};rt.Layers=qi,rt.Zoom=Xe,rt.Scale=Ki,rt.Attribution=$e,Yt.layers=yo,Yt.zoom=xo,Yt.scale=wo,Yt.attribution=Lo;var ct=_t.extend({initialize:function(t){this._map=t},enable:function(){return this._enabled?this:(this._enabled=!0,this.addHooks(),this)},disable:function(){return this._enabled?(this._enabled=!1,this.removeHooks(),this):this},enabled:function(){return!!this._enabled}});ct.addTo=function(t,e){return t.addHandler(e,this),this};var bo={Events:tt},Yi=y.touch?"touchstart mousedown":"mousedown",wt=jt.extend({options:{clickTolerance:3},initialize:function(t,e,i,n){A(this,n),this._element=t,this._dragStartTarget=e||t,this._preventOutline=i},enable:function(){this._enabled||(T(this._dragStartTarget,Yi,this._onDown,this),this._enabled=!0)},disable:function(){this._enabled&&(wt._dragging===this&&this.finishDrag(!0),B(this._dragStartTarget,Yi,this._onDown,this),this._enabled=!1,this._moved=!1)},_onDown:function(t){if(this._enabled&&(this._moved=!1,!Ne(this._element,"leaflet-zoom-anim"))){if(t.touches&&t.touches.length!==1){wt._dragging===this&&this.finishDrag();return}if(!(wt._dragging||t.shiftKey||t.which!==1&&t.button!==1&&!t.touches)&&(wt._dragging=this,this._preventOutline&&We(this._element),je(),Vt(),!this._moving)){this.fire("down");var e=t.touches?t.touches[0]:t,i=Fi(this._element);this._startPoint=new b(e.clientX,e.clientY),this._startPos=Mt(this._element),this._parentScale=Ue(i);var n=t.type==="mousedown";T(document,n?"mousemove":"touchmove",this._onMove,this),T(document,n?"mouseup":"touchend touchcancel",this._onUp,this)}}},_onMove:function(t){if(this._enabled){if(t.touches&&t.touches.length>1){this._moved=!0;return}var e=t.touches&&t.touches.length===1?t.touches[0]:t,i=new b(e.clientX,e.clientY)._subtract(this._startPoint);!i.x&&!i.y||Math.abs(i.x)+Math.abs(i.y)<this.options.clickTolerance||(i.x/=this._parentScale.x,i.y/=this._parentScale.y,K(t),this._moved||(this.fire("dragstart"),this._moved=!0,C(document.body,"leaflet-dragging"),this._lastTarget=t.target||t.srcElement,window.SVGElementInstance&&this._lastTarget instanceof window.SVGElementInstance&&(this._lastTarget=this._lastTarget.correspondingUseElement),C(this._lastTarget,"leaflet-drag-target")),this._newPos=this._startPos.add(i),this._moving=!0,this._lastEvent=t,this._updatePosition())}},_updatePosition:function(){var t={originalEvent:this._lastEvent};this.fire("predrag",t),H(this._element,this._newPos),this.fire("drag",t)},_onUp:function(){this._enabled&&this.finishDrag()},finishDrag:function(t){D(document.body,"leaflet-dragging"),this._lastTarget&&(D(this._lastTarget,"leaflet-drag-target"),this._lastTarget=null),B(document,"mousemove touchmove",this._onMove,this),B(document,"mouseup touchend touchcancel",this._onUp,this),Fe(),Gt();var e=this._moved&&this._moving;this._moving=!1,wt._dragging=!1,e&&this.fire("dragend",{noInertia:t,distance:this._newPos.distanceTo(this._startPos)})}});function Xi(t,e,i){var n,o=[1,4,2,8],s,r,h,u,c,p,x,S;for(s=0,p=t.length;s<p;s++)t[s]._code=Et(t[s],e);for(h=0;h<4;h++){for(x=o[h],n=[],s=0,p=t.length,r=p-1;s<p;r=s++)u=t[s],c=t[r],u._code&x?c._code&x||(S=de(c,u,x,e,i),S._code=Et(S,e),n.push(S)):(c._code&x&&(S=de(c,u,x,e,i),S._code=Et(S,e),n.push(S)),n.push(u));t=n}return t}function $i(t,e){var i,n,o,s,r,h,u,c,p;if(!t||t.length===0)throw new Error("latlngs not passed");nt(t)||(console.warn("latlngs are not flat! Only the first ring will be used"),t=t[0]);var x=E([0,0]),S=F(t),Y=S.getNorthWest().distanceTo(S.getSouthWest())*S.getNorthEast().distanceTo(S.getNorthWest());Y<1700&&(x=Je(t));var V=t.length,ot=[];for(i=0;i<V;i++){var Q=E(t[i]);ot.push(e.project(E([Q.lat-x.lat,Q.lng-x.lng])))}for(h=u=c=0,i=0,n=V-1;i<V;n=i++)o=ot[i],s=ot[n],r=o.y*s.x-s.y*o.x,u+=(o.x+s.x)*r,c+=(o.y+s.y)*r,h+=r*3;h===0?p=ot[0]:p=[u/h,c/h];var Dt=e.unproject(P(p));return E([Dt.lat+x.lat,Dt.lng+x.lng])}function Je(t){for(var e=0,i=0,n=0,o=0;o<t.length;o++){var s=E(t[o]);e+=s.lat,i+=s.lng,n++}return E([e/n,i/n])}var To={__proto__:null,clipPolygon:Xi,polygonCenter:$i,centroid:Je};function Ji(t,e){if(!e||!t.length)return t.slice();var i=e*e;return t=So(t,i),t=Mo(t,i),t}function Qi(t,e,i){return Math.sqrt(Xt(t,e,i,!0))}function Co(t,e,i){return Xt(t,e,i)}function Mo(t,e){var i=t.length,n=typeof Uint8Array<"u"?Uint8Array:Array,o=new n(i);o[0]=o[i-1]=1,Qe(t,o,e,0,i-1);var s,r=[];for(s=0;s<i;s++)o[s]&&r.push(t[s]);return r}function Qe(t,e,i,n,o){var s=0,r,h,u;for(h=n+1;h<=o-1;h++)u=Xt(t[h],t[n],t[o],!0),u>s&&(r=h,s=u);s>i&&(e[r]=1,Qe(t,e,i,n,r),Qe(t,e,i,r,o))}function So(t,e){for(var i=[t[0]],n=1,o=0,s=t.length;n<s;n++)zo(t[n],t[o])>e&&(i.push(t[n]),o=n);return o<s-1&&i.push(t[s-1]),i}var tn;function en(t,e,i,n,o){var s=n?tn:Et(t,i),r=Et(e,i),h,u,c;for(tn=r;;){if(!(s|r))return[t,e];if(s&r)return!1;h=s||r,u=de(t,e,h,i,o),c=Et(u,i),h===s?(t=u,s=c):(e=u,r=c)}}function de(t,e,i,n,o){var s=e.x-t.x,r=e.y-t.y,h=n.min,u=n.max,c,p;return i&8?(c=t.x+s*(u.y-t.y)/r,p=u.y):i&4?(c=t.x+s*(h.y-t.y)/r,p=h.y):i&2?(c=u.x,p=t.y+r*(u.x-t.x)/s):i&1&&(c=h.x,p=t.y+r*(h.x-t.x)/s),new b(c,p,o)}function Et(t,e){var i=0;return t.x<e.min.x?i|=1:t.x>e.max.x&&(i|=2),t.y<e.min.y?i|=4:t.y>e.max.y&&(i|=8),i}function zo(t,e){var i=e.x-t.x,n=e.y-t.y;return i*i+n*n}function Xt(t,e,i,n){var o=e.x,s=e.y,r=i.x-o,h=i.y-s,u=r*r+h*h,c;return u>0&&(c=((t.x-o)*r+(t.y-s)*h)/u,c>1?(o=i.x,s=i.y):c>0&&(o+=r*c,s+=h*c)),r=t.x-o,h=t.y-s,n?r*r+h*h:new b(o,s)}function nt(t){return!st(t[0])||typeof t[0][0]!="object"&&typeof t[0][0]<"u"}function nn(t){return console.warn("Deprecated use of _flat, please use L.LineUtil.isFlat instead."),nt(t)}function on(t,e){var i,n,o,s,r,h,u,c;if(!t||t.length===0)throw new Error("latlngs not passed");nt(t)||(console.warn("latlngs are not flat! Only the first ring will be used"),t=t[0]);var p=E([0,0]),x=F(t),S=x.getNorthWest().distanceTo(x.getSouthWest())*x.getNorthEast().distanceTo(x.getNorthWest());S<1700&&(p=Je(t));var Y=t.length,V=[];for(i=0;i<Y;i++){var ot=E(t[i]);V.push(e.project(E([ot.lat-p.lat,ot.lng-p.lng])))}for(i=0,n=0;i<Y-1;i++)n+=V[i].distanceTo(V[i+1])/2;if(n===0)c=V[0];else for(i=0,s=0;i<Y-1;i++)if(r=V[i],h=V[i+1],o=r.distanceTo(h),s+=o,s>n){u=(s-n)/o,c=[h.x-u*(h.x-r.x),h.y-u*(h.y-r.y)];break}var Q=e.unproject(P(c));return E([Q.lat+p.lat,Q.lng+p.lng])}var Eo={__proto__:null,simplify:Ji,pointToSegmentDistance:Qi,closestPointOnSegment:Co,clipSegment:en,_getEdgeIntersection:de,_getBitCode:Et,_sqClosestPointOnSegment:Xt,isFlat:nt,_flat:nn,polylineCenter:on},ti={project:function(t){return new b(t.lng,t.lat)},unproject:function(t){return new I(t.y,t.x)},bounds:new N([-180,-90],[180,90])},ei={R:6378137,R_MINOR:6356752314245179e-9,bounds:new N([-2003750834279e-5,-1549657073972e-5],[2003750834279e-5,1876465623138e-5]),project:function(t){var e=Math.PI/180,i=this.R,n=t.lat*e,o=this.R_MINOR/i,s=Math.sqrt(1-o*o),r=s*Math.sin(n),h=Math.tan(Math.PI/4-n/2)/Math.pow((1-r)/(1+r),s/2);return n=-i*Math.log(Math.max(h,1e-10)),new b(t.lng*e*i,n)},unproject:function(t){for(var e=180/Math.PI,i=this.R,n=this.R_MINOR/i,o=Math.sqrt(1-n*n),s=Math.exp(-t.y/i),r=Math.PI/2-2*Math.atan(s),h=0,u=.1,c;h<15&&Math.abs(u)>1e-7;h++)c=o*Math.sin(r),c=Math.pow((1-c)/(1+c),o/2),u=Math.PI/2-2*Math.atan(s*c)-r,r+=u;return new I(r*e,t.x*e/i)}},ko={__proto__:null,LonLat:ti,Mercator:ei,SphericalMercator:Me},Oo=_({},xt,{code:"EPSG:3395",projection:ei,transformation:function(){var t=.5/(Math.PI*ei.R);return Ft(t,.5,-t,.5)}()}),sn=_({},xt,{code:"EPSG:4326",projection:ti,transformation:Ft(1/180,1,-1/180,.5)}),Zo=_({},mt,{projection:ti,transformation:Ft(1,0,-1,0),scale:function(t){return Math.pow(2,t)},zoom:function(t){return Math.log(t)/Math.LN2},distance:function(t,e){var i=e.lng-t.lng,n=e.lat-t.lat;return Math.sqrt(i*i+n*n)},infinite:!0});mt.Earth=xt,mt.EPSG3395=Oo,mt.EPSG3857=ze,mt.EPSG900913=Rn,mt.EPSG4326=sn,mt.Simple=Zo;var at=jt.extend({options:{pane:"overlayPane",attribution:null,bubblingMouseEvents:!0},addTo:function(t){return t.addLayer(this),this},remove:function(){return this.removeFrom(this._map||this._mapToAdd)},removeFrom:function(t){return t&&t.removeLayer(this),this},getPane:function(t){return this._map.getPane(t?this.options[t]||t:this.options.pane)},addInteractiveTarget:function(t){return this._map._targets[w(t)]=this,this},removeInteractiveTarget:function(t){return delete this._map._targets[w(t)],this},getAttribution:function(){return this.options.attribution},_layerAdd:function(t){var e=t.target;if(e.hasLayer(this)){if(this._map=e,this._zoomAnimated=e._zoomAnimated,this.getEvents){var i=this.getEvents();e.on(i,this),this.once("remove",function(){e.off(i,this)},this)}this.onAdd(e),this.fire("add"),e.fire("layeradd",{layer:this})}}});k.include({addLayer:function(t){if(!t._layerAdd)throw new Error("The provided object is not a Layer.");var e=w(t);return this._layers[e]?this:(this._layers[e]=t,t._mapToAdd=this,t.beforeAdd&&t.beforeAdd(this),this.whenReady(t._layerAdd,t),this)},removeLayer:function(t){var e=w(t);return this._layers[e]?(this._loaded&&t.onRemove(this),delete this._layers[e],this._loaded&&(this.fire("layerremove",{layer:t}),t.fire("remove")),t._map=t._mapToAdd=null,this):this},hasLayer:function(t){return w(t)in this._layers},eachLayer:function(t,e){for(var i in this._layers)t.call(e,this._layers[i]);return this},_addLayers:function(t){t=t?st(t)?t:[t]:[];for(var e=0,i=t.length;e<i;e++)this.addLayer(t[e])},_addZoomLimit:function(t){(!isNaN(t.options.maxZoom)||!isNaN(t.options.minZoom))&&(this._zoomBoundLayers[w(t)]=t,this._updateZoomLevels())},_removeZoomLimit:function(t){var e=w(t);this._zoomBoundLayers[e]&&(delete this._zoomBoundLayers[e],this._updateZoomLevels())},_updateZoomLevels:function(){var t=1/0,e=-1/0,i=this._getZoomSpan();for(var n in this._zoomBoundLayers){var o=this._zoomBoundLayers[n].options;t=o.minZoom===void 0?t:Math.min(t,o.minZoom),e=o.maxZoom===void 0?e:Math.max(e,o.maxZoom)}this._layersMaxZoom=e===-1/0?void 0:e,this._layersMinZoom=t===1/0?void 0:t,i!==this._getZoomSpan()&&this.fire("zoomlevelschange"),this.options.maxZoom===void 0&&this._layersMaxZoom&&this.getZoom()>this._layersMaxZoom&&this.setZoom(this._layersMaxZoom),this.options.minZoom===void 0&&this._layersMinZoom&&this.getZoom()<this._layersMinZoom&&this.setZoom(this._layersMinZoom)}});var It=at.extend({initialize:function(t,e){A(this,e),this._layers={};var i,n;if(t)for(i=0,n=t.length;i<n;i++)this.addLayer(t[i])},addLayer:function(t){var e=this.getLayerId(t);return this._layers[e]=t,this._map&&this._map.addLayer(t),this},removeLayer:function(t){var e=t in this._layers?t:this.getLayerId(t);return this._map&&this._layers[e]&&this._map.removeLayer(this._layers[e]),delete this._layers[e],this},hasLayer:function(t){var e=typeof t=="number"?t:this.getLayerId(t);return e in this._layers},clearLayers:function(){return this.eachLayer(this.removeLayer,this)},invoke:function(t){var e=Array.prototype.slice.call(arguments,1),i,n;for(i in this._layers)n=this._layers[i],n[t]&&n[t].apply(n,e);return this},onAdd:function(t){this.eachLayer(t.addLayer,t)},onRemove:function(t){this.eachLayer(t.removeLayer,t)},eachLayer:function(t,e){for(var i in this._layers)t.call(e,this._layers[i]);return this},getLayer:function(t){return this._layers[t]},getLayers:function(){var t=[];return this.eachLayer(t.push,t),t},setZIndex:function(t){return this.invoke("setZIndex",t)},getLayerId:function(t){return w(t)}}),Io=function(t,e){return new It(t,e)},pt=It.extend({addLayer:function(t){return this.hasLayer(t)?this:(t.addEventParent(this),It.prototype.addLayer.call(this,t),this.fire("layeradd",{layer:t}))},removeLayer:function(t){return this.hasLayer(t)?(t in this._layers&&(t=this._layers[t]),t.removeEventParent(this),It.prototype.removeLayer.call(this,t),this.fire("layerremove",{layer:t})):this},setStyle:function(t){return this.invoke("setStyle",t)},bringToFront:function(){return this.invoke("bringToFront")},bringToBack:function(){return this.invoke("bringToBack")},getBounds:function(){var t=new J;for(var e in this._layers){var i=this._layers[e];t.extend(i.getBounds?i.getBounds():i.getLatLng())}return t}}),Ao=function(t,e){return new pt(t,e)},At=_t.extend({options:{popupAnchor:[0,0],tooltipAnchor:[0,0],crossOrigin:!1},initialize:function(t){A(this,t)},createIcon:function(t){return this._createIcon("icon",t)},createShadow:function(t){return this._createIcon("shadow",t)},_createIcon:function(t,e){var i=this._getIconUrl(t);if(!i){if(t==="icon")throw new Error("iconUrl not set in Icon options (see the docs).");return null}var n=this._createImg(i,e&&e.tagName==="IMG"?e:null);return this._setIconStyles(n,t),(this.options.crossOrigin||this.options.crossOrigin==="")&&(n.crossOrigin=this.options.crossOrigin===!0?"":this.options.crossOrigin),n},_setIconStyles:function(t,e){var i=this.options,n=i[e+"Size"];typeof n=="number"&&(n=[n,n]);var o=P(n),s=P(e==="shadow"&&i.shadowAnchor||i.iconAnchor||o&&o.divideBy(2,!0));t.className="leaflet-marker-"+e+" "+(i.className||""),s&&(t.style.marginLeft=-s.x+"px",t.style.marginTop=-s.y+"px"),o&&(t.style.width=o.x+"px",t.style.height=o.y+"px")},_createImg:function(t,e){return e=e||document.createElement("img"),e.src=t,e},_getIconUrl:function(t){return y.retina&&this.options[t+"RetinaUrl"]||this.options[t+"Url"]}});function Bo(t){return new At(t)}var $t=At.extend({options:{iconUrl:"marker-icon.png",iconRetinaUrl:"marker-icon-2x.png",shadowUrl:"marker-shadow.png",iconSize:[25,41],iconAnchor:[12,41],popupAnchor:[1,-34],tooltipAnchor:[16,-28],shadowSize:[41,41]},_getIconUrl:function(t){return typeof $t.imagePath!="string"&&($t.imagePath=this._detectIconPath()),(this.options.imagePath||$t.imagePath)+At.prototype._getIconUrl.call(this,t)},_stripUrl:function(t){var e=function(i,n,o){var s=n.exec(i);return s&&s[o]};return t=e(t,/^url\((['"])?(.+)\1\)$/,2),t&&e(t,/^(.*)marker-icon\.png$/,1)},_detectIconPath:function(){var t=Z("div","leaflet-default-icon-path",document.body),e=Ut(t,"background-image")||Ut(t,"backgroundImage");if(document.body.removeChild(t),e=this._stripUrl(e),e)return e;var i=document.querySelector('link[href$="leaflet.css"]');return i?i.href.substring(0,i.href.length-11-1):""}}),rn=ct.extend({initialize:function(t){this._marker=t},addHooks:function(){var t=this._marker._icon;this._draggable||(this._draggable=new wt(t,t,!0)),this._draggable.on({dragstart:this._onDragStart,predrag:this._onPreDrag,drag:this._onDrag,dragend:this._onDragEnd},this).enable(),C(t,"leaflet-marker-draggable")},removeHooks:function(){this._draggable.off({dragstart:this._onDragStart,predrag:this._onPreDrag,drag:this._onDrag,dragend:this._onDragEnd},this).disable(),this._marker._icon&&D(this._marker._icon,"leaflet-marker-draggable")},moved:function(){return this._draggable&&this._draggable._moved},_adjustPan:function(t){var e=this._marker,i=e._map,n=this._marker.options.autoPanSpeed,o=this._marker.options.autoPanPadding,s=Mt(e._icon),r=i.getPixelBounds(),h=i.getPixelOrigin(),u=$(r.min._subtract(h).add(o),r.max._subtract(h).subtract(o));if(!u.contains(s)){var c=P((Math.max(u.max.x,s.x)-u.max.x)/(r.max.x-u.max.x)-(Math.min(u.min.x,s.x)-u.min.x)/(r.min.x-u.min.x),(Math.max(u.max.y,s.y)-u.max.y)/(r.max.y-u.max.y)-(Math.min(u.min.y,s.y)-u.min.y)/(r.min.y-u.min.y)).multiplyBy(n);i.panBy(c,{animate:!1}),this._draggable._newPos._add(c),this._draggable._startPos._add(c),H(e._icon,this._draggable._newPos),this._onDrag(t),this._panRequest=X(this._adjustPan.bind(this,t))}},_onDragStart:function(){this._oldLatLng=this._marker.getLatLng(),this._marker.closePopup&&this._marker.closePopup(),this._marker.fire("movestart").fire("dragstart")},_onPreDrag:function(t){this._marker.options.autoPan&&(et(this._panRequest),this._panRequest=X(this._adjustPan.bind(this,t)))},_onDrag:function(t){var e=this._marker,i=e._shadow,n=Mt(e._icon),o=e._map.layerPointToLatLng(n);i&&H(i,n),e._latlng=o,t.latlng=o,t.oldLatLng=this._oldLatLng,e.fire("move",t).fire("drag",t)},_onDragEnd:function(t){et(this._panRequest),delete this._oldLatLng,this._marker.fire("moveend").fire("dragend",t)}}),fe=at.extend({options:{icon:new $t,interactive:!0,keyboard:!0,title:"",alt:"Marker",zIndexOffset:0,opacity:1,riseOnHover:!1,riseOffset:250,pane:"markerPane",shadowPane:"shadowPane",bubblingMouseEvents:!1,autoPanOnFocus:!0,draggable:!1,autoPan:!1,autoPanPadding:[50,50],autoPanSpeed:10},initialize:function(t,e){A(this,e),this._latlng=E(t)},onAdd:function(t){this._zoomAnimated=this._zoomAnimated&&t.options.markerZoomAnimation,this._zoomAnimated&&t.on("zoomanim",this._animateZoom,this),this._initIcon(),this.update()},onRemove:function(t){this.dragging&&this.dragging.enabled()&&(this.options.draggable=!0,this.dragging.removeHooks()),delete this.dragging,this._zoomAnimated&&t.off("zoomanim",this._animateZoom,this),this._removeIcon(),this._removeShadow()},getEvents:function(){return{zoom:this.update,viewreset:this.update}},getLatLng:function(){return this._latlng},setLatLng:function(t){var e=this._latlng;return this._latlng=E(t),this.update(),this.fire("move",{oldLatLng:e,latlng:this._latlng})},setZIndexOffset:function(t){return this.options.zIndexOffset=t,this.update()},getIcon:function(){return this.options.icon},setIcon:function(t){return this.options.icon=t,this._map&&(this._initIcon(),this.update()),this._popup&&this.bindPopup(this._popup,this._popup.options),this},getElement:function(){return this._icon},update:function(){if(this._icon&&this._map){var t=this._map.latLngToLayerPoint(this._latlng).round();this._setPos(t)}return this},_initIcon:function(){var t=this.options,e="leaflet-zoom-"+(this._zoomAnimated?"animated":"hide"),i=t.icon.createIcon(this._icon),n=!1;i!==this._icon&&(this._icon&&this._removeIcon(),n=!0,t.title&&(i.title=t.title),i.tagName==="IMG"&&(i.alt=t.alt||"")),C(i,e),t.keyboard&&(i.tabIndex="0",i.setAttribute("role","button")),this._icon=i,t.riseOnHover&&this.on({mouseover:this._bringToFront,mouseout:this._resetZIndex}),this.options.autoPanOnFocus&&T(i,"focus",this._panOnFocus,this);var o=t.icon.createShadow(this._shadow),s=!1;o!==this._shadow&&(this._removeShadow(),s=!0),o&&(C(o,e),o.alt=""),this._shadow=o,t.opacity<1&&this._updateOpacity(),n&&this.getPane().appendChild(this._icon),this._initInteraction(),o&&s&&this.getPane(t.shadowPane).appendChild(this._shadow)},_removeIcon:function(){this.options.riseOnHover&&this.off({mouseover:this._bringToFront,mouseout:this._resetZIndex}),this.options.autoPanOnFocus&&B(this._icon,"focus",this._panOnFocus,this),R(this._icon),this.removeInteractiveTarget(this._icon),this._icon=null},_removeShadow:function(){this._shadow&&R(this._shadow),this._shadow=null},_setPos:function(t){this._icon&&H(this._icon,t),this._shadow&&H(this._shadow,t),this._zIndex=t.y+this.options.zIndexOffset,this._resetZIndex()},_updateZIndex:function(t){this._icon&&(this._icon.style.zIndex=this._zIndex+t)},_animateZoom:function(t){var e=this._map._latLngToNewLayerPoint(this._latlng,t.zoom,t.center).round();this._setPos(e)},_initInteraction:function(){if(this.options.interactive&&(C(this._icon,"leaflet-interactive"),this.addInteractiveTarget(this._icon),rn)){var t=this.options.draggable;this.dragging&&(t=this.dragging.enabled(),this.dragging.disable()),this.dragging=new rn(this),t&&this.dragging.enable()}},setOpacity:function(t){return this.options.opacity=t,this._map&&this._updateOpacity(),this},_updateOpacity:function(){var t=this.options.opacity;this._icon&&it(this._icon,t),this._shadow&&it(this._shadow,t)},_bringToFront:function(){this._updateZIndex(this.options.riseOffset)},_resetZIndex:function(){this._updateZIndex(0)},_panOnFocus:function(){var t=this._map;if(t){var e=this.options.icon.options,i=e.iconSize?P(e.iconSize):P(0,0),n=e.iconAnchor?P(e.iconAnchor):P(0,0);t.panInside(this._latlng,{paddingTopLeft:n,paddingBottomRight:i.subtract(n)})}},_getPopupAnchor:function(){return this.options.icon.options.popupAnchor},_getTooltipAnchor:function(){return this.options.icon.options.tooltipAnchor}});function No(t,e){return new fe(t,e)}var Pt=at.extend({options:{stroke:!0,color:"#3388ff",weight:3,opacity:1,lineCap:"round",lineJoin:"round",dashArray:null,dashOffset:null,fill:!1,fillColor:null,fillOpacity:.2,fillRule:"evenodd",interactive:!0,bubblingMouseEvents:!0},beforeAdd:function(t){this._renderer=t.getRenderer(this)},onAdd:function(){this._renderer._initPath(this),this._reset(),this._renderer._addPath(this)},onRemove:function(){this._renderer._removePath(this)},redraw:function(){return this._map&&this._renderer._updatePath(this),this},setStyle:function(t){return A(this,t),this._renderer&&(this._renderer._updateStyle(this),this.options.stroke&&t&&Object.prototype.hasOwnProperty.call(t,"weight")&&this._updateBounds()),this},bringToFront:function(){return this._renderer&&this._renderer._bringToFront(this),this},bringToBack:function(){return this._renderer&&this._renderer._bringToBack(this),this},getElement:function(){return this._path},_reset:function(){this._project(),this._update()},_clickTolerance:function(){return(this.options.stroke?this.options.weight/2:0)+(this._renderer.options.tolerance||0)}}),_e=Pt.extend({options:{fill:!0,radius:10},initialize:function(t,e){A(this,e),this._latlng=E(t),this._radius=this.options.radius},setLatLng:function(t){var e=this._latlng;return this._latlng=E(t),this.redraw(),this.fire("move",{oldLatLng:e,latlng:this._latlng})},getLatLng:function(){return this._latlng},setRadius:function(t){return this.options.radius=this._radius=t,this.redraw()},getRadius:function(){return this._radius},setStyle:function(t){var e=t&&t.radius||this._radius;return Pt.prototype.setStyle.call(this,t),this.setRadius(e),this},_project:function(){this._point=this._map.latLngToLayerPoint(this._latlng),this._updateBounds()},_updateBounds:function(){var t=this._radius,e=this._radiusY||t,i=this._clickTolerance(),n=[t+i,e+i];this._pxBounds=new N(this._point.subtract(n),this._point.add(n))},_update:function(){this._map&&this._updatePath()},_updatePath:function(){this._renderer._updateCircle(this)},_empty:function(){return this._radius&&!this._renderer._bounds.intersects(this._pxBounds)},_containsPoint:function(t){return t.distanceTo(this._point)<=this._radius+this._clickTolerance()}});function Ro(t,e){return new _e(t,e)}var ii=_e.extend({initialize:function(t,e,i){if(typeof e=="number"&&(e=_({},i,{radius:e})),A(this,e),this._latlng=E(t),isNaN(this.options.radius))throw new Error("Circle radius cannot be NaN");this._mRadius=this.options.radius},setRadius:function(t){return this._mRadius=t,this.redraw()},getRadius:function(){return this._mRadius},getBounds:function(){var t=[this._radius,this._radiusY||this._radius];return new J(this._map.layerPointToLatLng(this._point.subtract(t)),this._map.layerPointToLatLng(this._point.add(t)))},setStyle:Pt.prototype.setStyle,_project:function(){var t=this._latlng.lng,e=this._latlng.lat,i=this._map,n=i.options.crs;if(n.distance===xt.distance){var o=Math.PI/180,s=this._mRadius/xt.R/o,r=i.project([e+s,t]),h=i.project([e-s,t]),u=r.add(h).divideBy(2),c=i.unproject(u).lat,p=Math.acos((Math.cos(s*o)-Math.sin(e*o)*Math.sin(c*o))/(Math.cos(e*o)*Math.cos(c*o)))/o;(isNaN(p)||p===0)&&(p=s/Math.cos(Math.PI/180*e)),this._point=u.subtract(i.getPixelOrigin()),this._radius=isNaN(p)?0:u.x-i.project([c,t-p]).x,this._radiusY=u.y-r.y}else{var x=n.unproject(n.project(this._latlng).subtract([this._mRadius,0]));this._point=i.latLngToLayerPoint(this._latlng),this._radius=this._point.x-i.latLngToLayerPoint(x).x}this._updateBounds()}});function Do(t,e,i){return new ii(t,e,i)}var gt=Pt.extend({options:{smoothFactor:1,noClip:!1},initialize:function(t,e){A(this,e),this._setLatLngs(t)},getLatLngs:function(){return this._latlngs},setLatLngs:function(t){return this._setLatLngs(t),this.redraw()},isEmpty:function(){return!this._latlngs.length},closestLayerPoint:function(t){for(var e=1/0,i=null,n=Xt,o,s,r=0,h=this._parts.length;r<h;r++)for(var u=this._parts[r],c=1,p=u.length;c<p;c++){o=u[c-1],s=u[c];var x=n(t,o,s,!0);x<e&&(e=x,i=n(t,o,s))}return i&&(i.distance=Math.sqrt(e)),i},getCenter:function(){if(!this._map)throw new Error("Must add layer to map before using getCenter()");return on(this._defaultShape(),this._map.options.crs)},getBounds:function(){return this._bounds},addLatLng:function(t,e){return e=e||this._defaultShape(),t=E(t),e.push(t),this._bounds.extend(t),this.redraw()},_setLatLngs:function(t){this._bounds=new J,this._latlngs=this._convertLatLngs(t)},_defaultShape:function(){return nt(this._latlngs)?this._latlngs:this._latlngs[0]},_convertLatLngs:function(t){for(var e=[],i=nt(t),n=0,o=t.length;n<o;n++)i?(e[n]=E(t[n]),this._bounds.extend(e[n])):e[n]=this._convertLatLngs(t[n]);return e},_project:function(){var t=new N;this._rings=[],this._projectLatlngs(this._latlngs,this._rings,t),this._bounds.isValid()&&t.isValid()&&(this._rawPxBounds=t,this._updateBounds())},_updateBounds:function(){var t=this._clickTolerance(),e=new b(t,t);this._rawPxBounds&&(this._pxBounds=new N([this._rawPxBounds.min.subtract(e),this._rawPxBounds.max.add(e)]))},_projectLatlngs:function(t,e,i){var n=t[0]instanceof I,o=t.length,s,r;if(n){for(r=[],s=0;s<o;s++)r[s]=this._map.latLngToLayerPoint(t[s]),i.extend(r[s]);e.push(r)}else for(s=0;s<o;s++)this._projectLatlngs(t[s],e,i)},_clipPoints:function(){var t=this._renderer._bounds;if(this._parts=[],!(!this._pxBounds||!this._pxBounds.intersects(t))){if(this.options.noClip){this._parts=this._rings;return}var e=this._parts,i,n,o,s,r,h,u;for(i=0,o=0,s=this._rings.length;i<s;i++)for(u=this._rings[i],n=0,r=u.length;n<r-1;n++)h=en(u[n],u[n+1],t,n,!0),h&&(e[o]=e[o]||[],e[o].push(h[0]),(h[1]!==u[n+1]||n===r-2)&&(e[o].push(h[1]),o++))}},_simplifyPoints:function(){for(var t=this._parts,e=this.options.smoothFactor,i=0,n=t.length;i<n;i++)t[i]=Ji(t[i],e)},_update:function(){this._map&&(this._clipPoints(),this._simplifyPoints(),this._updatePath())},_updatePath:function(){this._renderer._updatePoly(this)},_containsPoint:function(t,e){var i,n,o,s,r,h,u=this._clickTolerance();if(!this._pxBounds||!this._pxBounds.contains(t))return!1;for(i=0,s=this._parts.length;i<s;i++)for(h=this._parts[i],n=0,r=h.length,o=r-1;n<r;o=n++)if(!(!e&&n===0)&&Qi(t,h[o],h[n])<=u)return!0;return!1}});function jo(t,e){return new gt(t,e)}gt._flat=nn;var Bt=gt.extend({options:{fill:!0},isEmpty:function(){return!this._latlngs.length||!this._latlngs[0].length},getCenter:function(){if(!this._map)throw new Error("Must add layer to map before using getCenter()");return $i(this._defaultShape(),this._map.options.crs)},_convertLatLngs:function(t){var e=gt.prototype._convertLatLngs.call(this,t),i=e.length;return i>=2&&e[0]instanceof I&&e[0].equals(e[i-1])&&e.pop(),e},_setLatLngs:function(t){gt.prototype._setLatLngs.call(this,t),nt(this._latlngs)&&(this._latlngs=[this._latlngs])},_defaultShape:function(){return nt(this._latlngs[0])?this._latlngs[0]:this._latlngs[0][0]},_clipPoints:function(){var t=this._renderer._bounds,e=this.options.weight,i=new b(e,e);if(t=new N(t.min.subtract(i),t.max.add(i)),this._parts=[],!(!this._pxBounds||!this._pxBounds.intersects(t))){if(this.options.noClip){this._parts=this._rings;return}for(var n=0,o=this._rings.length,s;n<o;n++)s=Xi(this._rings[n],t,!0),s.length&&this._parts.push(s)}},_updatePath:function(){this._renderer._updatePoly(this,!0)},_containsPoint:function(t){var e=!1,i,n,o,s,r,h,u,c;if(!this._pxBounds||!this._pxBounds.contains(t))return!1;for(s=0,u=this._parts.length;s<u;s++)for(i=this._parts[s],r=0,c=i.length,h=c-1;r<c;h=r++)n=i[r],o=i[h],n.y>t.y!=o.y>t.y&&t.x<(o.x-n.x)*(t.y-n.y)/(o.y-n.y)+n.x&&(e=!e);return e||gt.prototype._containsPoint.call(this,t,!0)}});function Fo(t,e){return new Bt(t,e)}var vt=pt.extend({initialize:function(t,e){A(this,e),this._layers={},t&&this.addData(t)},addData:function(t){var e=st(t)?t:t.features,i,n,o;if(e){for(i=0,n=e.length;i<n;i++)o=e[i],(o.geometries||o.geometry||o.features||o.coordinates)&&this.addData(o);return this}var s=this.options;if(s.filter&&!s.filter(t))return this;var r=me(t,s);return r?(r.feature=ve(t),r.defaultOptions=r.options,this.resetStyle(r),s.onEachFeature&&s.onEachFeature(t,r),this.addLayer(r)):this},resetStyle:function(t){return t===void 0?this.eachLayer(this.resetStyle,this):(t.options=_({},t.defaultOptions),this._setLayerStyle(t,this.options.style),this)},setStyle:function(t){return this.eachLayer(function(e){this._setLayerStyle(e,t)},this)},_setLayerStyle:function(t,e){t.setStyle&&(typeof e=="function"&&(e=e(t.feature)),t.setStyle(e))}});function me(t,e){var i=t.type==="Feature"?t.geometry:t,n=i?i.coordinates:null,o=[],s=e&&e.pointToLayer,r=e&&e.coordsToLatLng||ni,h,u,c,p;if(!n&&!i)return null;switch(i.type){case"Point":return h=r(n),an(s,t,h,e);case"MultiPoint":for(c=0,p=n.length;c<p;c++)h=r(n[c]),o.push(an(s,t,h,e));return new pt(o);case"LineString":case"MultiLineString":return u=pe(n,i.type==="LineString"?0:1,r),new gt(u,e);case"Polygon":case"MultiPolygon":return u=pe(n,i.type==="Polygon"?1:2,r),new Bt(u,e);case"GeometryCollection":for(c=0,p=i.geometries.length;c<p;c++){var x=me({geometry:i.geometries[c],type:"Feature",properties:t.properties},e);x&&o.push(x)}return new pt(o);case"FeatureCollection":for(c=0,p=i.features.length;c<p;c++){var S=me(i.features[c],e);S&&o.push(S)}return new pt(o);default:throw new Error("Invalid GeoJSON object.")}}function an(t,e,i,n){return t?t(e,i):new fe(i,n&&n.markersInheritOptions&&n)}function ni(t){return new I(t[1],t[0],t[2])}function pe(t,e,i){for(var n=[],o=0,s=t.length,r;o<s;o++)r=e?pe(t[o],e-1,i):(i||ni)(t[o]),n.push(r);return n}function oi(t,e){return t=E(t),t.alt!==void 0?[j(t.lng,e),j(t.lat,e),j(t.alt,e)]:[j(t.lng,e),j(t.lat,e)]}function ge(t,e,i,n){for(var o=[],s=0,r=t.length;s<r;s++)o.push(e?ge(t[s],nt(t[s])?0:e-1,i,n):oi(t[s],n));return!e&&i&&o.length>0&&o.push(o[0].slice()),o}function Nt(t,e){return t.feature?_({},t.feature,{geometry:e}):ve(e)}function ve(t){return t.type==="Feature"||t.type==="FeatureCollection"?t:{type:"Feature",properties:{},geometry:t}}var si={toGeoJSON:function(t){return Nt(this,{type:"Point",coordinates:oi(this.getLatLng(),t)})}};fe.include(si),ii.include(si),_e.include(si),gt.include({toGeoJSON:function(t){var e=!nt(this._latlngs),i=ge(this._latlngs,e?1:0,!1,t);return Nt(this,{type:(e?"Multi":"")+"LineString",coordinates:i})}}),Bt.include({toGeoJSON:function(t){var e=!nt(this._latlngs),i=e&&!nt(this._latlngs[0]),n=ge(this._latlngs,i?2:e?1:0,!0,t);return e||(n=[n]),Nt(this,{type:(i?"Multi":"")+"Polygon",coordinates:n})}}),It.include({toMultiPoint:function(t){var e=[];return this.eachLayer(function(i){e.push(i.toGeoJSON(t).geometry.coordinates)}),Nt(this,{type:"MultiPoint",coordinates:e})},toGeoJSON:function(t){var e=this.feature&&this.feature.geometry&&this.feature.geometry.type;if(e==="MultiPoint")return this.toMultiPoint(t);var i=e==="GeometryCollection",n=[];return this.eachLayer(function(o){if(o.toGeoJSON){var s=o.toGeoJSON(t);if(i)n.push(s.geometry);else{var r=ve(s);r.type==="FeatureCollection"?n.push.apply(n,r.features):n.push(r)}}}),i?Nt(this,{geometries:n,type:"GeometryCollection"}):{type:"FeatureCollection",features:n}}});function hn(t,e){return new vt(t,e)}var Ho=hn,ye=at.extend({options:{opacity:1,alt:"",interactive:!1,crossOrigin:!1,errorOverlayUrl:"",zIndex:1,className:""},initialize:function(t,e,i){this._url=t,this._bounds=F(e),A(this,i)},onAdd:function(){this._image||(this._initImage(),this.options.opacity<1&&this._updateOpacity()),this.options.interactive&&(C(this._image,"leaflet-interactive"),this.addInteractiveTarget(this._image)),this.getPane().appendChild(this._image),this._reset()},onRemove:function(){R(this._image),this.options.interactive&&this.removeInteractiveTarget(this._image)},setOpacity:function(t){return this.options.opacity=t,this._image&&this._updateOpacity(),this},setStyle:function(t){return t.opacity&&this.setOpacity(t.opacity),this},bringToFront:function(){return this._map&&Ot(this._image),this},bringToBack:function(){return this._map&&Zt(this._image),this},setUrl:function(t){return this._url=t,this._image&&(this._image.src=t),this},setBounds:function(t){return this._bounds=F(t),this._map&&this._reset(),this},getEvents:function(){var t={zoom:this._reset,viewreset:this._reset};return this._zoomAnimated&&(t.zoomanim=this._animateZoom),t},setZIndex:function(t){return this.options.zIndex=t,this._updateZIndex(),this},getBounds:function(){return this._bounds},getElement:function(){return this._image},_initImage:function(){var t=this._url.tagName==="IMG",e=this._image=t?this._url:Z("img");if(C(e,"leaflet-image-layer"),this._zoomAnimated&&C(e,"leaflet-zoom-animated"),this.options.className&&C(e,this.options.className),e.onselectstart=g,e.onmousemove=g,e.onload=v(this.fire,this,"load"),e.onerror=v(this._overlayOnError,this,"error"),(this.options.crossOrigin||this.options.crossOrigin==="")&&(e.crossOrigin=this.options.crossOrigin===!0?"":this.options.crossOrigin),this.options.zIndex&&this._updateZIndex(),t){this._url=e.src;return}e.src=this._url,e.alt=this.options.alt},_animateZoom:function(t){var e=this._map.getZoomScale(t.zoom),i=this._map._latLngBoundsToNewLayerBounds(this._bounds,t.zoom,t.center).min;Ct(this._image,i,e)},_reset:function(){var t=this._image,e=new N(this._map.latLngToLayerPoint(this._bounds.getNorthWest()),this._map.latLngToLayerPoint(this._bounds.getSouthEast())),i=e.getSize();H(t,e.min),t.style.width=i.x+"px",t.style.height=i.y+"px"},_updateOpacity:function(){it(this._image,this.options.opacity)},_updateZIndex:function(){this._image&&this.options.zIndex!==void 0&&this.options.zIndex!==null&&(this._image.style.zIndex=this.options.zIndex)},_overlayOnError:function(){this.fire("error");var t=this.options.errorOverlayUrl;t&&this._url!==t&&(this._url=t,this._image.src=t)},getCenter:function(){return this._bounds.getCenter()}}),Wo=function(t,e,i){return new ye(t,e,i)},un=ye.extend({options:{autoplay:!0,loop:!0,keepAspectRatio:!0,muted:!1,playsInline:!0},_initImage:function(){var t=this._url.tagName==="VIDEO",e=this._image=t?this._url:Z("video");if(C(e,"leaflet-image-layer"),this._zoomAnimated&&C(e,"leaflet-zoom-animated"),this.options.className&&C(e,this.options.className),e.onselectstart=g,e.onmousemove=g,e.onloadeddata=v(this.fire,this,"load"),t){for(var i=e.getElementsByTagName("source"),n=[],o=0;o<i.length;o++)n.push(i[o].src);this._url=i.length>0?n:[e.src];return}st(this._url)||(this._url=[this._url]),!this.options.keepAspectRatio&&Object.prototype.hasOwnProperty.call(e.style,"objectFit")&&(e.style.objectFit="fill"),e.autoplay=!!this.options.autoplay,e.loop=!!this.options.loop,e.muted=!!this.options.muted,e.playsInline=!!this.options.playsInline;for(var s=0;s<this._url.length;s++){var r=Z("source");r.src=this._url[s],e.appendChild(r)}}});function Uo(t,e,i){return new un(t,e,i)}var ln=ye.extend({_initImage:function(){var t=this._image=this._url;C(t,"leaflet-image-layer"),this._zoomAnimated&&C(t,"leaflet-zoom-animated"),this.options.className&&C(t,this.options.className),t.onselectstart=g,t.onmousemove=g}});function Vo(t,e,i){return new ln(t,e,i)}var dt=at.extend({options:{interactive:!1,offset:[0,0],className:"",pane:void 0,content:""},initialize:function(t,e){t&&(t instanceof I||st(t))?(this._latlng=E(t),A(this,e)):(A(this,t),this._source=e),this.options.content&&(this._content=this.options.content)},openOn:function(t){return t=arguments.length?t:this._source._map,t.hasLayer(this)||t.addLayer(this),this},close:function(){return this._map&&this._map.removeLayer(this),this},toggle:function(t){return this._map?this.close():(arguments.length?this._source=t:t=this._source,this._prepareOpen(),this.openOn(t._map)),this},onAdd:function(t){this._zoomAnimated=t._zoomAnimated,this._container||this._initLayout(),t._fadeAnimated&&it(this._container,0),clearTimeout(this._removeTimeout),this.getPane().appendChild(this._container),this.update(),t._fadeAnimated&&it(this._container,1),this.bringToFront(),this.options.interactive&&(C(this._container,"leaflet-interactive"),this.addInteractiveTarget(this._container))},onRemove:function(t){t._fadeAnimated?(it(this._container,0),this._removeTimeout=setTimeout(v(R,void 0,this._container),200)):R(this._container),this.options.interactive&&(D(this._container,"leaflet-interactive"),this.removeInteractiveTarget(this._container))},getLatLng:function(){return this._latlng},setLatLng:function(t){return this._latlng=E(t),this._map&&(this._updatePosition(),this._adjustPan()),this},getContent:function(){return this._content},setContent:function(t){return this._content=t,this.update(),this},getElement:function(){return this._container},update:function(){this._map&&(this._container.style.visibility="hidden",this._updateContent(),this._updateLayout(),this._updatePosition(),this._container.style.visibility="",this._adjustPan())},getEvents:function(){var t={zoom:this._updatePosition,viewreset:this._updatePosition};return this._zoomAnimated&&(t.zoomanim=this._animateZoom),t},isOpen:function(){return!!this._map&&this._map.hasLayer(this)},bringToFront:function(){return this._map&&Ot(this._container),this},bringToBack:function(){return this._map&&Zt(this._container),this},_prepareOpen:function(t){var e=this._source;if(!e._map)return!1;if(e instanceof pt){e=null;var i=this._source._layers;for(var n in i)if(i[n]._map){e=i[n];break}if(!e)return!1;this._source=e}if(!t)if(e.getCenter)t=e.getCenter();else if(e.getLatLng)t=e.getLatLng();else if(e.getBounds)t=e.getBounds().getCenter();else throw new Error("Unable to get source layer LatLng.");return this.setLatLng(t),this._map&&this.update(),!0},_updateContent:function(){if(this._content){var t=this._contentNode,e=typeof this._content=="function"?this._content(this._source||this):this._content;if(typeof e=="string")t.innerHTML=e;else{for(;t.hasChildNodes();)t.removeChild(t.firstChild);t.appendChild(e)}this.fire("contentupdate")}},_updatePosition:function(){if(this._map){var t=this._map.latLngToLayerPoint(this._latlng),e=P(this.options.offset),i=this._getAnchor();this._zoomAnimated?H(this._container,t.add(i)):e=e.add(t).add(i);var n=this._containerBottom=-e.y,o=this._containerLeft=-Math.round(this._containerWidth/2)+e.x;this._container.style.bottom=n+"px",this._container.style.left=o+"px"}},_getAnchor:function(){return[0,0]}});k.include({_initOverlay:function(t,e,i,n){var o=e;return o instanceof t||(o=new t(n).setContent(e)),i&&o.setLatLng(i),o}}),at.include({_initOverlay:function(t,e,i,n){var o=i;return o instanceof t?(A(o,n),o._source=this):(o=e&&!n?e:new t(n,this),o.setContent(i)),o}});var xe=dt.extend({options:{pane:"popupPane",offset:[0,7],maxWidth:300,minWidth:50,maxHeight:null,autoPan:!0,autoPanPaddingTopLeft:null,autoPanPaddingBottomRight:null,autoPanPadding:[5,5],keepInView:!1,closeButton:!0,autoClose:!0,closeOnEscapeKey:!0,className:""},openOn:function(t){return t=arguments.length?t:this._source._map,!t.hasLayer(this)&&t._popup&&t._popup.options.autoClose&&t.removeLayer(t._popup),t._popup=this,dt.prototype.openOn.call(this,t)},onAdd:function(t){dt.prototype.onAdd.call(this,t),t.fire("popupopen",{popup:this}),this._source&&(this._source.fire("popupopen",{popup:this},!0),this._source instanceof Pt||this._source.on("preclick",St))},onRemove:function(t){dt.prototype.onRemove.call(this,t),t.fire("popupclose",{popup:this}),this._source&&(this._source.fire("popupclose",{popup:this},!0),this._source instanceof Pt||this._source.off("preclick",St))},getEvents:function(){var t=dt.prototype.getEvents.call(this);return(this.options.closeOnClick!==void 0?this.options.closeOnClick:this._map.options.closePopupOnClick)&&(t.preclick=this.close),this.options.keepInView&&(t.moveend=this._adjustPan),t},_initLayout:function(){var t="leaflet-popup",e=this._container=Z("div",t+" "+(this.options.className||"")+" leaflet-zoom-animated"),i=this._wrapper=Z("div",t+"-content-wrapper",e);if(this._contentNode=Z("div",t+"-content",i),Kt(e),Ke(this._contentNode),T(e,"contextmenu",St),this._tipContainer=Z("div",t+"-tip-container",e),this._tip=Z("div",t+"-tip",this._tipContainer),this.options.closeButton){var n=this._closeButton=Z("a",t+"-close-button",e);n.setAttribute("role","button"),n.setAttribute("aria-label","Close popup"),n.href="#close",n.innerHTML='<span aria-hidden="true">&#215;</span>',T(n,"click",function(o){K(o),this.close()},this)}},_updateLayout:function(){var t=this._contentNode,e=t.style;e.width="",e.whiteSpace="nowrap";var i=t.offsetWidth;i=Math.min(i,this.options.maxWidth),i=Math.max(i,this.options.minWidth),e.width=i+1+"px",e.whiteSpace="",e.height="";var n=t.offsetHeight,o=this.options.maxHeight,s="leaflet-popup-scrolled";o&&n>o?(e.height=o+"px",C(t,s)):D(t,s),this._containerWidth=this._container.offsetWidth},_animateZoom:function(t){var e=this._map._latLngToNewLayerPoint(this._latlng,t.zoom,t.center),i=this._getAnchor();H(this._container,e.add(i))},_adjustPan:function(){if(this.options.autoPan){if(this._map._panAnim&&this._map._panAnim.stop(),this._autopanning){this._autopanning=!1;return}var t=this._map,e=parseInt(Ut(this._container,"marginBottom"),10)||0,i=this._container.offsetHeight+e,n=this._containerWidth,o=new b(this._containerLeft,-i-this._containerBottom);o._add(Mt(this._container));var s=t.layerPointToContainerPoint(o),r=P(this.options.autoPanPadding),h=P(this.options.autoPanPaddingTopLeft||r),u=P(this.options.autoPanPaddingBottomRight||r),c=t.getSize(),p=0,x=0;s.x+n+u.x>c.x&&(p=s.x+n-c.x+u.x),s.x-p-h.x<0&&(p=s.x-h.x),s.y+i+u.y>c.y&&(x=s.y+i-c.y+u.y),s.y-x-h.y<0&&(x=s.y-h.y),(p||x)&&(this.options.keepInView&&(this._autopanning=!0),t.fire("autopanstart").panBy([p,x]))}},_getAnchor:function(){return P(this._source&&this._source._getPopupAnchor?this._source._getPopupAnchor():[0,0])}}),Go=function(t,e){return new xe(t,e)};k.mergeOptions({closePopupOnClick:!0}),k.include({openPopup:function(t,e,i){return this._initOverlay(xe,t,e,i).openOn(this),this},closePopup:function(t){return t=arguments.length?t:this._popup,t&&t.close(),this}}),at.include({bindPopup:function(t,e){return this._popup=this._initOverlay(xe,this._popup,t,e),this._popupHandlersAdded||(this.on({click:this._openPopup,keypress:this._onKeyPress,remove:this.closePopup,move:this._movePopup}),this._popupHandlersAdded=!0),this},unbindPopup:function(){return this._popup&&(this.off({click:this._openPopup,keypress:this._onKeyPress,remove:this.closePopup,move:this._movePopup}),this._popupHandlersAdded=!1,this._popup=null),this},openPopup:function(t){return this._popup&&(this instanceof pt||(this._popup._source=this),this._popup._prepareOpen(t||this._latlng)&&this._popup.openOn(this._map)),this},closePopup:function(){return this._popup&&this._popup.close(),this},togglePopup:function(){return this._popup&&this._popup.toggle(this),this},isPopupOpen:function(){return this._popup?this._popup.isOpen():!1},setPopupContent:function(t){return this._popup&&this._popup.setContent(t),this},getPopup:function(){return this._popup},_openPopup:function(t){if(!(!this._popup||!this._map)){zt(t);var e=t.layer||t.target;if(this._popup._source===e&&!(e instanceof Pt)){this._map.hasLayer(this._popup)?this.closePopup():this.openPopup(t.latlng);return}this._popup._source=e,this.openPopup(t.latlng)}},_movePopup:function(t){this._popup.setLatLng(t.latlng)},_onKeyPress:function(t){t.originalEvent.keyCode===13&&this._openPopup(t)}});var we=dt.extend({options:{pane:"tooltipPane",offset:[0,0],direction:"auto",permanent:!1,sticky:!1,opacity:.9},onAdd:function(t){dt.prototype.onAdd.call(this,t),this.setOpacity(this.options.opacity),t.fire("tooltipopen",{tooltip:this}),this._source&&(this.addEventParent(this._source),this._source.fire("tooltipopen",{tooltip:this},!0))},onRemove:function(t){dt.prototype.onRemove.call(this,t),t.fire("tooltipclose",{tooltip:this}),this._source&&(this.removeEventParent(this._source),this._source.fire("tooltipclose",{tooltip:this},!0))},getEvents:function(){var t=dt.prototype.getEvents.call(this);return this.options.permanent||(t.preclick=this.close),t},_initLayout:function(){var t="leaflet-tooltip",e=t+" "+(this.options.className||"")+" leaflet-zoom-"+(this._zoomAnimated?"animated":"hide");this._contentNode=this._container=Z("div",e),this._container.setAttribute("role","tooltip"),this._container.setAttribute("id","leaflet-tooltip-"+w(this))},_updateLayout:function(){},_adjustPan:function(){},_setPosition:function(t){var e,i,n=this._map,o=this._container,s=n.latLngToContainerPoint(n.getCenter()),r=n.layerPointToContainerPoint(t),h=this.options.direction,u=o.offsetWidth,c=o.offsetHeight,p=P(this.options.offset),x=this._getAnchor();h==="top"?(e=u/2,i=c):h==="bottom"?(e=u/2,i=0):h==="center"?(e=u/2,i=c/2):h==="right"?(e=0,i=c/2):h==="left"?(e=u,i=c/2):r.x<s.x?(h="right",e=0,i=c/2):(h="left",e=u+(p.x+x.x)*2,i=c/2),t=t.subtract(P(e,i,!0)).add(p).add(x),D(o,"leaflet-tooltip-right"),D(o,"leaflet-tooltip-left"),D(o,"leaflet-tooltip-top"),D(o,"leaflet-tooltip-bottom"),C(o,"leaflet-tooltip-"+h),H(o,t)},_updatePosition:function(){var t=this._map.latLngToLayerPoint(this._latlng);this._setPosition(t)},setOpacity:function(t){this.options.opacity=t,this._container&&it(this._container,t)},_animateZoom:function(t){var e=this._map._latLngToNewLayerPoint(this._latlng,t.zoom,t.center);this._setPosition(e)},_getAnchor:function(){return P(this._source&&this._source._getTooltipAnchor&&!this.options.sticky?this._source._getTooltipAnchor():[0,0])}}),qo=function(t,e){return new we(t,e)};k.include({openTooltip:function(t,e,i){return this._initOverlay(we,t,e,i).openOn(this),this},closeTooltip:function(t){return t.close(),this}}),at.include({bindTooltip:function(t,e){return this._tooltip&&this.isTooltipOpen()&&this.unbindTooltip(),this._tooltip=this._initOverlay(we,this._tooltip,t,e),this._initTooltipInteractions(),this._tooltip.options.permanent&&this._map&&this._map.hasLayer(this)&&this.openTooltip(),this},unbindTooltip:function(){return this._tooltip&&(this._initTooltipInteractions(!0),this.closeTooltip(),this._tooltip=null),this},_initTooltipInteractions:function(t){if(!(!t&&this._tooltipHandlersAdded)){var e=t?"off":"on",i={remove:this.closeTooltip,move:this._moveTooltip};this._tooltip.options.permanent?i.add=this._openTooltip:(i.mouseover=this._openTooltip,i.mouseout=this.closeTooltip,i.click=this._openTooltip,this._map?this._addFocusListeners():i.add=this._addFocusListeners),this._tooltip.options.sticky&&(i.mousemove=this._moveTooltip),this[e](i),this._tooltipHandlersAdded=!t}},openTooltip:function(t){return this._tooltip&&(this instanceof pt||(this._tooltip._source=this),this._tooltip._prepareOpen(t)&&(this._tooltip.openOn(this._map),this.getElement?this._setAriaDescribedByOnLayer(this):this.eachLayer&&this.eachLayer(this._setAriaDescribedByOnLayer,this))),this},closeTooltip:function(){if(this._tooltip)return this._tooltip.close()},toggleTooltip:function(){return this._tooltip&&this._tooltip.toggle(this),this},isTooltipOpen:function(){return this._tooltip.isOpen()},setTooltipContent:function(t){return this._tooltip&&this._tooltip.setContent(t),this},getTooltip:function(){return this._tooltip},_addFocusListeners:function(){this.getElement?this._addFocusListenersOnLayer(this):this.eachLayer&&this.eachLayer(this._addFocusListenersOnLayer,this)},_addFocusListenersOnLayer:function(t){var e=typeof t.getElement=="function"&&t.getElement();e&&(T(e,"focus",function(){this._tooltip._source=t,this.openTooltip()},this),T(e,"blur",this.closeTooltip,this))},_setAriaDescribedByOnLayer:function(t){var e=typeof t.getElement=="function"&&t.getElement();e&&e.setAttribute("aria-describedby",this._tooltip._container.id)},_openTooltip:function(t){if(!(!this._tooltip||!this._map)){if(this._map.dragging&&this._map.dragging.moving()&&!this._openOnceFlag){this._openOnceFlag=!0;var e=this;this._map.once("moveend",function(){e._openOnceFlag=!1,e._openTooltip(t)});return}this._tooltip._source=t.layer||t.target,this.openTooltip(this._tooltip.options.sticky?t.latlng:void 0)}},_moveTooltip:function(t){var e=t.latlng,i,n;this._tooltip.options.sticky&&t.originalEvent&&(i=this._map.mouseEventToContainerPoint(t.originalEvent),n=this._map.containerPointToLayerPoint(i),e=this._map.layerPointToLatLng(n)),this._tooltip.setLatLng(e)}});var cn=At.extend({options:{iconSize:[12,12],html:!1,bgPos:null,className:"leaflet-div-icon"},createIcon:function(t){var e=t&&t.tagName==="DIV"?t:document.createElement("div"),i=this.options;if(i.html instanceof Element?(ae(e),e.appendChild(i.html)):e.innerHTML=i.html!==!1?i.html:"",i.bgPos){var n=P(i.bgPos);e.style.backgroundPosition=-n.x+"px "+-n.y+"px"}return this._setIconStyles(e,"icon"),e},createShadow:function(){return null}});function Ko(t){return new cn(t)}At.Default=$t;var Jt=at.extend({options:{tileSize:256,opacity:1,updateWhenIdle:y.mobile,updateWhenZooming:!0,updateInterval:200,zIndex:1,bounds:null,minZoom:0,maxZoom:void 0,maxNativeZoom:void 0,minNativeZoom:void 0,noWrap:!1,pane:"tilePane",className:"",keepBuffer:2},initialize:function(t){A(this,t)},onAdd:function(){this._initContainer(),this._levels={},this._tiles={},this._resetView()},beforeAdd:function(t){t._addZoomLimit(this)},onRemove:function(t){this._removeAllTiles(),R(this._container),t._removeZoomLimit(this),this._container=null,this._tileZoom=void 0},bringToFront:function(){return this._map&&(Ot(this._container),this._setAutoZIndex(Math.max)),this},bringToBack:function(){return this._map&&(Zt(this._container),this._setAutoZIndex(Math.min)),this},getContainer:function(){return this._container},setOpacity:function(t){return this.options.opacity=t,this._updateOpacity(),this},setZIndex:function(t){return this.options.zIndex=t,this._updateZIndex(),this},isLoading:function(){return this._loading},redraw:function(){if(this._map){this._removeAllTiles();var t=this._clampZoom(this._map.getZoom());t!==this._tileZoom&&(this._tileZoom=t,this._updateLevels()),this._update()}return this},getEvents:function(){var t={viewprereset:this._invalidateAll,viewreset:this._resetView,zoom:this._resetView,moveend:this._onMoveEnd};return this.options.updateWhenIdle||(this._onMove||(this._onMove=U(this._onMoveEnd,this.options.updateInterval,this)),t.move=this._onMove),this._zoomAnimated&&(t.zoomanim=this._animateZoom),t},createTile:function(){return document.createElement("div")},getTileSize:function(){var t=this.options.tileSize;return t instanceof b?t:new b(t,t)},_updateZIndex:function(){this._container&&this.options.zIndex!==void 0&&this.options.zIndex!==null&&(this._container.style.zIndex=this.options.zIndex)},_setAutoZIndex:function(t){for(var e=this.getPane().children,i=-t(-1/0,1/0),n=0,o=e.length,s;n<o;n++)s=e[n].style.zIndex,e[n]!==this._container&&s&&(i=t(i,+s));isFinite(i)&&(this.options.zIndex=i+t(-1,1),this._updateZIndex())},_updateOpacity:function(){if(this._map&&!y.ielt9){it(this._container,this.options.opacity);var t=+new Date,e=!1,i=!1;for(var n in this._tiles){var o=this._tiles[n];if(!(!o.current||!o.loaded)){var s=Math.min(1,(t-o.loaded)/200);it(o.el,s),s<1?e=!0:(o.active?i=!0:this._onOpaqueTile(o),o.active=!0)}}i&&!this._noPrune&&this._pruneTiles(),e&&(et(this._fadeFrame),this._fadeFrame=X(this._updateOpacity,this))}},_onOpaqueTile:g,_initContainer:function(){this._container||(this._container=Z("div","leaflet-layer "+(this.options.className||"")),this._updateZIndex(),this.options.opacity<1&&this._updateOpacity(),this.getPane().appendChild(this._container))},_updateLevels:function(){var t=this._tileZoom,e=this.options.maxZoom;if(t!==void 0){for(var i in this._levels)i=Number(i),this._levels[i].el.children.length||i===t?(this._levels[i].el.style.zIndex=e-Math.abs(t-i),this._onUpdateLevel(i)):(R(this._levels[i].el),this._removeTilesAtZoom(i),this._onRemoveLevel(i),delete this._levels[i]);var n=this._levels[t],o=this._map;return n||(n=this._levels[t]={},n.el=Z("div","leaflet-tile-container leaflet-zoom-animated",this._container),n.el.style.zIndex=e,n.origin=o.project(o.unproject(o.getPixelOrigin()),t).round(),n.zoom=t,this._setZoomTransform(n,o.getCenter(),o.getZoom()),g(n.el.offsetWidth),this._onCreateLevel(n)),this._level=n,n}},_onUpdateLevel:g,_onRemoveLevel:g,_onCreateLevel:g,_pruneTiles:function(){if(this._map){var t,e,i=this._map.getZoom();if(i>this.options.maxZoom||i<this.options.minZoom){this._removeAllTiles();return}for(t in this._tiles)e=this._tiles[t],e.retain=e.current;for(t in this._tiles)if(e=this._tiles[t],e.current&&!e.active){var n=e.coords;this._retainParent(n.x,n.y,n.z,n.z-5)||this._retainChildren(n.x,n.y,n.z,n.z+2)}for(t in this._tiles)this._tiles[t].retain||this._removeTile(t)}},_removeTilesAtZoom:function(t){for(var e in this._tiles)this._tiles[e].coords.z===t&&this._removeTile(e)},_removeAllTiles:function(){for(var t in this._tiles)this._removeTile(t)},_invalidateAll:function(){for(var t in this._levels)R(this._levels[t].el),this._onRemoveLevel(Number(t)),delete this._levels[t];this._removeAllTiles(),this._tileZoom=void 0},_retainParent:function(t,e,i,n){var o=Math.floor(t/2),s=Math.floor(e/2),r=i-1,h=new b(+o,+s);h.z=+r;var u=this._tileCoordsToKey(h),c=this._tiles[u];return c&&c.active?(c.retain=!0,!0):(c&&c.loaded&&(c.retain=!0),r>n?this._retainParent(o,s,r,n):!1)},_retainChildren:function(t,e,i,n){for(var o=2*t;o<2*t+2;o++)for(var s=2*e;s<2*e+2;s++){var r=new b(o,s);r.z=i+1;var h=this._tileCoordsToKey(r),u=this._tiles[h];if(u&&u.active){u.retain=!0;continue}else u&&u.loaded&&(u.retain=!0);i+1<n&&this._retainChildren(o,s,i+1,n)}},_resetView:function(t){var e=t&&(t.pinch||t.flyTo);this._setView(this._map.getCenter(),this._map.getZoom(),e,e)},_animateZoom:function(t){this._setView(t.center,t.zoom,!0,t.noUpdate)},_clampZoom:function(t){var e=this.options;return e.minNativeZoom!==void 0&&t<e.minNativeZoom?e.minNativeZoom:e.maxNativeZoom!==void 0&&e.maxNativeZoom<t?e.maxNativeZoom:t},_setView:function(t,e,i,n){var o=Math.round(e);this.options.maxZoom!==void 0&&o>this.options.maxZoom||this.options.minZoom!==void 0&&o<this.options.minZoom?o=void 0:o=this._clampZoom(o);var s=this.options.updateWhenZooming&&o!==this._tileZoom;(!n||s)&&(this._tileZoom=o,this._abortLoading&&this._abortLoading(),this._updateLevels(),this._resetGrid(),o!==void 0&&this._update(t),i||this._pruneTiles(),this._noPrune=!!i),this._setZoomTransforms(t,e)},_setZoomTransforms:function(t,e){for(var i in this._levels)this._setZoomTransform(this._levels[i],t,e)},_setZoomTransform:function(t,e,i){var n=this._map.getZoomScale(i,t.zoom),o=t.origin.multiplyBy(n).subtract(this._map._getNewPixelOrigin(e,i)).round();y.any3d?Ct(t.el,o,n):H(t.el,o)},_resetGrid:function(){var t=this._map,e=t.options.crs,i=this._tileSize=this.getTileSize(),n=this._tileZoom,o=this._map.getPixelWorldBounds(this._tileZoom);o&&(this._globalTileRange=this._pxBoundsToTileRange(o)),this._wrapX=e.wrapLng&&!this.options.noWrap&&[Math.floor(t.project([0,e.wrapLng[0]],n).x/i.x),Math.ceil(t.project([0,e.wrapLng[1]],n).x/i.y)],this._wrapY=e.wrapLat&&!this.options.noWrap&&[Math.floor(t.project([e.wrapLat[0],0],n).y/i.x),Math.ceil(t.project([e.wrapLat[1],0],n).y/i.y)]},_onMoveEnd:function(){!this._map||this._map._animatingZoom||this._update()},_getTiledPixelBounds:function(t){var e=this._map,i=e._animatingZoom?Math.max(e._animateToZoom,e.getZoom()):e.getZoom(),n=e.getZoomScale(i,this._tileZoom),o=e.project(t,this._tileZoom).floor(),s=e.getSize().divideBy(n*2);return new N(o.subtract(s),o.add(s))},_update:function(t){var e=this._map;if(e){var i=this._clampZoom(e.getZoom());if(t===void 0&&(t=e.getCenter()),this._tileZoom!==void 0){var n=this._getTiledPixelBounds(t),o=this._pxBoundsToTileRange(n),s=o.getCenter(),r=[],h=this.options.keepBuffer,u=new N(o.getBottomLeft().subtract([h,-h]),o.getTopRight().add([h,-h]));if(!(isFinite(o.min.x)&&isFinite(o.min.y)&&isFinite(o.max.x)&&isFinite(o.max.y)))throw new Error("Attempted to load an infinite number of tiles");for(var c in this._tiles){var p=this._tiles[c].coords;(p.z!==this._tileZoom||!u.contains(new b(p.x,p.y)))&&(this._tiles[c].current=!1)}if(Math.abs(i-this._tileZoom)>1){this._setView(t,i);return}for(var x=o.min.y;x<=o.max.y;x++)for(var S=o.min.x;S<=o.max.x;S++){var Y=new b(S,x);if(Y.z=this._tileZoom,!!this._isValidTile(Y)){var V=this._tiles[this._tileCoordsToKey(Y)];V?V.current=!0:r.push(Y)}}if(r.sort(function(Q,Dt){return Q.distanceTo(s)-Dt.distanceTo(s)}),r.length!==0){this._loading||(this._loading=!0,this.fire("loading"));var ot=document.createDocumentFragment();for(S=0;S<r.length;S++)this._addTile(r[S],ot);this._level.el.appendChild(ot)}}}},_isValidTile:function(t){var e=this._map.options.crs;if(!e.infinite){var i=this._globalTileRange;if(!e.wrapLng&&(t.x<i.min.x||t.x>i.max.x)||!e.wrapLat&&(t.y<i.min.y||t.y>i.max.y))return!1}if(!this.options.bounds)return!0;var n=this._tileCoordsToBounds(t);return F(this.options.bounds).overlaps(n)},_keyToBounds:function(t){return this._tileCoordsToBounds(this._keyToTileCoords(t))},_tileCoordsToNwSe:function(t){var e=this._map,i=this.getTileSize(),n=t.scaleBy(i),o=n.add(i),s=e.unproject(n,t.z),r=e.unproject(o,t.z);return[s,r]},_tileCoordsToBounds:function(t){var e=this._tileCoordsToNwSe(t),i=new J(e[0],e[1]);return this.options.noWrap||(i=this._map.wrapLatLngBounds(i)),i},_tileCoordsToKey:function(t){return t.x+":"+t.y+":"+t.z},_keyToTileCoords:function(t){var e=t.split(":"),i=new b(+e[0],+e[1]);return i.z=+e[2],i},_removeTile:function(t){var e=this._tiles[t];e&&(R(e.el),delete this._tiles[t],this.fire("tileunload",{tile:e.el,coords:this._keyToTileCoords(t)}))},_initTile:function(t){C(t,"leaflet-tile");var e=this.getTileSize();t.style.width=e.x+"px",t.style.height=e.y+"px",t.onselectstart=g,t.onmousemove=g,y.ielt9&&this.options.opacity<1&&it(t,this.options.opacity)},_addTile:function(t,e){var i=this._getTilePos(t),n=this._tileCoordsToKey(t),o=this.createTile(this._wrapCoords(t),v(this._tileReady,this,t));this._initTile(o),this.createTile.length<2&&X(v(this._tileReady,this,t,null,o)),H(o,i),this._tiles[n]={el:o,coords:t,current:!0},e.appendChild(o),this.fire("tileloadstart",{tile:o,coords:t})},_tileReady:function(t,e,i){e&&this.fire("tileerror",{error:e,tile:i,coords:t});var n=this._tileCoordsToKey(t);i=this._tiles[n],i&&(i.loaded=+new Date,this._map._fadeAnimated?(it(i.el,0),et(this._fadeFrame),this._fadeFrame=X(this._updateOpacity,this)):(i.active=!0,this._pruneTiles()),e||(C(i.el,"leaflet-tile-loaded"),this.fire("tileload",{tile:i.el,coords:t})),this._noTilesToLoad()&&(this._loading=!1,this.fire("load"),y.ielt9||!this._map._fadeAnimated?X(this._pruneTiles,this):setTimeout(v(this._pruneTiles,this),250)))},_getTilePos:function(t){return t.scaleBy(this.getTileSize()).subtract(this._level.origin)},_wrapCoords:function(t){var e=new b(this._wrapX?M(t.x,this._wrapX):t.x,this._wrapY?M(t.y,this._wrapY):t.y);return e.z=t.z,e},_pxBoundsToTileRange:function(t){var e=this.getTileSize();return new N(t.min.unscaleBy(e).floor(),t.max.unscaleBy(e).ceil().subtract([1,1]))},_noTilesToLoad:function(){for(var t in this._tiles)if(!this._tiles[t].loaded)return!1;return!0}});function Yo(t){return new Jt(t)}var Rt=Jt.extend({options:{minZoom:0,maxZoom:18,subdomains:"abc",errorTileUrl:"",zoomOffset:0,tms:!1,zoomReverse:!1,detectRetina:!1,crossOrigin:!1,referrerPolicy:!1},initialize:function(t,e){this._url=t,e=A(this,e),e.detectRetina&&y.retina&&e.maxZoom>0?(e.tileSize=Math.floor(e.tileSize/2),e.zoomReverse?(e.zoomOffset--,e.minZoom=Math.min(e.maxZoom,e.minZoom+1)):(e.zoomOffset++,e.maxZoom=Math.max(e.minZoom,e.maxZoom-1)),e.minZoom=Math.max(0,e.minZoom)):e.zoomReverse?e.minZoom=Math.min(e.maxZoom,e.minZoom):e.maxZoom=Math.max(e.minZoom,e.maxZoom),typeof e.subdomains=="string"&&(e.subdomains=e.subdomains.split("")),this.on("tileunload",this._onTileRemove)},setUrl:function(t,e){return this._url===t&&e===void 0&&(e=!0),this._url=t,e||this.redraw(),this},createTile:function(t,e){var i=document.createElement("img");return T(i,"load",v(this._tileOnLoad,this,e,i)),T(i,"error",v(this._tileOnError,this,e,i)),(this.options.crossOrigin||this.options.crossOrigin==="")&&(i.crossOrigin=this.options.crossOrigin===!0?"":this.options.crossOrigin),typeof this.options.referrerPolicy=="string"&&(i.referrerPolicy=this.options.referrerPolicy),i.alt="",i.src=this.getTileUrl(t),i},getTileUrl:function(t){var e={r:y.retina?"@2x":"",s:this._getSubdomain(t),x:t.x,y:t.y,z:this._getZoomForUrl()};if(this._map&&!this._map.options.crs.infinite){var i=this._globalTileRange.max.y-t.y;this.options.tms&&(e.y=i),e["-y"]=i}return Tt(this._url,_(e,this.options))},_tileOnLoad:function(t,e){y.ielt9?setTimeout(v(t,this,null,e),0):t(null,e)},_tileOnError:function(t,e,i){var n=this.options.errorTileUrl;n&&e.getAttribute("src")!==n&&(e.src=n),t(i,e)},_onTileRemove:function(t){t.tile.onload=null},_getZoomForUrl:function(){var t=this._tileZoom,e=this.options.maxZoom,i=this.options.zoomReverse,n=this.options.zoomOffset;return i&&(t=e-t),t+n},_getSubdomain:function(t){var e=Math.abs(t.x+t.y)%this.options.subdomains.length;return this.options.subdomains[e]},_abortLoading:function(){var t,e;for(t in this._tiles)if(this._tiles[t].coords.z!==this._tileZoom&&(e=this._tiles[t].el,e.onload=g,e.onerror=g,!e.complete)){e.src=oe;var i=this._tiles[t].coords;R(e),delete this._tiles[t],this.fire("tileabort",{tile:e,coords:i})}},_removeTile:function(t){var e=this._tiles[t];if(e)return e.el.setAttribute("src",oe),Jt.prototype._removeTile.call(this,t)},_tileReady:function(t,e,i){if(!(!this._map||i&&i.getAttribute("src")===oe))return Jt.prototype._tileReady.call(this,t,e,i)}});function dn(t,e){return new Rt(t,e)}var fn=Rt.extend({defaultWmsParams:{service:"WMS",request:"GetMap",layers:"",styles:"",format:"image/jpeg",transparent:!1,version:"1.1.1"},options:{crs:null,uppercase:!1},initialize:function(t,e){this._url=t;var i=_({},this.defaultWmsParams);for(var n in e)n in this.options||(i[n]=e[n]);e=A(this,e);var o=e.detectRetina&&y.retina?2:1,s=this.getTileSize();i.width=s.x*o,i.height=s.y*o,this.wmsParams=i},onAdd:function(t){this._crs=this.options.crs||t.options.crs,this._wmsVersion=parseFloat(this.wmsParams.version);var e=this._wmsVersion>=1.3?"crs":"srs";this.wmsParams[e]=this._crs.code,Rt.prototype.onAdd.call(this,t)},getTileUrl:function(t){var e=this._tileCoordsToNwSe(t),i=this._crs,n=$(i.project(e[0]),i.project(e[1])),o=n.min,s=n.max,r=(this._wmsVersion>=1.3&&this._crs===sn?[o.y,o.x,s.y,s.x]:[o.x,o.y,s.x,s.y]).join(","),h=Rt.prototype.getTileUrl.call(this,t);return h+ie(this.wmsParams,h,this.options.uppercase)+(this.options.uppercase?"&BBOX=":"&bbox=")+r},setParams:function(t,e){return _(this.wmsParams,t),e||this.redraw(),this}});function Xo(t,e){return new fn(t,e)}Rt.WMS=fn,dn.wms=Xo;var yt=at.extend({options:{padding:.1},initialize:function(t){A(this,t),w(this),this._layers=this._layers||{}},onAdd:function(){this._container||(this._initContainer(),C(this._container,"leaflet-zoom-animated")),this.getPane().appendChild(this._container),this._update(),this.on("update",this._updatePaths,this)},onRemove:function(){this.off("update",this._updatePaths,this),this._destroyContainer()},getEvents:function(){var t={viewreset:this._reset,zoom:this._onZoom,moveend:this._update,zoomend:this._onZoomEnd};return this._zoomAnimated&&(t.zoomanim=this._onAnimZoom),t},_onAnimZoom:function(t){this._updateTransform(t.center,t.zoom)},_onZoom:function(){this._updateTransform(this._map.getCenter(),this._map.getZoom())},_updateTransform:function(t,e){var i=this._map.getZoomScale(e,this._zoom),n=this._map.getSize().multiplyBy(.5+this.options.padding),o=this._map.project(this._center,e),s=n.multiplyBy(-i).add(o).subtract(this._map._getNewPixelOrigin(t,e));y.any3d?Ct(this._container,s,i):H(this._container,s)},_reset:function(){this._update(),this._updateTransform(this._center,this._zoom);for(var t in this._layers)this._layers[t]._reset()},_onZoomEnd:function(){for(var t in this._layers)this._layers[t]._project()},_updatePaths:function(){for(var t in this._layers)this._layers[t]._update()},_update:function(){var t=this.options.padding,e=this._map.getSize(),i=this._map.containerPointToLayerPoint(e.multiplyBy(-t)).round();this._bounds=new N(i,i.add(e.multiplyBy(1+t*2)).round()),this._center=this._map.getCenter(),this._zoom=this._map.getZoom()}}),_n=yt.extend({options:{tolerance:0},getEvents:function(){var t=yt.prototype.getEvents.call(this);return t.viewprereset=this._onViewPreReset,t},_onViewPreReset:function(){this._postponeUpdatePaths=!0},onAdd:function(){yt.prototype.onAdd.call(this),this._draw()},_initContainer:function(){var t=this._container=document.createElement("canvas");T(t,"mousemove",this._onMouseMove,this),T(t,"click dblclick mousedown mouseup contextmenu",this._onClick,this),T(t,"mouseout",this._handleMouseOut,this),t._leaflet_disable_events=!0,this._ctx=t.getContext("2d")},_destroyContainer:function(){et(this._redrawRequest),delete this._ctx,R(this._container),B(this._container),delete this._container},_updatePaths:function(){if(!this._postponeUpdatePaths){var t;this._redrawBounds=null;for(var e in this._layers)t=this._layers[e],t._update();this._redraw()}},_update:function(){if(!(this._map._animatingZoom&&this._bounds)){yt.prototype._update.call(this);var t=this._bounds,e=this._container,i=t.getSize(),n=y.retina?2:1;H(e,t.min),e.width=n*i.x,e.height=n*i.y,e.style.width=i.x+"px",e.style.height=i.y+"px",y.retina&&this._ctx.scale(2,2),this._ctx.translate(-t.min.x,-t.min.y),this.fire("update")}},_reset:function(){yt.prototype._reset.call(this),this._postponeUpdatePaths&&(this._postponeUpdatePaths=!1,this._updatePaths())},_initPath:function(t){this._updateDashArray(t),this._layers[w(t)]=t;var e=t._order={layer:t,prev:this._drawLast,next:null};this._drawLast&&(this._drawLast.next=e),this._drawLast=e,this._drawFirst=this._drawFirst||this._drawLast},_addPath:function(t){this._requestRedraw(t)},_removePath:function(t){var e=t._order,i=e.next,n=e.prev;i?i.prev=n:this._drawLast=n,n?n.next=i:this._drawFirst=i,delete t._order,delete this._layers[w(t)],this._requestRedraw(t)},_updatePath:function(t){this._extendRedrawBounds(t),t._project(),t._update(),this._requestRedraw(t)},_updateStyle:function(t){this._updateDashArray(t),this._requestRedraw(t)},_updateDashArray:function(t){if(typeof t.options.dashArray=="string"){var e=t.options.dashArray.split(/[, ]+/),i=[],n,o;for(o=0;o<e.length;o++){if(n=Number(e[o]),isNaN(n))return;i.push(n)}t.options._dashArray=i}else t.options._dashArray=t.options.dashArray},_requestRedraw:function(t){this._map&&(this._extendRedrawBounds(t),this._redrawRequest=this._redrawRequest||X(this._redraw,this))},_extendRedrawBounds:function(t){if(t._pxBounds){var e=(t.options.weight||0)+1;this._redrawBounds=this._redrawBounds||new N,this._redrawBounds.extend(t._pxBounds.min.subtract([e,e])),this._redrawBounds.extend(t._pxBounds.max.add([e,e]))}},_redraw:function(){this._redrawRequest=null,this._redrawBounds&&(this._redrawBounds.min._floor(),this._redrawBounds.max._ceil()),this._clear(),this._draw(),this._redrawBounds=null},_clear:function(){var t=this._redrawBounds;if(t){var e=t.getSize();this._ctx.clearRect(t.min.x,t.min.y,e.x,e.y)}else this._ctx.save(),this._ctx.setTransform(1,0,0,1,0,0),this._ctx.clearRect(0,0,this._container.width,this._container.height),this._ctx.restore()},_draw:function(){var t,e=this._redrawBounds;if(this._ctx.save(),e){var i=e.getSize();this._ctx.beginPath(),this._ctx.rect(e.min.x,e.min.y,i.x,i.y),this._ctx.clip()}this._drawing=!0;for(var n=this._drawFirst;n;n=n.next)t=n.layer,(!e||t._pxBounds&&t._pxBounds.intersects(e))&&t._updatePath();this._drawing=!1,this._ctx.restore()},_updatePoly:function(t,e){if(this._drawing){var i,n,o,s,r=t._parts,h=r.length,u=this._ctx;if(h){for(u.beginPath(),i=0;i<h;i++){for(n=0,o=r[i].length;n<o;n++)s=r[i][n],u[n?"lineTo":"moveTo"](s.x,s.y);e&&u.closePath()}this._fillStroke(u,t)}}},_updateCircle:function(t){if(!(!this._drawing||t._empty())){var e=t._point,i=this._ctx,n=Math.max(Math.round(t._radius),1),o=(Math.max(Math.round(t._radiusY),1)||n)/n;o!==1&&(i.save(),i.scale(1,o)),i.beginPath(),i.arc(e.x,e.y/o,n,0,Math.PI*2,!1),o!==1&&i.restore(),this._fillStroke(i,t)}},_fillStroke:function(t,e){var i=e.options;i.fill&&(t.globalAlpha=i.fillOpacity,t.fillStyle=i.fillColor||i.color,t.fill(i.fillRule||"evenodd")),i.stroke&&i.weight!==0&&(t.setLineDash&&t.setLineDash(e.options&&e.options._dashArray||[]),t.globalAlpha=i.opacity,t.lineWidth=i.weight,t.strokeStyle=i.color,t.lineCap=i.lineCap,t.lineJoin=i.lineJoin,t.stroke())},_onClick:function(t){for(var e=this._map.mouseEventToLayerPoint(t),i,n,o=this._drawFirst;o;o=o.next)i=o.layer,i.options.interactive&&i._containsPoint(e)&&(!(t.type==="click"||t.type==="preclick")||!this._map._draggableMoved(i))&&(n=i);this._fireEvent(n?[n]:!1,t)},_onMouseMove:function(t){if(!(!this._map||this._map.dragging.moving()||this._map._animatingZoom)){var e=this._map.mouseEventToLayerPoint(t);this._handleMouseHover(t,e)}},_handleMouseOut:function(t){var e=this._hoveredLayer;e&&(D(this._container,"leaflet-interactive"),this._fireEvent([e],t,"mouseout"),this._hoveredLayer=null,this._mouseHoverThrottled=!1)},_handleMouseHover:function(t,e){if(!this._mouseHoverThrottled){for(var i,n,o=this._drawFirst;o;o=o.next)i=o.layer,i.options.interactive&&i._containsPoint(e)&&(n=i);n!==this._hoveredLayer&&(this._handleMouseOut(t),n&&(C(this._container,"leaflet-interactive"),this._fireEvent([n],t,"mouseover"),this._hoveredLayer=n)),this._fireEvent(this._hoveredLayer?[this._hoveredLayer]:!1,t),this._mouseHoverThrottled=!0,setTimeout(v(function(){this._mouseHoverThrottled=!1},this),32)}},_fireEvent:function(t,e,i){this._map._fireDOMEvent(e,i||e.type,t)},_bringToFront:function(t){var e=t._order;if(e){var i=e.next,n=e.prev;if(i)i.prev=n;else return;n?n.next=i:i&&(this._drawFirst=i),e.prev=this._drawLast,this._drawLast.next=e,e.next=null,this._drawLast=e,this._requestRedraw(t)}},_bringToBack:function(t){var e=t._order;if(e){var i=e.next,n=e.prev;if(n)n.next=i;else return;i?i.prev=n:n&&(this._drawLast=n),e.prev=null,e.next=this._drawFirst,this._drawFirst.prev=e,this._drawFirst=e,this._requestRedraw(t)}}});function mn(t){return y.canvas?new _n(t):null}var Qt=function(){try{return document.namespaces.add("lvml","urn:schemas-microsoft-com:vml"),function(t){return document.createElement("<lvml:"+t+' class="lvml">')}}catch{}return function(t){return document.createElement("<"+t+' xmlns="urn:schemas-microsoft.com:vml" class="lvml">')}}(),$o={_initContainer:function(){this._container=Z("div","leaflet-vml-container")},_update:function(){this._map._animatingZoom||(yt.prototype._update.call(this),this.fire("update"))},_initPath:function(t){var e=t._container=Qt("shape");C(e,"leaflet-vml-shape "+(this.options.className||"")),e.coordsize="1 1",t._path=Qt("path"),e.appendChild(t._path),this._updateStyle(t),this._layers[w(t)]=t},_addPath:function(t){var e=t._container;this._container.appendChild(e),t.options.interactive&&t.addInteractiveTarget(e)},_removePath:function(t){var e=t._container;R(e),t.removeInteractiveTarget(e),delete this._layers[w(t)]},_updateStyle:function(t){var e=t._stroke,i=t._fill,n=t.options,o=t._container;o.stroked=!!n.stroke,o.filled=!!n.fill,n.stroke?(e||(e=t._stroke=Qt("stroke")),o.appendChild(e),e.weight=n.weight+"px",e.color=n.color,e.opacity=n.opacity,n.dashArray?e.dashStyle=st(n.dashArray)?n.dashArray.join(" "):n.dashArray.replace(/( *, *)/g," "):e.dashStyle="",e.endcap=n.lineCap.replace("butt","flat"),e.joinstyle=n.lineJoin):e&&(o.removeChild(e),t._stroke=null),n.fill?(i||(i=t._fill=Qt("fill")),o.appendChild(i),i.color=n.fillColor||n.color,i.opacity=n.fillOpacity):i&&(o.removeChild(i),t._fill=null)},_updateCircle:function(t){var e=t._point.round(),i=Math.round(t._radius),n=Math.round(t._radiusY||i);this._setPath(t,t._empty()?"M0 0":"AL "+e.x+","+e.y+" "+i+","+n+" 0,"+65535*360)},_setPath:function(t,e){t._path.v=e},_bringToFront:function(t){Ot(t._container)},_bringToBack:function(t){Zt(t._container)}},Pe=y.vml?Qt:vi,te=yt.extend({_initContainer:function(){this._container=Pe("svg"),this._container.setAttribute("pointer-events","none"),this._rootGroup=Pe("g"),this._container.appendChild(this._rootGroup)},_destroyContainer:function(){R(this._container),B(this._container),delete this._container,delete this._rootGroup,delete this._svgSize},_update:function(){if(!(this._map._animatingZoom&&this._bounds)){yt.prototype._update.call(this);var t=this._bounds,e=t.getSize(),i=this._container;(!this._svgSize||!this._svgSize.equals(e))&&(this._svgSize=e,i.setAttribute("width",e.x),i.setAttribute("height",e.y)),H(i,t.min),i.setAttribute("viewBox",[t.min.x,t.min.y,e.x,e.y].join(" ")),this.fire("update")}},_initPath:function(t){var e=t._path=Pe("path");t.options.className&&C(e,t.options.className),t.options.interactive&&C(e,"leaflet-interactive"),this._updateStyle(t),this._layers[w(t)]=t},_addPath:function(t){this._rootGroup||this._initContainer(),this._rootGroup.appendChild(t._path),t.addInteractiveTarget(t._path)},_removePath:function(t){R(t._path),t.removeInteractiveTarget(t._path),delete this._layers[w(t)]},_updatePath:function(t){t._project(),t._update()},_updateStyle:function(t){var e=t._path,i=t.options;e&&(i.stroke?(e.setAttribute("stroke",i.color),e.setAttribute("stroke-opacity",i.opacity),e.setAttribute("stroke-width",i.weight),e.setAttribute("stroke-linecap",i.lineCap),e.setAttribute("stroke-linejoin",i.lineJoin),i.dashArray?e.setAttribute("stroke-dasharray",i.dashArray):e.removeAttribute("stroke-dasharray"),i.dashOffset?e.setAttribute("stroke-dashoffset",i.dashOffset):e.removeAttribute("stroke-dashoffset")):e.setAttribute("stroke","none"),i.fill?(e.setAttribute("fill",i.fillColor||i.color),e.setAttribute("fill-opacity",i.fillOpacity),e.setAttribute("fill-rule",i.fillRule||"evenodd")):e.setAttribute("fill","none"))},_updatePoly:function(t,e){this._setPath(t,yi(t._parts,e))},_updateCircle:function(t){var e=t._point,i=Math.max(Math.round(t._radius),1),n=Math.max(Math.round(t._radiusY),1)||i,o="a"+i+","+n+" 0 1,0 ",s=t._empty()?"M0 0":"M"+(e.x-i)+","+e.y+o+i*2+",0 "+o+-i*2+",0 ";this._setPath(t,s)},_setPath:function(t,e){t._path.setAttribute("d",e)},_bringToFront:function(t){Ot(t._path)},_bringToBack:function(t){Zt(t._path)}});y.vml&&te.include($o);function pn(t){return y.svg||y.vml?new te(t):null}k.include({getRenderer:function(t){var e=t.options.renderer||this._getPaneRenderer(t.options.pane)||this.options.renderer||this._renderer;return e||(e=this._renderer=this._createRenderer()),this.hasLayer(e)||this.addLayer(e),e},_getPaneRenderer:function(t){if(t==="overlayPane"||t===void 0)return!1;var e=this._paneRenderers[t];return e===void 0&&(e=this._createRenderer({pane:t}),this._paneRenderers[t]=e),e},_createRenderer:function(t){return this.options.preferCanvas&&mn(t)||pn(t)}});var gn=Bt.extend({initialize:function(t,e){Bt.prototype.initialize.call(this,this._boundsToLatLngs(t),e)},setBounds:function(t){return this.setLatLngs(this._boundsToLatLngs(t))},_boundsToLatLngs:function(t){return t=F(t),[t.getSouthWest(),t.getNorthWest(),t.getNorthEast(),t.getSouthEast()]}});function Jo(t,e){return new gn(t,e)}te.create=Pe,te.pointsToPath=yi,vt.geometryToLayer=me,vt.coordsToLatLng=ni,vt.coordsToLatLngs=pe,vt.latLngToCoords=oi,vt.latLngsToCoords=ge,vt.getFeature=Nt,vt.asFeature=ve,k.mergeOptions({boxZoom:!0});var vn=ct.extend({initialize:function(t){this._map=t,this._container=t._container,this._pane=t._panes.overlayPane,this._resetStateTimeout=0,t.on("unload",this._destroy,this)},addHooks:function(){T(this._container,"mousedown",this._onMouseDown,this)},removeHooks:function(){B(this._container,"mousedown",this._onMouseDown,this)},moved:function(){return this._moved},_destroy:function(){R(this._pane),delete this._pane},_resetState:function(){this._resetStateTimeout=0,this._moved=!1},_clearDeferredResetState:function(){this._resetStateTimeout!==0&&(clearTimeout(this._resetStateTimeout),this._resetStateTimeout=0)},_onMouseDown:function(t){if(!t.shiftKey||t.which!==1&&t.button!==1)return!1;this._clearDeferredResetState(),this._resetState(),Vt(),je(),this._startPoint=this._map.mouseEventToContainerPoint(t),T(document,{contextmenu:zt,mousemove:this._onMouseMove,mouseup:this._onMouseUp,keydown:this._onKeyDown},this)},_onMouseMove:function(t){this._moved||(this._moved=!0,this._box=Z("div","leaflet-zoom-box",this._container),C(this._container,"leaflet-crosshair"),this._map.fire("boxzoomstart")),this._point=this._map.mouseEventToContainerPoint(t);var e=new N(this._point,this._startPoint),i=e.getSize();H(this._box,e.min),this._box.style.width=i.x+"px",this._box.style.height=i.y+"px"},_finish:function(){this._moved&&(R(this._box),D(this._container,"leaflet-crosshair")),Gt(),Fe(),B(document,{contextmenu:zt,mousemove:this._onMouseMove,mouseup:this._onMouseUp,keydown:this._onKeyDown},this)},_onMouseUp:function(t){if(!(t.which!==1&&t.button!==1)&&(this._finish(),!!this._moved)){this._clearDeferredResetState(),this._resetStateTimeout=setTimeout(v(this._resetState,this),0);var e=new J(this._map.containerPointToLatLng(this._startPoint),this._map.containerPointToLatLng(this._point));this._map.fitBounds(e).fire("boxzoomend",{boxZoomBounds:e})}},_onKeyDown:function(t){t.keyCode===27&&(this._finish(),this._clearDeferredResetState(),this._resetState())}});k.addInitHook("addHandler","boxZoom",vn),k.mergeOptions({doubleClickZoom:!0});var yn=ct.extend({addHooks:function(){this._map.on("dblclick",this._onDoubleClick,this)},removeHooks:function(){this._map.off("dblclick",this._onDoubleClick,this)},_onDoubleClick:function(t){var e=this._map,i=e.getZoom(),n=e.options.zoomDelta,o=t.originalEvent.shiftKey?i-n:i+n;e.options.doubleClickZoom==="center"?e.setZoom(o):e.setZoomAround(t.containerPoint,o)}});k.addInitHook("addHandler","doubleClickZoom",yn),k.mergeOptions({dragging:!0,inertia:!0,inertiaDeceleration:3400,inertiaMaxSpeed:1/0,easeLinearity:.2,worldCopyJump:!1,maxBoundsViscosity:0});var xn=ct.extend({addHooks:function(){if(!this._draggable){var t=this._map;this._draggable=new wt(t._mapPane,t._container),this._draggable.on({dragstart:this._onDragStart,drag:this._onDrag,dragend:this._onDragEnd},this),this._draggable.on("predrag",this._onPreDragLimit,this),t.options.worldCopyJump&&(this._draggable.on("predrag",this._onPreDragWrap,this),t.on("zoomend",this._onZoomEnd,this),t.whenReady(this._onZoomEnd,this))}C(this._map._container,"leaflet-grab leaflet-touch-drag"),this._draggable.enable(),this._positions=[],this._times=[]},removeHooks:function(){D(this._map._container,"leaflet-grab"),D(this._map._container,"leaflet-touch-drag"),this._draggable.disable()},moved:function(){return this._draggable&&this._draggable._moved},moving:function(){return this._draggable&&this._draggable._moving},_onDragStart:function(){var t=this._map;if(t._stop(),this._map.options.maxBounds&&this._map.options.maxBoundsViscosity){var e=F(this._map.options.maxBounds);this._offsetLimit=$(this._map.latLngToContainerPoint(e.getNorthWest()).multiplyBy(-1),this._map.latLngToContainerPoint(e.getSouthEast()).multiplyBy(-1).add(this._map.getSize())),this._viscosity=Math.min(1,Math.max(0,this._map.options.maxBoundsViscosity))}else this._offsetLimit=null;t.fire("movestart").fire("dragstart"),t.options.inertia&&(this._positions=[],this._times=[])},_onDrag:function(t){if(this._map.options.inertia){var e=this._lastTime=+new Date,i=this._lastPos=this._draggable._absPos||this._draggable._newPos;this._positions.push(i),this._times.push(e),this._prunePositions(e)}this._map.fire("move",t).fire("drag",t)},_prunePositions:function(t){for(;this._positions.length>1&&t-this._times[0]>50;)this._positions.shift(),this._times.shift()},_onZoomEnd:function(){var t=this._map.getSize().divideBy(2),e=this._map.latLngToLayerPoint([0,0]);this._initialWorldOffset=e.subtract(t).x,this._worldWidth=this._map.getPixelWorldBounds().getSize().x},_viscousLimit:function(t,e){return t-(t-e)*this._viscosity},_onPreDragLimit:function(){if(!(!this._viscosity||!this._offsetLimit)){var t=this._draggable._newPos.subtract(this._draggable._startPos),e=this._offsetLimit;t.x<e.min.x&&(t.x=this._viscousLimit(t.x,e.min.x)),t.y<e.min.y&&(t.y=this._viscousLimit(t.y,e.min.y)),t.x>e.max.x&&(t.x=this._viscousLimit(t.x,e.max.x)),t.y>e.max.y&&(t.y=this._viscousLimit(t.y,e.max.y)),this._draggable._newPos=this._draggable._startPos.add(t)}},_onPreDragWrap:function(){var t=this._worldWidth,e=Math.round(t/2),i=this._initialWorldOffset,n=this._draggable._newPos.x,o=(n-e+i)%t+e-i,s=(n+e+i)%t-e-i,r=Math.abs(o+i)<Math.abs(s+i)?o:s;this._draggable._absPos=this._draggable._newPos.clone(),this._draggable._newPos.x=r},_onDragEnd:function(t){var e=this._map,i=e.options,n=!i.inertia||t.noInertia||this._times.length<2;if(e.fire("dragend",t),n)e.fire("moveend");else{this._prunePositions(+new Date);var o=this._lastPos.subtract(this._positions[0]),s=(this._lastTime-this._times[0])/1e3,r=i.easeLinearity,h=o.multiplyBy(r/s),u=h.distanceTo([0,0]),c=Math.min(i.inertiaMaxSpeed,u),p=h.multiplyBy(c/u),x=c/(i.inertiaDeceleration*r),S=p.multiplyBy(-x/2).round();!S.x&&!S.y?e.fire("moveend"):(S=e._limitOffset(S,e.options.maxBounds),X(function(){e.panBy(S,{duration:x,easeLinearity:r,noMoveStart:!0,animate:!0})}))}}});k.addInitHook("addHandler","dragging",xn),k.mergeOptions({keyboard:!0,keyboardPanDelta:80});var wn=ct.extend({keyCodes:{left:[37],right:[39],down:[40],up:[38],zoomIn:[187,107,61,171],zoomOut:[189,109,54,173]},initialize:function(t){this._map=t,this._setPanDelta(t.options.keyboardPanDelta),this._setZoomDelta(t.options.zoomDelta)},addHooks:function(){var t=this._map._container;t.tabIndex<=0&&(t.tabIndex="0"),T(t,{focus:this._onFocus,blur:this._onBlur,mousedown:this._onMouseDown},this),this._map.on({focus:this._addHooks,blur:this._removeHooks},this)},removeHooks:function(){this._removeHooks(),B(this._map._container,{focus:this._onFocus,blur:this._onBlur,mousedown:this._onMouseDown},this),this._map.off({focus:this._addHooks,blur:this._removeHooks},this)},_onMouseDown:function(){if(!this._focused){var t=document.body,e=document.documentElement,i=t.scrollTop||e.scrollTop,n=t.scrollLeft||e.scrollLeft;this._map._container.focus(),window.scrollTo(n,i)}},_onFocus:function(){this._focused=!0,this._map.fire("focus")},_onBlur:function(){this._focused=!1,this._map.fire("blur")},_setPanDelta:function(t){var e=this._panKeys={},i=this.keyCodes,n,o;for(n=0,o=i.left.length;n<o;n++)e[i.left[n]]=[-1*t,0];for(n=0,o=i.right.length;n<o;n++)e[i.right[n]]=[t,0];for(n=0,o=i.down.length;n<o;n++)e[i.down[n]]=[0,t];for(n=0,o=i.up.length;n<o;n++)e[i.up[n]]=[0,-1*t]},_setZoomDelta:function(t){var e=this._zoomKeys={},i=this.keyCodes,n,o;for(n=0,o=i.zoomIn.length;n<o;n++)e[i.zoomIn[n]]=t;for(n=0,o=i.zoomOut.length;n<o;n++)e[i.zoomOut[n]]=-t},_addHooks:function(){T(document,"keydown",this._onKeyDown,this)},_removeHooks:function(){B(document,"keydown",this._onKeyDown,this)},_onKeyDown:function(t){if(!(t.altKey||t.ctrlKey||t.metaKey)){var e=t.keyCode,i=this._map,n;if(e in this._panKeys){if(!i._panAnim||!i._panAnim._inProgress)if(n=this._panKeys[e],t.shiftKey&&(n=P(n).multiplyBy(3)),i.options.maxBounds&&(n=i._limitOffset(P(n),i.options.maxBounds)),i.options.worldCopyJump){var o=i.wrapLatLng(i.unproject(i.project(i.getCenter()).add(n)));i.panTo(o)}else i.panBy(n)}else if(e in this._zoomKeys)i.setZoom(i.getZoom()+(t.shiftKey?3:1)*this._zoomKeys[e]);else if(e===27&&i._popup&&i._popup.options.closeOnEscapeKey)i.closePopup();else return;zt(t)}}});k.addInitHook("addHandler","keyboard",wn),k.mergeOptions({scrollWheelZoom:!0,wheelDebounceTime:40,wheelPxPerZoomLevel:60});var Pn=ct.extend({addHooks:function(){T(this._map._container,"wheel",this._onWheelScroll,this),this._delta=0},removeHooks:function(){B(this._map._container,"wheel",this._onWheelScroll,this)},_onWheelScroll:function(t){var e=Vi(t),i=this._map.options.wheelDebounceTime;this._delta+=e,this._lastMousePos=this._map.mouseEventToContainerPoint(t),this._startTime||(this._startTime=+new Date);var n=Math.max(i-(+new Date-this._startTime),0);clearTimeout(this._timer),this._timer=setTimeout(v(this._performZoom,this),n),zt(t)},_performZoom:function(){var t=this._map,e=t.getZoom(),i=this._map.options.zoomSnap||0;t._stop();var n=this._delta/(this._map.options.wheelPxPerZoomLevel*4),o=4*Math.log(2/(1+Math.exp(-Math.abs(n))))/Math.LN2,s=i?Math.ceil(o/i)*i:o,r=t._limitZoom(e+(this._delta>0?s:-s))-e;this._delta=0,this._startTime=null,r&&(t.options.scrollWheelZoom==="center"?t.setZoom(e+r):t.setZoomAround(this._lastMousePos,e+r))}});k.addInitHook("addHandler","scrollWheelZoom",Pn);var Qo=600;k.mergeOptions({tapHold:y.touchNative&&y.safari&&y.mobile,tapTolerance:15});var Ln=ct.extend({addHooks:function(){T(this._map._container,"touchstart",this._onDown,this)},removeHooks:function(){B(this._map._container,"touchstart",this._onDown,this)},_onDown:function(t){if(clearTimeout(this._holdTimeout),t.touches.length===1){var e=t.touches[0];this._startPos=this._newPos=new b(e.clientX,e.clientY),this._holdTimeout=setTimeout(v(function(){this._cancel(),this._isTapValid()&&(T(document,"touchend",K),T(document,"touchend touchcancel",this._cancelClickPrevent),this._simulateEvent("contextmenu",e))},this),Qo),T(document,"touchend touchcancel contextmenu",this._cancel,this),T(document,"touchmove",this._onMove,this)}},_cancelClickPrevent:function t(){B(document,"touchend",K),B(document,"touchend touchcancel",t)},_cancel:function(){clearTimeout(this._holdTimeout),B(document,"touchend touchcancel contextmenu",this._cancel,this),B(document,"touchmove",this._onMove,this)},_onMove:function(t){var e=t.touches[0];this._newPos=new b(e.clientX,e.clientY)},_isTapValid:function(){return this._newPos.distanceTo(this._startPos)<=this._map.options.tapTolerance},_simulateEvent:function(t,e){var i=new MouseEvent(t,{bubbles:!0,cancelable:!0,view:window,screenX:e.screenX,screenY:e.screenY,clientX:e.clientX,clientY:e.clientY});i._simulated=!0,e.target.dispatchEvent(i)}});k.addInitHook("addHandler","tapHold",Ln),k.mergeOptions({touchZoom:y.touch,bounceAtZoomLimits:!0});var bn=ct.extend({addHooks:function(){C(this._map._container,"leaflet-touch-zoom"),T(this._map._container,"touchstart",this._onTouchStart,this)},removeHooks:function(){D(this._map._container,"leaflet-touch-zoom"),B(this._map._container,"touchstart",this._onTouchStart,this)},_onTouchStart:function(t){var e=this._map;if(!(!t.touches||t.touches.length!==2||e._animatingZoom||this._zooming)){var i=e.mouseEventToContainerPoint(t.touches[0]),n=e.mouseEventToContainerPoint(t.touches[1]);this._centerPoint=e.getSize()._divideBy(2),this._startLatLng=e.containerPointToLatLng(this._centerPoint),e.options.touchZoom!=="center"&&(this._pinchStartLatLng=e.containerPointToLatLng(i.add(n)._divideBy(2))),this._startDist=i.distanceTo(n),this._startZoom=e.getZoom(),this._moved=!1,this._zooming=!0,e._stop(),T(document,"touchmove",this._onTouchMove,this),T(document,"touchend touchcancel",this._onTouchEnd,this),K(t)}},_onTouchMove:function(t){if(!(!t.touches||t.touches.length!==2||!this._zooming)){var e=this._map,i=e.mouseEventToContainerPoint(t.touches[0]),n=e.mouseEventToContainerPoint(t.touches[1]),o=i.distanceTo(n)/this._startDist;if(this._zoom=e.getScaleZoom(o,this._startZoom),!e.options.bounceAtZoomLimits&&(this._zoom<e.getMinZoom()&&o<1||this._zoom>e.getMaxZoom()&&o>1)&&(this._zoom=e._limitZoom(this._zoom)),e.options.touchZoom==="center"){if(this._center=this._startLatLng,o===1)return}else{var s=i._add(n)._divideBy(2)._subtract(this._centerPoint);if(o===1&&s.x===0&&s.y===0)return;this._center=e.unproject(e.project(this._pinchStartLatLng,this._zoom).subtract(s),this._zoom)}this._moved||(e._moveStart(!0,!1),this._moved=!0),et(this._animRequest);var r=v(e._move,e,this._center,this._zoom,{pinch:!0,round:!1},void 0);this._animRequest=X(r,this,!0),K(t)}},_onTouchEnd:function(){if(!this._moved||!this._zooming){this._zooming=!1;return}this._zooming=!1,et(this._animRequest),B(document,"touchmove",this._onTouchMove,this),B(document,"touchend touchcancel",this._onTouchEnd,this),this._map.options.zoomAnimation?this._map._animateZoom(this._center,this._map._limitZoom(this._zoom),!0,this._map.options.zoomSnap):this._map._resetView(this._center,this._map._limitZoom(this._zoom))}});k.addInitHook("addHandler","touchZoom",bn),k.BoxZoom=vn,k.DoubleClickZoom=yn,k.Drag=xn,k.Keyboard=wn,k.ScrollWheelZoom=Pn,k.TapHold=Ln,k.TouchZoom=bn,a.Bounds=N,a.Browser=y,a.CRS=mt,a.Canvas=_n,a.Circle=ii,a.CircleMarker=_e,a.Class=_t,a.Control=rt,a.DivIcon=cn,a.DivOverlay=dt,a.DomEvent=go,a.DomUtil=mo,a.Draggable=wt,a.Evented=jt,a.FeatureGroup=pt,a.GeoJSON=vt,a.GridLayer=Jt,a.Handler=ct,a.Icon=At,a.ImageOverlay=ye,a.LatLng=I,a.LatLngBounds=J,a.Layer=at,a.LayerGroup=It,a.LineUtil=Eo,a.Map=k,a.Marker=fe,a.Mixin=bo,a.Path=Pt,a.Point=b,a.PolyUtil=To,a.Polygon=Bt,a.Polyline=gt,a.Popup=xe,a.PosAnimation=Gi,a.Projection=ko,a.Rectangle=gn,a.Renderer=yt,a.SVG=te,a.SVGOverlay=ln,a.TileLayer=Rt,a.Tooltip=we,a.Transformation=Se,a.Util=Bn,a.VideoOverlay=un,a.bind=v,a.bounds=$,a.canvas=mn,a.circle=Do,a.circleMarker=Ro,a.control=Yt,a.divIcon=Ko,a.extend=_,a.featureGroup=Ao,a.geoJSON=hn,a.geoJson=Ho,a.gridLayer=Yo,a.icon=Bo,a.imageOverlay=Wo,a.latLng=E,a.latLngBounds=F,a.layerGroup=Io,a.map=vo,a.marker=No,a.point=P,a.polygon=Fo,a.polyline=jo,a.popup=Go,a.rectangle=Jo,a.setOptions=A,a.stamp=w,a.svg=pn,a.svgOverlay=Vo,a.tileLayer=dn,a.tooltip=qo,a.transformation=Ft,a.version=m,a.videoOverlay=Uo;var ts=window.L;a.noConflict=function(){return window.L=ts,this},window.L=a})}(ee,ee.exports)),ee.exports}var bt=Ts();function ci(d,f,a){return Object.freeze({instance:d,context:f,container:a})}function di(d,f){return f==null?function(m,_){const O=z.useRef();return O.current||(O.current=d(m,_)),O}:function(m,_){const O=z.useRef();O.current||(O.current=d(m,_));const v=z.useRef(m),{instance:G}=O.current;return z.useEffect(function(){v.current!==m&&(f(G,m,v.current),v.current=m)},[G,m,_]),O}}function Cs(d,f){z.useEffect(function(){return(f.layerContainer??f.map).addLayer(d.instance),function(){var O;(O=f.layerContainer)==null||O.removeLayer(d.instance),f.map.removeLayer(d.instance)}},[f,d])}function An(d){return function(a){const m=ui(),_=d(li(a,m),m);return kn(m.map,a.attribution),In(_.current,a.eventHandlers),Cs(_.current,m),_}}function Ms(d,f){const a=di(d,f),m=An(a);return xs(m)}function Ss(d,f){const a=di(d),m=Ls(a,f);return ws(m)}function zs(d,f){const a=di(d,f),m=An(a);return Ps(m)}function Es(d,f,a){const{opacity:m,zIndex:_}=f;m!=null&&m!==a.opacity&&d.setOpacity(m),_!=null&&_!==a.zIndex&&d.setZIndex(_)}function ks(){return ui().map}function hi(){return hi=Object.assign||function(d){for(var f=1;f<arguments.length;f++){var a=arguments[f];for(var m in a)Object.prototype.hasOwnProperty.call(a,m)&&(d[m]=a[m])}return d},hi.apply(this,arguments)}function Os({bounds:d,boundsOptions:f,center:a,children:m,className:_,id:O,placeholder:v,style:G,whenReady:w,zoom:U,...M},g){const[j]=z.useState({className:_,id:O,style:G}),[q,ft]=z.useState(null);z.useImperativeHandle(g,()=>(q==null?void 0:q.map)??null,[q]);const A=z.useCallback(ne=>{if(ne!==null&&q===null){const Tt=new bt.Map(ne,M);a!=null&&U!=null?Tt.setView(a,U):d!=null&&Tt.fitBounds(d,f),w!=null&&Tt.whenReady(w),ft(vs(Tt))}},[]);z.useEffect(()=>()=>{q==null||q.map.remove()},[q]);const ie=q?ai.createElement(Zn,{value:q},m):v??null;return ai.createElement("div",hi({},j,{ref:A}),ie)}const Zs=z.forwardRef(Os),Is=Ms(function({position:f,...a},m){const _=new bt.Marker(f,a);return ci(_,ys(m,{overlayContainer:_}))},function(f,a,m){a.position!==m.position&&f.setLatLng(a.position),a.icon!=null&&a.icon!==m.icon&&f.setIcon(a.icon),a.zIndexOffset!=null&&a.zIndexOffset!==m.zIndexOffset&&f.setZIndexOffset(a.zIndexOffset),a.opacity!=null&&a.opacity!==m.opacity&&f.setOpacity(a.opacity),f.dragging!=null&&a.draggable!==m.draggable&&(a.draggable===!0?f.dragging.enable():f.dragging.disable())}),As=Ss(function(f,a){const m=new bt.Popup(f,a.overlayContainer);return ci(m,a)},function(f,a,{position:m},_){z.useEffect(function(){const{instance:v}=f;function G(U){U.popup===v&&(v.update(),_(!0))}function w(U){U.popup===v&&_(!1)}return a.map.on({popupopen:G,popupclose:w}),a.overlayContainer==null?(m!=null&&v.setLatLng(m),v.openOn(a.map)):a.overlayContainer.bindPopup(v),function(){var M;a.map.off({popupopen:G,popupclose:w}),(M=a.overlayContainer)==null||M.unbindPopup(),a.map.removeLayer(v)}},[f,a,_,m])}),Bs=zs(function({url:f,...a},m){const _=new bt.TileLayer(f,li(a,m));return ci(_,m)},function(f,a,m){Es(f,a,m);const{url:_}=a;_!=null&&_!==m.url&&f.setUrl(_)});delete bt.Icon.Default.prototype._getIconUrl;bt.Icon.Default.mergeOptions({iconRetinaUrl:"https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png",iconUrl:"https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png",shadowUrl:"https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png"});const Ns=d=>{const a={cultural:"#8B5CF6",nature:"#10B981",beach:"#3B82F6",city:"#F59E0B",adventure:"#EF4444"}[d]||"#6B7280";return new bt.Icon({iconUrl:`data:image/svg+xml;base64,${btoa(`
      <svg width="25" height="41" viewBox="0 0 25 41" xmlns="http://www.w3.org/2000/svg">
        <path d="M12.5 0C5.6 0 0 5.6 0 12.5C0 19.4 12.5 41 12.5 41S25 19.4 25 12.5C25 5.6 19.4 0 12.5 0Z" fill="${a}"/>
        <circle cx="12.5" cy="12.5" r="6" fill="white"/>
      </svg>
    `)}`,iconSize:[25,41],iconAnchor:[12,41],popupAnchor:[1,-34]})},Rs=({destinations:d})=>{const f=ks();return z.useEffect(()=>{if(d.length>0){const a=new bt.LatLngBounds(d.map(m=>[m.coordinates.lat,m.coordinates.lng]));f.fitBounds(a,{padding:[20,20]})}},[d,f]),null},Ds=({className:d="",height:f="600px"})=>{const[a,m]=z.useState(ht),[_,O]=z.useState("all"),[v,G]=z.useState(""),[w,U]=z.useState(!1),M=[{id:"all",name:"All Destinations",count:ht.length},{id:"cultural",name:"Cultural Sites",count:ht.filter(g=>g.category==="cultural").length},{id:"nature",name:"Nature & Wildlife",count:ht.filter(g=>g.category==="nature").length},{id:"beach",name:"Beaches",count:ht.filter(g=>g.category==="beach").length},{id:"city",name:"Cities",count:ht.filter(g=>g.category==="city").length},{id:"adventure",name:"Adventure",count:ht.filter(g=>g.category==="adventure").length}];return z.useEffect(()=>{let g=ht;_!=="all"&&(g=g.filter(j=>j.category===_)),v&&(g=g.filter(j=>j.name.toLowerCase().includes(v.toLowerCase())||j.description.toLowerCase().includes(v.toLowerCase())||j.province.toLowerCase().includes(v.toLowerCase()))),m(g)},[_,v]),l.jsxs("div",{className:`relative ${d}`,children:[l.jsxs("div",{className:"absolute top-4 left-4 right-4 z-[1000] flex flex-col sm:flex-row gap-4",children:[l.jsxs("div",{className:"relative flex-1",children:[l.jsx(zn,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400"}),l.jsx("input",{type:"text",placeholder:"Search destinations...",value:v,onChange:g=>G(g.target.value),className:"w-full pl-10 pr-4 py-2 bg-white rounded-lg shadow-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary-500"})]}),l.jsxs("button",{onClick:()=>U(!w),className:"flex items-center space-x-2 px-4 py-2 bg-white rounded-lg shadow-lg border border-gray-200 hover:bg-gray-50 transition-colors",children:[l.jsx(ms,{className:"h-5 w-5 text-gray-600"}),l.jsx("span",{className:"text-gray-700 font-medium",children:"Filters"})]})]}),l.jsx(us,{children:w&&l.jsxs(Lt.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},className:"absolute top-20 left-4 right-4 z-[1000] bg-white rounded-lg shadow-xl border border-gray-200 p-4",children:[l.jsxs("div",{className:"flex items-center justify-between mb-4",children:[l.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Filter by Category"}),l.jsx("button",{onClick:()=>U(!1),className:"p-1 hover:bg-gray-100 rounded-full transition-colors",children:l.jsx(ls,{className:"h-5 w-5 text-gray-500"})})]}),l.jsx("div",{className:"grid grid-cols-2 sm:grid-cols-3 gap-2",children:M.map(g=>l.jsxs("button",{onClick:()=>O(g.id),className:`p-3 rounded-lg text-left transition-colors ${_===g.id?"bg-primary-100 text-primary-700 border-2 border-primary-300":"bg-gray-50 text-gray-700 border-2 border-transparent hover:bg-gray-100"}`,children:[l.jsx("div",{className:"font-medium",children:g.name}),l.jsxs("div",{className:"text-sm opacity-75",children:[g.count," destinations"]})]},g.id))})]})}),l.jsx("div",{style:{height:f},className:"rounded-lg overflow-hidden shadow-lg",children:l.jsxs(Zs,{center:[7.8731,80.7718],zoom:8,style:{height:"100%",width:"100%"},className:"z-0",children:[l.jsx(Bs,{attribution:'© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',url:"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"}),l.jsx(Rs,{destinations:a}),a.map(g=>l.jsx(Is,{position:[g.coordinates.lat,g.coordinates.lng],icon:Ns(g.category),children:l.jsx(As,{maxWidth:300,className:"custom-popup",children:l.jsxs("div",{className:"p-2",children:[l.jsx("h3",{className:"text-lg font-bold text-gray-900 mb-2",children:g.name}),l.jsx("p",{className:"text-gray-600 text-sm mb-3",children:g.shortDescription}),l.jsxs("div",{className:"space-y-2 text-sm",children:[l.jsxs("div",{className:"flex items-center space-x-2",children:[l.jsx(En,{className:"h-4 w-4 text-gray-500"}),l.jsxs("span",{className:"text-gray-600",children:[g.province," Province"]})]}),l.jsxs("div",{className:"flex items-center space-x-2",children:[l.jsx("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${g.category==="cultural"?"bg-purple-100 text-purple-700":g.category==="nature"?"bg-green-100 text-green-700":g.category==="beach"?"bg-blue-100 text-blue-700":g.category==="city"?"bg-orange-100 text-orange-700":"bg-red-100 text-red-700"}`,children:g.category}),l.jsx("span",{className:"text-gray-500",children:"•"}),l.jsx("span",{className:"text-gray-600",children:g.duration})]})]}),l.jsx("div",{className:"mt-3 pt-3 border-t border-gray-200",children:l.jsx("button",{className:"w-full bg-primary-600 text-white py-2 px-4 rounded-lg hover:bg-primary-700 transition-colors text-sm font-medium",children:"View Details"})})]})})},g.id))]})}),l.jsx("div",{className:"absolute bottom-4 left-4 z-[1000] bg-white rounded-lg shadow-lg px-3 py-2 border border-gray-200",children:l.jsxs("span",{className:"text-sm text-gray-600",children:["Showing ",l.jsx("span",{className:"font-semibold text-gray-900",children:a.length})," of"," ",l.jsx("span",{className:"font-semibold text-gray-900",children:ht.length})," destinations"]})})]})},$s=()=>{const[d,f]=z.useState(""),[a,m]=z.useState("all"),[_,O]=z.useState("all"),v=_s("fadeIn",.1),G=["all","beach","cultural","nature","adventure","city"],w=["all",...Array.from(new Set(ht.map(M=>M.province)))],U=z.useMemo(()=>ht.filter(M=>{const g=M.name.toLowerCase().includes(d.toLowerCase())||M.description.toLowerCase().includes(d.toLowerCase()),j=a==="all"||M.category===a,q=_==="all"||M.province===_;return g&&j&&q}),[d,a,_]);return l.jsxs(l.Fragment,{children:[l.jsxs(cs,{children:[l.jsx("title",{children:"Destinations - Sri Lanka Tourist Guide"}),l.jsx("meta",{name:"description",content:"Explore the most beautiful destinations in Sri Lanka, from beaches to mountains to cultural sites."})]}),l.jsx("section",{className:"bg-gradient-to-br from-blue-50 to-purple-50 section-padding",children:l.jsxs("div",{className:"container-max",children:[l.jsxs(Lt.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8},className:"text-center mb-12",children:[l.jsxs("h1",{className:"text-4xl md:text-6xl font-bold text-gray-900 mb-6",children:["Discover Amazing ",l.jsx("span",{className:"text-gradient",children:"Destinations"})]}),l.jsx("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:"From ancient cities to pristine beaches, explore the diverse landscapes and rich cultural heritage that make Sri Lanka truly special."})]}),l.jsx(Lt.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.2},className:"bg-white rounded-2xl shadow-lg p-6 mb-12",children:l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[l.jsxs("div",{className:"relative",children:[l.jsx(zn,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400"}),l.jsx("input",{type:"text",placeholder:"Search destinations...",value:d,onChange:M=>f(M.target.value),className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"})]}),l.jsx("select",{value:a,onChange:M=>m(M.target.value),"aria-label":"Filter by category",className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent",children:G.map(M=>l.jsx("option",{value:M,children:M==="all"?"All Categories":M.charAt(0).toUpperCase()+M.slice(1)},M))}),l.jsx("select",{value:_,onChange:M=>O(M.target.value),"aria-label":"Filter by province",className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent",children:w.map(M=>l.jsx("option",{value:M,children:M==="all"?"All Provinces":`${M} Province`},M))})]})})]})}),l.jsx("section",{className:"section-padding bg-gray-50",children:l.jsxs("div",{className:"container-max",children:[l.jsxs(Lt.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"text-center mb-8",children:[l.jsx("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-4",children:"Explore Sri Lanka on the Map"}),l.jsx("p",{className:"text-lg text-gray-600 max-w-3xl mx-auto",children:"Discover destinations across all 9 provinces of Sri Lanka. Click on markers to learn more about each location."})]}),l.jsx(Lt.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.2},viewport:{once:!0},children:l.jsx(Ds,{height:"500px"})})]})}),l.jsx("section",{className:"section-padding bg-white",children:l.jsx("div",{className:"container-max",children:U.length===0?l.jsxs(Lt.div,{initial:{opacity:0},animate:{opacity:1},className:"text-center py-16",children:[l.jsx("div",{className:"text-6xl mb-4",children:"🔍"}),l.jsx("h3",{className:"text-2xl font-semibold text-gray-900 mb-2",children:"No destinations found"}),l.jsx("p",{className:"text-gray-600",children:"Try adjusting your search criteria"})]}):l.jsxs(l.Fragment,{children:[l.jsxs(Lt.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"flex items-center justify-between mb-8",children:[l.jsxs("h2",{className:"text-2xl font-semibold text-gray-900",children:[U.length," destination",U.length!==1?"s":""," found"]}),l.jsxs("div",{className:"text-sm text-gray-500",children:["Showing results for ",a!=="all"&&`${a} destinations`,a!=="all"&&_!=="all"&&" in ",_!=="all"&&`${_} Province`]})]}),l.jsx("div",{ref:v,className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:U.map(M=>l.jsx("div",{className:"gsap-fade-in",children:l.jsx(js,{destination:M})},M.id))})]})})})]})},js=({destination:d})=>{const f=m=>{switch(m){case"easy":return"text-green-600 bg-green-100";case"moderate":return"text-yellow-600 bg-yellow-100";case"challenging":return"text-red-600 bg-red-100";default:return"text-gray-600 bg-gray-100"}},a=m=>{switch(m){case"beach":return"🏖️";case"cultural":return"🏛️";case"nature":return"🌿";case"adventure":return"🏔️";case"city":return"🏙️";default:return"📍"}};return l.jsxs(Lt.div,{whileHover:{y:-5},transition:{duration:.3},className:"card overflow-hidden group",children:[l.jsxs("div",{className:"relative h-64 bg-gradient-to-br from-blue-400 to-purple-500 overflow-hidden",children:[l.jsx("div",{className:"absolute inset-0 bg-black bg-opacity-20 group-hover:bg-opacity-10 transition-all duration-300"}),l.jsx("div",{className:"absolute top-4 left-4",children:l.jsxs("span",{className:"inline-flex items-center px-3 py-1 bg-white bg-opacity-90 rounded-full text-sm font-medium text-gray-900",children:[l.jsx("span",{className:"mr-1",children:a(d.category)}),d.category]})}),l.jsx("div",{className:"absolute top-4 right-4",children:l.jsx("span",{className:`inline-block px-3 py-1 rounded-full text-xs font-medium ${f(d.difficulty)}`,children:d.difficulty})}),l.jsx("div",{className:"absolute bottom-4 left-4",children:l.jsxs("span",{className:"inline-flex items-center text-white text-sm",children:[l.jsx(En,{className:"h-4 w-4 mr-1"}),d.province," Province"]})})]}),l.jsxs("div",{className:"p-6",children:[l.jsx("h3",{className:"text-xl font-semibold mb-2 text-gray-900 group-hover:text-primary-600 transition-colors",children:d.name}),l.jsx("p",{className:"text-gray-600 mb-4 line-clamp-2",children:d.shortDescription}),l.jsxs("div",{className:"flex items-center justify-between text-sm text-gray-500 mb-4",children:[l.jsxs("div",{className:"flex items-center",children:[l.jsx(ps,{className:"h-4 w-4 mr-1"}),d.duration]}),l.jsxs("div",{className:"text-right",children:["Best: ",d.bestTimeToVisit]})]}),l.jsx("div",{className:"mb-4",children:l.jsxs("div",{className:"flex flex-wrap gap-1",children:[d.highlights.slice(0,2).map((m,_)=>l.jsx("span",{className:"inline-block px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded",children:m},_)),d.highlights.length>2&&l.jsxs("span",{className:"inline-block px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded",children:["+",d.highlights.length-2," more"]})]})}),l.jsx(fs,{to:`/destinations/${d.id}`,className:"block w-full text-center bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-4 rounded-lg transition-colors",children:"Explore Destination"})]})]})};export{$s as default};
