# Animation Implementation Guide

This document provides comprehensive information about the animation libraries and implementations used in the Sri Lanka Tourist Guide website.

## Animation Libraries Used

### 1. GSAP (GreenSock Animation Platform)
- **Version**: 3.13.0
- **Purpose**: Professional-grade animations, scroll triggers, and complex timeline sequences
- **License**: Free for non-commercial use

#### Key Features Implemented:
- Scroll-triggered animations
- Parallax effects
- Stagger animations
- Timeline sequences
- Performance-optimized transforms

#### Usage Examples:

```typescript
// Basic fade in animation
import { fadeIn } from '@/utils/animations'

fadeIn('.my-element', { delay: 0.2 })

// Scroll-triggered animation
import { createScrollTrigger } from '@/utils/animations'

createScrollTrigger('.trigger-element', () => 
  fadeIn('.animated-element')
)
```

### 2. Framer Motion
- **Version**: 12.18.1
- **Purpose**: React-specific animations, gestures, and layout animations
- **License**: MIT

#### Key Features Implemented:
- Component enter/exit animations
- Hover and tap interactions
- Layout animations
- Page transitions
- Gesture handling

#### Usage Examples:

```tsx
import { motion } from 'framer-motion'

// Basic motion component
<motion.div
  initial={{ opacity: 0, y: 20 }}
  animate={{ opacity: 1, y: 0 }}
  transition={{ duration: 0.5 }}
>
  Content
</motion.div>

// Hover animations
<motion.button
  whileHover={{ scale: 1.05 }}
  whileTap={{ scale: 0.95 }}
>
  Click me
</motion.button>
```

### 3. Lottie React
- **Version**: 2.4.1
- **Purpose**: Complex animations from After Effects
- **License**: MIT

## Custom Animation Components

### AnimatedSection Component
A reusable component for applying consistent animations across the site.

```tsx
<AnimatedSection 
  animation="fadeIn" 
  delay={0.2}
  trigger="viewport"
>
  <YourContent />
</AnimatedSection>
```

**Available animations:**
- `fadeIn`: Fade in with upward movement
- `slideUp`: Slide up from bottom
- `slideLeft`: Slide in from left
- `slideRight`: Slide in from right
- `scaleIn`: Scale in with fade
- `stagger`: Stagger children animations

### Custom Hooks

#### useGSAP Hooks
- `useFadeIn(delay)`: Fade in animation on mount
- `useSlideIn(direction, delay)`: Slide in animations
- `useScrollTrigger(animation, options)`: Scroll-triggered animations
- `useParallax(speed)`: Parallax effects
- `useStaggerChildren(animation, delay)`: Stagger child animations

#### usePerformance Hook
Monitors animation performance and provides metrics.

```typescript
const { metrics, performanceGrade } = usePerformance()
```

## Animation Patterns

### 1. Page Transitions
All page transitions use consistent Framer Motion variants:

```typescript
const pageVariants = {
  initial: { opacity: 0, y: 20 },
  in: { opacity: 1, y: 0 },
  out: { opacity: 0, y: -20 }
}
```

### 2. Scroll Animations
GSAP ScrollTrigger is used for scroll-based animations:

```typescript
ScrollTrigger.create({
  trigger: element,
  start: 'top 80%',
  end: 'bottom 20%',
  toggleActions: 'play none none reverse'
})
```

### 3. Stagger Animations
For animating lists and grids:

```typescript
gsap.fromTo(children, 
  { opacity: 0, y: 30 },
  { 
    opacity: 1, 
    y: 0, 
    stagger: 0.1,
    duration: 0.6 
  }
)
```

## Performance Considerations

### 1. Hardware Acceleration
All animations use transform properties for GPU acceleration:
- `transform: translateX/Y/Z`
- `transform: scale`
- `opacity`

### 2. Reduced Motion Support
Respects user's motion preferences:

```css
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
```

### 3. Performance Monitoring
- Animation performance is tracked in development
- Metrics are collected for optimization
- Bundle size is optimized with code splitting

## Best Practices

### 1. Animation Timing
- **Fast**: 0.3s for micro-interactions
- **Normal**: 0.6s for standard animations
- **Slow**: 1.2s for complex sequences

### 2. Easing Functions
- `power2.out`: Smooth, natural feeling
- `back.out(1.7)`: Bouncy, playful
- `elastic.out`: Spring-like effect

### 3. Stagger Delays
- **Fast**: 0.1s between elements
- **Normal**: 0.2s between elements
- **Slow**: 0.3s between elements

## Accessibility

### 1. Reduced Motion
All animations respect the `prefers-reduced-motion` media query.

### 2. Focus Management
Animations don't interfere with keyboard navigation and focus states.

### 3. Screen Readers
Animations are hidden from screen readers when appropriate using `aria-hidden`.

## Debugging

### 1. Development Tools
- GSAP DevTools (available in development)
- React DevTools for Framer Motion
- Performance monitoring hooks

### 2. Common Issues
- **Flickering**: Ensure proper initial states
- **Performance**: Use `will-change` sparingly
- **Layout Shift**: Avoid animating layout properties

## File Structure

```
src/
├── components/
│   ├── AnimatedSection.tsx
│   ├── ScrollProgress.tsx
│   └── FloatingActionButton.tsx
├── hooks/
│   ├── useGSAP.ts
│   └── usePerformance.ts
├── utils/
│   └── animations.ts
└── styles/
    └── index.css (animation utilities)
```

## Configuration

### GSAP Registration
```typescript
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'

gsap.registerPlugin(ScrollTrigger)
```

### Framer Motion Configuration
```typescript
// In App.tsx
const pageTransition = {
  type: "tween",
  ease: "anticipate",
  duration: 0.5
}
```

## Future Enhancements

1. **Lottie Animations**: Add complex After Effects animations
2. **3D Animations**: Implement Three.js for 3D effects
3. **WebGL**: Advanced GPU-accelerated animations
4. **Motion Path**: SVG path-based animations
5. **Physics**: Spring physics for natural motion

## Resources

- [GSAP Documentation](https://greensock.com/docs/)
- [Framer Motion Documentation](https://www.framer.com/motion/)
- [Web Animation API](https://developer.mozilla.org/en-US/docs/Web/API/Web_Animations_API)
- [CSS Animation Performance](https://web.dev/animations-guide/)

## Support

For animation-related issues or questions, refer to:
1. This documentation
2. Component source code
3. Animation utility functions
4. Performance monitoring tools
