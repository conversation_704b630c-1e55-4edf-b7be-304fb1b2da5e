import{r as c,j as e,m as i}from"./animations-Dls6NBzi.js";import{H as f}from"./index-DoUPJfA4.js";import{L as b}from"./router-DklJj9iW.js";import{d}from"./destinations-BSfjDwhr.js";import{u as j}from"./useGSAP-KRu8-WuZ.js";import{F as v}from"./MagnifyingGlassIcon-DvDOtkAW.js";import{F as N}from"./MapPinIcon-DxqRjeFE.js";import{F as w}from"./ClockIcon-Bop423fN.js";import"./vendor-BtP0CW_r.js";const R=()=>{const[t,o]=c.useState(""),[a,l]=c.useState("all"),[r,m]=c.useState("all"),x=j("fadeIn",.1),h=["all","beach","cultural","nature","adventure","city"],u=["all",...Array.from(new Set(d.map(s=>s.province)))],n=c.useMemo(()=>d.filter(s=>{const g=s.name.toLowerCase().includes(t.toLowerCase())||s.description.toLowerCase().includes(t.toLowerCase()),p=a==="all"||s.category===a,y=r==="all"||s.province===r;return g&&p&&y}),[t,a,r]);return e.jsxs(e.Fragment,{children:[e.jsxs(f,{children:[e.jsx("title",{children:"Destinations - Sri Lanka Tourist Guide"}),e.jsx("meta",{name:"description",content:"Explore the most beautiful destinations in Sri Lanka, from beaches to mountains to cultural sites."})]}),e.jsx("section",{className:"bg-gradient-to-br from-blue-50 to-purple-50 section-padding",children:e.jsxs("div",{className:"container-max",children:[e.jsxs(i.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8},className:"text-center mb-12",children:[e.jsxs("h1",{className:"text-4xl md:text-6xl font-bold text-gray-900 mb-6",children:["Discover Amazing ",e.jsx("span",{className:"text-gradient",children:"Destinations"})]}),e.jsx("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:"From ancient cities to pristine beaches, explore the diverse landscapes and rich cultural heritage that make Sri Lanka truly special."})]}),e.jsx(i.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.2},className:"bg-white rounded-2xl shadow-lg p-6 mb-12",children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs("div",{className:"relative",children:[e.jsx(v,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400"}),e.jsx("input",{type:"text",placeholder:"Search destinations...",value:t,onChange:s=>o(s.target.value),className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"})]}),e.jsx("select",{value:a,onChange:s=>l(s.target.value),"aria-label":"Filter by category",className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent",children:h.map(s=>e.jsx("option",{value:s,children:s==="all"?"All Categories":s.charAt(0).toUpperCase()+s.slice(1)},s))}),e.jsx("select",{value:r,onChange:s=>m(s.target.value),"aria-label":"Filter by province",className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent",children:u.map(s=>e.jsx("option",{value:s,children:s==="all"?"All Provinces":`${s} Province`},s))})]})})]})}),e.jsx("section",{className:"section-padding bg-white",children:e.jsx("div",{className:"container-max",children:n.length===0?e.jsxs(i.div,{initial:{opacity:0},animate:{opacity:1},className:"text-center py-16",children:[e.jsx("div",{className:"text-6xl mb-4",children:"🔍"}),e.jsx("h3",{className:"text-2xl font-semibold text-gray-900 mb-2",children:"No destinations found"}),e.jsx("p",{className:"text-gray-600",children:"Try adjusting your search criteria"})]}):e.jsxs(e.Fragment,{children:[e.jsxs(i.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"flex items-center justify-between mb-8",children:[e.jsxs("h2",{className:"text-2xl font-semibold text-gray-900",children:[n.length," destination",n.length!==1?"s":""," found"]}),e.jsxs("div",{className:"text-sm text-gray-500",children:["Showing results for ",a!=="all"&&`${a} destinations`,a!=="all"&&r!=="all"&&" in ",r!=="all"&&`${r} Province`]})]}),e.jsx("div",{ref:x,className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:n.map(s=>e.jsx("div",{className:"gsap-fade-in",children:e.jsx(C,{destination:s})},s.id))})]})})})]})},C=({destination:t})=>{const o=l=>{switch(l){case"easy":return"text-green-600 bg-green-100";case"moderate":return"text-yellow-600 bg-yellow-100";case"challenging":return"text-red-600 bg-red-100";default:return"text-gray-600 bg-gray-100"}},a=l=>{switch(l){case"beach":return"🏖️";case"cultural":return"🏛️";case"nature":return"🌿";case"adventure":return"🏔️";case"city":return"🏙️";default:return"📍"}};return e.jsxs(i.div,{whileHover:{y:-5},transition:{duration:.3},className:"card overflow-hidden group",children:[e.jsxs("div",{className:"relative h-64 bg-gradient-to-br from-blue-400 to-purple-500 overflow-hidden",children:[e.jsx("div",{className:"absolute inset-0 bg-black bg-opacity-20 group-hover:bg-opacity-10 transition-all duration-300"}),e.jsx("div",{className:"absolute top-4 left-4",children:e.jsxs("span",{className:"inline-flex items-center px-3 py-1 bg-white bg-opacity-90 rounded-full text-sm font-medium text-gray-900",children:[e.jsx("span",{className:"mr-1",children:a(t.category)}),t.category]})}),e.jsx("div",{className:"absolute top-4 right-4",children:e.jsx("span",{className:`inline-block px-3 py-1 rounded-full text-xs font-medium ${o(t.difficulty)}`,children:t.difficulty})}),e.jsx("div",{className:"absolute bottom-4 left-4",children:e.jsxs("span",{className:"inline-flex items-center text-white text-sm",children:[e.jsx(N,{className:"h-4 w-4 mr-1"}),t.province," Province"]})})]}),e.jsxs("div",{className:"p-6",children:[e.jsx("h3",{className:"text-xl font-semibold mb-2 text-gray-900 group-hover:text-primary-600 transition-colors",children:t.name}),e.jsx("p",{className:"text-gray-600 mb-4 line-clamp-2",children:t.shortDescription}),e.jsxs("div",{className:"flex items-center justify-between text-sm text-gray-500 mb-4",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(w,{className:"h-4 w-4 mr-1"}),t.duration]}),e.jsxs("div",{className:"text-right",children:["Best: ",t.bestTimeToVisit]})]}),e.jsx("div",{className:"mb-4",children:e.jsxs("div",{className:"flex flex-wrap gap-1",children:[t.highlights.slice(0,2).map((l,r)=>e.jsx("span",{className:"inline-block px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded",children:l},r)),t.highlights.length>2&&e.jsxs("span",{className:"inline-block px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded",children:["+",t.highlights.length-2," more"]})]})}),e.jsx(b,{to:`/destinations/${t.id}`,className:"block w-full text-center bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-4 rounded-lg transition-colors",children:"Explore Destination"})]})]})};export{R as default};
