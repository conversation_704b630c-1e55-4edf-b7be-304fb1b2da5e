# SplitText Walkthrough - YouTube

Hey there. Today we're going to be
taking a look at Split Text, one of the
most popular guit plugins. And now with
the latest announcement, a plugin that's
free for everyone to use. Split text
lets us split up an HTML element into
lines, words, or characters. Basically
wrapping up each bit in its own element.
Then we can whiz those bits around and
create all sorts of gorgeous staggered
animations and effects. Split text
recently got a complete rewrite,
including sensible defaults for screen
reader accessibility, easy masking for
reveal effects, responsive line
splitting, and much more. Split text can
also be used independently of Gap, just
in case you're only after the functional
text splitting and don't need animation.
Let's take a little look together. All
right, so let's take a look at creating
a simple split text animation first,
then we can dig a bit deeper into the
features. So, first up, we're going to
register the split text plugin. Then,
Gap's core knows that we want to use it.
We'll create a new split text instance.
And if we feed in our element here,
split text will split all of this text
up into
pieces. So, the default is lines, words,
and characters. If we open up the DOM
and take a little look, you can see that
everything here is all split up into
pieces. We've got lines here and then
we've got words and
characters. So if we want to customize
this, say you want to just split by
words or by characters only, we can pass
in a config
object. So we can define type and then
type lets us define which elements we
want to split up. So let's just split by
words for now. It's good for performance
as well to just split what you need.
More elements means more work for the
browser. So we'll add a little gap tween
here and then we'll grab those words. So
you can access your characters and words
and lines on the split text instance
itself which we called split. And then
we'll do a little gap between tween to
stagger them in. Lovely. So we can
access lines and characters in the same
way by accessing the properties where
the array of elements live. So the type
property accepts a commaepparated
string. So let's pass in the other
values. And then we'll swap this little
bit out here so you can see the
different
animations. Great stuff. Another way of
accessing all these little bits and bobs
inside your split text is by giving them
a class and then targeting
that. It's also a nice way to add a hook
for some styling. So, let's give the
words a class. And you can see we've got
a little border now. Brilliant. So, I
added some classes here so you can see
the splits without us having to look in
the DOM every time. So, we can do the
same thing. um add a class to our lines
using lines class and then add a class
to our characters using cars class char
class. You can also increment your
classes by adding a little plus+ to the
string here. So if we look in the DOM,
we can see that all of the classes that
we added are now numbered. I've added a
little bit of CSS here to just target
specific words. So you can see that
these words have a little border around
them. You can also add prop index true
which rather than a class adds a CSS
variable. If we look in the DOM here,
you can see the CSS variables here and
here. So easy selecting means it's nice
and simple to set up effects like this
that target specific words. So, this
isn't part of the split text plugin, but
while we have this demo ready to go, and
seeing as everyone loves a stagger,
let's just cover some of the fun stagger
options available to you. So, right now,
we've got a very basic stagger. We're
just saying stagger the characters with
0.05 seconds in between each animation.
We can write that like this instead
using the config object, which then
gives us space for more options. Maybe
we don't want to set time in between
each animation. Maybe we want the entire
stagger to last 0.5 seconds. Instead, we
can also define where we want the
stagger to start from. So, right now,
it's starting from the beginning, but we
can say end instead or maybe center or
my favorite
random. If you put a repeat in the tween
itself, the entire tween will play. Then
it will repeat when the animations have
all finished. Or you can put this repeat
and the yo-yo inside the stagger. And
each individual animation will repeat
when it's
finished. Another really fun thing to do
with stagger animations is to apply some
randomness to the values themselves. So
we can do this really easily with Gap's
random utility. Let's animate the
letters from either the top or the
bottom. Note that this has got an array
with two values in. So it's going to
pick either one of these values. We can
also add a little rotation. So we're
going to use random again, but this time
we're not passing an array. So this
means any value between minus30 and 30.
Now we'll add a little bouncies for fun.
And there we go. See how easy it is to
make really fun effects. You can check
out more about staggers in the Gap docs.
Another thing that we have to think
about when we are animating fonts is
font loading. Custom fonts sometimes
take a little bit longer to load in than
the default fonts. So, it's common for
people to end up with weirdly formatted
splits because the split text divides up
the text before the custom
font loads in. So, the measurements are a little bit off.
So if we pop the console here, we can
see that split text now informs us when
the text was split before the custom
font loaded in. So we get a little bit
of a warning. So we can make sure that
we're nice and safe by using
document.fonts, popping our splits and
our tween in here. So this is a promise
that fulfills when all of the fonts are
done loading. So if we check out our
console, we don't have the error
anymore. Perfect. There's also a nice
shiny new call back in 3.13 that ensures
that your tween's created when the text
splits and not before. So this callback
is nice and self-explanatory. It fires
when the text is split. So rather than
your tween being separate like this, we
can grab it and then we can tuck it up
inside the onsplit callback. This is
also super useful for responsive line
splitting. We'll take a look at that in
a bit. Split Text is also much smarter
at splitting than a lot of other texts
splitting libraries. In this demo, we're
loading in Split Text and also an
alternative text splitting library that
we've seen a lot of people using back
when split text was a paid plug-in. So,
now that Split Text is free, maybe this
will entice you to swap over. We're just
going to split by characters and words
for now. So, if I split with split text
and then revert the split, you can see
that the text doesn't move at all.
Perfect. That's exactly what we want.
Whereas some other text splitting
libraries, um, nested tags like this
strong tag here kind of shift
about. So the new rewrite of split text
also handles special characters far
better. If we split by characters and
use this third party library, you'll see
these weird little characters pop up.
This is because there are certain
characters and emojis that we see
visually as one character, but they're
actually graphine clusters, which is a
cool name. Basically, under the hood,
they're multiple code points that are
smooshed together by the browser into
one visual character or emoji. So, when
we split them up, the browser just gets
a bit confused. Split text solves for
most of these use cases internally. So,
if we split using split text, you can
see that we've just got the text as it
should display. Whites space is also
handled a lot better in the new version,
which is especially nice when you're
working with pre-tags. Working on the
web means thinking about how things work
on different screen sizes and if the
browser resizes
too. So, if we split by words here, you
can see that we get responsive behavior
out of the box. The words just nicely
wrap onto new lines as we resize. So
that's the easy one. But this is the
behavior that we want when we split by
characters and lines
too. But if we split by just characters,
we can end up with some funny character
by character refflows. See how the words
are being split up here? I mean, this
makes sense. We've broken up all of the
characters into their own divs. So the
browser doesn't really understand that
they're words and need to be grouped
together. So there's an easy fix for
this. You just make sure that you group
by characters and words. But in the new
split text, we've also added smart wrap,
which groups your words together, even
if you only split by characters. So,
that's solved. But that's the little
one. It's responsive line splitting.
That's the real headache. Or at least it
was in the past. If you've run into this
before, you know what I'm talking about.
So, let's take a little look at the
problem space in this
demo. Again, if we break up the text by
words, the text just refflows on resize.
Lovely. But when we break up text by
line and then resize,
uh-oh, we start getting all these weird
line breaks. So, why is this happening?
Well, if we toggle a border onto the
lines and the words, you'll be able to
see the issue. So you have the line
container here and when we squish it up,
there's no longer space for all of the
words to be on one line. So the word
divs are breaking onto a new line, but
they're constrained within the parent,
which is awkward. Similarly, if we split
it a small size and then we make the
screen larger, the boundaries of our
lines have already been defined, so the
text doesn't reflow up into the larger
space.
Considering that websites need to be
responsive, this is less than ideal. But
happily for all of us, there's a new
feature in split text to solve this.
This new feature is called autosplit. So
let's enable this. Now that I have
autosplit set to true. You'll see that
when we resize, everything refflows and
re-splits onto new lines. I'll do it a
little slower. This here is the last
word on the top row here. And then we
make the screen smaller. And you can see
it's now the first word on the second
row. So what's happening under the hood
is that split text is listening to a
resize observer on the element itself.
And then if the element changes width
and it's split by lines, it will trigger
a new split. Perfect. So what if we want
to animate this? Let's add a little
animation. So on the first glance, this
is perfect. So the text splits and then
the animation grabs those elements and
animates them.
But if this text re-splits, this
animation is now targeting the old lines
that don't exist anymore. So now our
animation isn't working. So the solution
to this is to ensure that we always pair
autosplit with the onsplit call back and
then pop our animations inside. Let's
take a little look. So if we add this
onsplit call back with a little console
log inside and then just resize, you can
see that this fires every time the text
splits. It's debounced for performance
too naturally. So to animate safely, all
we have to do is pop our animation
inside here and then reference it like
this, ensuring that we are returning our
animation too. So let's take a look at
this animation now. Perfect. Responsive
line splitting. So why is it important
that we return this animation? Well,
when the text re-splits, there might be
more lines or less lines. And if we
create an animation every time it
re-splits, we'd risk creating loads and
loads of twins and having overlapping
animations. So under the hood, split
text is doing this. First checking if
there's an existing animation, then
saving the progress, killing the old
animation and inline animation styles
with revert, then creating a new one
with the original progress value intact
so that the animation looks seamless.
But you don't have to worry about this.
All you have to do is pop a return right
there and then we handle it all for you.
A little note that you can also manually
call split or revert anytime you want.
So if you have niche behavior that for
some reason autosplit isn't set up for,
you can roll your own solution. So
here's a fun little feature, masking.
It's very common for folks to want to do
animated reveal effects. So we've added
masks to make your life a little bit
easier. just add masks into the config
object and then you can define either
character or words or lines one at a
time. So let's add a line mask. We've
got a class being added to the lines too
here. So you'll see in the DOM, if we
open this up, our masks also have a
class name with a mask suffix um at the
end of it. So now we can animate our
lines with a lovely mask reveal. Really
nice and simple. When splitting text,
you really have to be careful about
blind or partially cighted users who
might use a screen reader to help
navigate your website. So screen readers
analyze the content of a site and then
convert that into speech. Let's have a
little listen to this heading through a
screen reader. Hello, I am a heading.
Okay, so that makes sense. But what if
we split it up into characters? H E L L
O I N H E D I N G. Yesesh, not ideal.
Imagine a whole paragraph getting read
out loud to you like that. So to help
with this issue, split text adds an area
label onto the parent element and then
hides all of the children nested inside
it with area hidden. With that approach,
let's listen to the text again.
Hello, I am a heading.
Much better. So this default solution
works for the majority of use cases, but
because the text that's surfaced to the
screen readers here is just a string,
there's not really any way for them to
know about nested functionality like
links or semantic tags. If you have a
bit of text with links inside, for
instance, we recommend duplicating your
text element and setting up your styles
so that one element is visible to screen
readers and the other element is visible
to cited users.
So the screen reader accessible element
isn't split and is hidden visually using
a screen reader only class. And then on
our split element over here, we're
adding area hidden inside our split text
config to hide all of the split text. So
now if we listen to this with a screen
reader, you can hear that it announces
all of the text and the link. This text
has a link nested link. So we have
created a duplicate screen reader only
element to preserve the semantics of
child elements for screen readers. So
this approach takes a little setup but
we've linked to it in the gap docs and
in the description. A little note to use
this route sparingly. Duplicating
elements and text splitting can mean a
lot of elements which can bloat the DOM
and have knock-on performance impacts.
If you're creating a super jazzy text
animation that's more decorative than
functional, I recommend using area none
to avoid all automatic area, then you
can pop roll equals image on your
element along with an area label that
describes the animation rather than
reads out the contents. Kind of like
treating your animation like a GIF or a
video rather than some text. For extra
accessibility points and also to
optimize for performance, we recommend
reverting the split text back to the
original HTML after animation. So you
can do this by calling revert on your
split instance. If we do that in this
onmplete, when this animation finishes,
then your text reverts back to the
original HTML and it's no longer split
up. Split text also works perfectly with
match media. So, if you want to handle
reduced motion animations, that's super
nice and easy, too. Check out the match
media video or docs for more info. Even
if you have really unique text splitting
needs, I reckon we've got something for
you. Let's take a look at a few
different options. So, here we've got a
few sub elements uh styled so that you
can see the split clearly, but when we
split, you can see that the sub elements
split into their own word. Maybe that's
what you want, but you have a choice to
ignore it if you want. So, we'll tell
split text to ignore these elements.
This could be any element, too. Up to
you. And now we can see that they're all
grouped in nicely with the word that
they belong with. If you have an even
more complicated use case where you
actually want to get inside the gubbins
while split text is doing its thing, you
have prepare text. So prepare text is a
function that gets called out for each
chunk of text as split text is iterating
over it. So when it finds a bit of text
to split, it passes it to this function
and then you can work your magic on it.
So here we're using a browser API to
split up the Chinese text into words.
Chinese text doesn't usually have visual
breaks in between words, but here we're
iterating through the segments and then
adding a zero width space. Then we're
using word delimiter after all this is
done to handle all this for us. So
there's a little reax pattern to find
those breaks and then replace them with
visual spaces. And we've got a little
animation. Perfect. And that's a lot
pals. That's all I have for you. I will
catch you soon. And if you get stuck,
you know where we are. Bring a minimal
demo.
[Music]