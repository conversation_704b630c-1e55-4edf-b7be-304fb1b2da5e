import{r as Oo,g as cl}from"./vendor-BtP0CW_r.js";function hl(i,t){for(var e=0;e<t.length;e++){const n=t[e];if(typeof n!="string"&&!Array.isArray(n)){for(const s in n)if(s!=="default"&&!(s in i)){const r=Object.getOwnPropertyDescriptor(n,s);r&&Object.defineProperty(i,s,r.get?r:{enumerable:!0,get:()=>n[s]})}}}return Object.freeze(Object.defineProperty(i,Symbol.toStringTag,{value:"Module"}))}var Qi={exports:{}},Ue={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ir;function fl(){if(ir)return Ue;ir=1;var i=Oo(),t=Symbol.for("react.element"),e=Symbol.for("react.fragment"),n=Object.prototype.hasOwnProperty,s=i.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,r={key:!0,ref:!0,__self:!0,__source:!0};function o(a,u,l){var c,h={},f=null,d=null;l!==void 0&&(f=""+l),u.key!==void 0&&(f=""+u.key),u.ref!==void 0&&(d=u.ref);for(c in u)n.call(u,c)&&!r.hasOwnProperty(c)&&(h[c]=u[c]);if(a&&a.defaultProps)for(c in u=a.defaultProps,u)h[c]===void 0&&(h[c]=u[c]);return{$$typeof:t,type:a,key:f,ref:d,props:h,_owner:s.current}}return Ue.Fragment=e,Ue.jsx=o,Ue.jsxs=o,Ue}var nr;function dl(){return nr||(nr=1,Qi.exports=fl()),Qi.exports}var Bt=dl(),A=Oo();const pl=cl(A),mm=hl({__proto__:null,default:pl},[A]),is=A.createContext({});function ns(i){const t=A.useRef(null);return t.current===null&&(t.current=i()),t.current}const ss=typeof window<"u",Lo=ss?A.useLayoutEffect:A.useEffect,$i=A.createContext(null);function rs(i,t){i.indexOf(t)===-1&&i.push(t)}function os(i,t){const e=i.indexOf(t);e>-1&&i.splice(e,1)}const It=(i,t,e)=>e>t?t:e<i?i:e;let as=()=>{};const jt={},Fo=i=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(i);function Bo(i){return typeof i=="object"&&i!==null}const Io=i=>/^0[^.\s]+$/u.test(i);function us(i){let t;return()=>(t===void 0&&(t=i()),t)}const wt=i=>i,ml=(i,t)=>e=>t(i(e)),gi=(...i)=>i.reduce(ml),ii=(i,t,e)=>{const n=t-i;return n===0?1:(e-i)/n};class ls{constructor(){this.subscriptions=[]}add(t){return rs(this.subscriptions,t),()=>os(this.subscriptions,t)}notify(t,e,n){const s=this.subscriptions.length;if(s)if(s===1)this.subscriptions[0](t,e,n);else for(let r=0;r<s;r++){const o=this.subscriptions[r];o&&o(t,e,n)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const Dt=i=>i*1e3,Vt=i=>i/1e3;function jo(i,t){return t?i*(1e3/t):0}const No=(i,t,e)=>(((1-3*e+3*t)*i+(3*e-6*t))*i+3*t)*i,gl=1e-7,yl=12;function _l(i,t,e,n,s){let r,o,a=0;do o=t+(e-t)/2,r=No(o,n,s)-i,r>0?e=o:t=o;while(Math.abs(r)>gl&&++a<yl);return o}function yi(i,t,e,n){if(i===t&&e===n)return wt;const s=r=>_l(r,0,1,i,e);return r=>r===0||r===1?r:No(s(r),t,n)}const zo=i=>t=>t<=.5?i(2*t)/2:(2-i(2*(1-t)))/2,Uo=i=>t=>1-i(1-t),Wo=yi(.33,1.53,.69,.99),cs=Uo(Wo),Ko=zo(cs),$o=i=>(i*=2)<1?.5*cs(i):.5*(2-Math.pow(2,-10*(i-1))),hs=i=>1-Math.sin(Math.acos(i)),Yo=Uo(hs),Go=zo(hs),vl=yi(.42,0,1,1),xl=yi(0,0,.58,1),Xo=yi(.42,0,.58,1),Tl=i=>Array.isArray(i)&&typeof i[0]!="number",qo=i=>Array.isArray(i)&&typeof i[0]=="number",bl={linear:wt,easeIn:vl,easeInOut:Xo,easeOut:xl,circIn:hs,circInOut:Go,circOut:Yo,backIn:cs,backInOut:Ko,backOut:Wo,anticipate:$o},Pl=i=>typeof i=="string",sr=i=>{if(qo(i)){as(i.length===4);const[t,e,n,s]=i;return yi(t,e,n,s)}else if(Pl(i))return bl[i];return i},Ti=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],rr={value:null};function wl(i,t){let e=new Set,n=new Set,s=!1,r=!1;const o=new WeakSet;let a={delta:0,timestamp:0,isProcessing:!1},u=0;function l(h){o.has(h)&&(c.schedule(h),i()),u++,h(a)}const c={schedule:(h,f=!1,d=!1)=>{const p=d&&s?e:n;return f&&o.add(h),p.has(h)||p.add(h),h},cancel:h=>{n.delete(h),o.delete(h)},process:h=>{if(a=h,s){r=!0;return}s=!0,[e,n]=[n,e],e.forEach(l),t&&rr.value&&rr.value.frameloop[t].push(u),u=0,e.clear(),s=!1,r&&(r=!1,c.process(h))}};return c}const Sl=40;function Ho(i,t){let e=!1,n=!0;const s={delta:0,timestamp:0,isProcessing:!1},r=()=>e=!0,o=Ti.reduce((v,T)=>(v[T]=wl(r,t?T:void 0),v),{}),{setup:a,read:u,resolveKeyframes:l,preUpdate:c,update:h,preRender:f,render:d,postRender:m}=o,p=()=>{const v=jt.useManualTiming?s.timestamp:performance.now();e=!1,jt.useManualTiming||(s.delta=n?1e3/60:Math.max(Math.min(v-s.timestamp,Sl),1)),s.timestamp=v,s.isProcessing=!0,a.process(s),u.process(s),l.process(s),c.process(s),h.process(s),f.process(s),d.process(s),m.process(s),s.isProcessing=!1,e&&t&&(n=!1,i(p))},g=()=>{e=!0,n=!0,s.isProcessing||i(p)};return{schedule:Ti.reduce((v,T)=>{const _=o[T];return v[T]=(b,S=!1,P=!1)=>(e||g(),_.schedule(b,S,P)),v},{}),cancel:v=>{for(let T=0;T<Ti.length;T++)o[Ti[T]].cancel(v)},state:s,steps:o}}const{schedule:N,cancel:qt,state:J,steps:Ji}=Ho(typeof requestAnimationFrame<"u"?requestAnimationFrame:wt,!0);let Ai;function Al(){Ai=void 0}const ot={now:()=>(Ai===void 0&&ot.set(J.isProcessing||jt.useManualTiming?J.timestamp:performance.now()),Ai),set:i=>{Ai=i,queueMicrotask(Al)}},Zo=i=>t=>typeof t=="string"&&t.startsWith(i),fs=Zo("--"),Cl=Zo("var(--"),ds=i=>Cl(i)?Ml.test(i.split("/*")[0].trim()):!1,Ml=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,Ie={test:i=>typeof i=="number",parse:parseFloat,transform:i=>i},ni={...Ie,transform:i=>It(0,1,i)},bi={...Ie,default:1},Xe=i=>Math.round(i*1e5)/1e5,ps=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;function Dl(i){return i==null}const Vl=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,ms=(i,t)=>e=>!!(typeof e=="string"&&Vl.test(e)&&e.startsWith(i)||t&&!Dl(e)&&Object.prototype.hasOwnProperty.call(e,t)),Qo=(i,t,e)=>n=>{if(typeof n!="string")return n;const[s,r,o,a]=n.match(ps);return{[i]:parseFloat(s),[t]:parseFloat(r),[e]:parseFloat(o),alpha:a!==void 0?parseFloat(a):1}},Rl=i=>It(0,255,i),tn={...Ie,transform:i=>Math.round(Rl(i))},ue={test:ms("rgb","red"),parse:Qo("red","green","blue"),transform:({red:i,green:t,blue:e,alpha:n=1})=>"rgba("+tn.transform(i)+", "+tn.transform(t)+", "+tn.transform(e)+", "+Xe(ni.transform(n))+")"};function El(i){let t="",e="",n="",s="";return i.length>5?(t=i.substring(1,3),e=i.substring(3,5),n=i.substring(5,7),s=i.substring(7,9)):(t=i.substring(1,2),e=i.substring(2,3),n=i.substring(3,4),s=i.substring(4,5),t+=t,e+=e,n+=n,s+=s),{red:parseInt(t,16),green:parseInt(e,16),blue:parseInt(n,16),alpha:s?parseInt(s,16)/255:1}}const Tn={test:ms("#"),parse:El,transform:ue.transform},_i=i=>({test:t=>typeof t=="string"&&t.endsWith(i)&&t.split(" ").length===1,parse:parseFloat,transform:t=>`${t}${i}`}),Wt=_i("deg"),Rt=_i("%"),C=_i("px"),kl=_i("vh"),Ol=_i("vw"),or={...Rt,parse:i=>Rt.parse(i)/100,transform:i=>Rt.transform(i*100)},xe={test:ms("hsl","hue"),parse:Qo("hue","saturation","lightness"),transform:({hue:i,saturation:t,lightness:e,alpha:n=1})=>"hsla("+Math.round(i)+", "+Rt.transform(Xe(t))+", "+Rt.transform(Xe(e))+", "+Xe(ni.transform(n))+")"},X={test:i=>ue.test(i)||Tn.test(i)||xe.test(i),parse:i=>ue.test(i)?ue.parse(i):xe.test(i)?xe.parse(i):Tn.parse(i),transform:i=>typeof i=="string"?i:i.hasOwnProperty("red")?ue.transform(i):xe.transform(i),getAnimatableNone:i=>{const t=X.parse(i);return t.alpha=0,X.transform(t)}},Ll=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;function Fl(i){var t,e;return isNaN(i)&&typeof i=="string"&&(((t=i.match(ps))==null?void 0:t.length)||0)+(((e=i.match(Ll))==null?void 0:e.length)||0)>0}const Jo="number",ta="color",Bl="var",Il="var(",ar="${}",jl=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function si(i){const t=i.toString(),e=[],n={color:[],number:[],var:[]},s=[];let r=0;const a=t.replace(jl,u=>(X.test(u)?(n.color.push(r),s.push(ta),e.push(X.parse(u))):u.startsWith(Il)?(n.var.push(r),s.push(Bl),e.push(u)):(n.number.push(r),s.push(Jo),e.push(parseFloat(u))),++r,ar)).split(ar);return{values:e,split:a,indexes:n,types:s}}function ea(i){return si(i).values}function ia(i){const{split:t,types:e}=si(i),n=t.length;return s=>{let r="";for(let o=0;o<n;o++)if(r+=t[o],s[o]!==void 0){const a=e[o];a===Jo?r+=Xe(s[o]):a===ta?r+=X.transform(s[o]):r+=s[o]}return r}}const Nl=i=>typeof i=="number"?0:X.test(i)?X.getAnimatableNone(i):i;function zl(i){const t=ea(i);return ia(i)(t.map(Nl))}const Ht={test:Fl,parse:ea,createTransformer:ia,getAnimatableNone:zl};function en(i,t,e){return e<0&&(e+=1),e>1&&(e-=1),e<1/6?i+(t-i)*6*e:e<1/2?t:e<2/3?i+(t-i)*(2/3-e)*6:i}function Ul({hue:i,saturation:t,lightness:e,alpha:n}){i/=360,t/=100,e/=100;let s=0,r=0,o=0;if(!t)s=r=o=e;else{const a=e<.5?e*(1+t):e+t-e*t,u=2*e-a;s=en(u,a,i+1/3),r=en(u,a,i),o=en(u,a,i-1/3)}return{red:Math.round(s*255),green:Math.round(r*255),blue:Math.round(o*255),alpha:n}}function ki(i,t){return e=>e>0?t:i}const I=(i,t,e)=>i+(t-i)*e,nn=(i,t,e)=>{const n=i*i,s=e*(t*t-n)+n;return s<0?0:Math.sqrt(s)},Wl=[Tn,ue,xe],Kl=i=>Wl.find(t=>t.test(i));function ur(i){const t=Kl(i);if(!t)return!1;let e=t.parse(i);return t===xe&&(e=Ul(e)),e}const lr=(i,t)=>{const e=ur(i),n=ur(t);if(!e||!n)return ki(i,t);const s={...e};return r=>(s.red=nn(e.red,n.red,r),s.green=nn(e.green,n.green,r),s.blue=nn(e.blue,n.blue,r),s.alpha=I(e.alpha,n.alpha,r),ue.transform(s))},bn=new Set(["none","hidden"]);function $l(i,t){return bn.has(i)?e=>e<=0?i:t:e=>e>=1?t:i}function Yl(i,t){return e=>I(i,t,e)}function gs(i){return typeof i=="number"?Yl:typeof i=="string"?ds(i)?ki:X.test(i)?lr:ql:Array.isArray(i)?na:typeof i=="object"?X.test(i)?lr:Gl:ki}function na(i,t){const e=[...i],n=e.length,s=i.map((r,o)=>gs(r)(r,t[o]));return r=>{for(let o=0;o<n;o++)e[o]=s[o](r);return e}}function Gl(i,t){const e={...i,...t},n={};for(const s in e)i[s]!==void 0&&t[s]!==void 0&&(n[s]=gs(i[s])(i[s],t[s]));return s=>{for(const r in n)e[r]=n[r](s);return e}}function Xl(i,t){const e=[],n={color:0,var:0,number:0};for(let s=0;s<t.values.length;s++){const r=t.types[s],o=i.indexes[r][n[r]],a=i.values[o]??0;e[s]=a,n[r]++}return e}const ql=(i,t)=>{const e=Ht.createTransformer(t),n=si(i),s=si(t);return n.indexes.var.length===s.indexes.var.length&&n.indexes.color.length===s.indexes.color.length&&n.indexes.number.length>=s.indexes.number.length?bn.has(i)&&!s.values.length||bn.has(t)&&!n.values.length?$l(i,t):gi(na(Xl(n,s),s.values),e):ki(i,t)};function sa(i,t,e){return typeof i=="number"&&typeof t=="number"&&typeof e=="number"?I(i,t,e):gs(i)(i,t)}const Hl=i=>{const t=({timestamp:e})=>i(e);return{start:(e=!0)=>N.update(t,e),stop:()=>qt(t),now:()=>J.isProcessing?J.timestamp:ot.now()}},ra=(i,t,e=10)=>{let n="";const s=Math.max(Math.round(t/e),2);for(let r=0;r<s;r++)n+=Math.round(i(r/(s-1))*1e4)/1e4+", ";return`linear(${n.substring(0,n.length-2)})`},Oi=2e4;function ys(i){let t=0;const e=50;let n=i.next(t);for(;!n.done&&t<Oi;)t+=e,n=i.next(t);return t>=Oi?1/0:t}function Zl(i,t=100,e){const n=e({...i,keyframes:[0,t]}),s=Math.min(ys(n),Oi);return{type:"keyframes",ease:r=>n.next(s*r).value/t,duration:Vt(s)}}const Ql=5;function oa(i,t,e){const n=Math.max(t-Ql,0);return jo(e-i(n),t-n)}const z={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1},sn=.001;function Jl({duration:i=z.duration,bounce:t=z.bounce,velocity:e=z.velocity,mass:n=z.mass}){let s,r,o=1-t;o=It(z.minDamping,z.maxDamping,o),i=It(z.minDuration,z.maxDuration,Vt(i)),o<1?(s=l=>{const c=l*o,h=c*i,f=c-e,d=Pn(l,o),m=Math.exp(-h);return sn-f/d*m},r=l=>{const h=l*o*i,f=h*e+e,d=Math.pow(o,2)*Math.pow(l,2)*i,m=Math.exp(-h),p=Pn(Math.pow(l,2),o);return(-s(l)+sn>0?-1:1)*((f-d)*m)/p}):(s=l=>{const c=Math.exp(-l*i),h=(l-e)*i+1;return-sn+c*h},r=l=>{const c=Math.exp(-l*i),h=(e-l)*(i*i);return c*h});const a=5/i,u=ec(s,r,a);if(i=Dt(i),isNaN(u))return{stiffness:z.stiffness,damping:z.damping,duration:i};{const l=Math.pow(u,2)*n;return{stiffness:l,damping:o*2*Math.sqrt(n*l),duration:i}}}const tc=12;function ec(i,t,e){let n=e;for(let s=1;s<tc;s++)n=n-i(n)/t(n);return n}function Pn(i,t){return i*Math.sqrt(1-t*t)}const ic=["duration","bounce"],nc=["stiffness","damping","mass"];function cr(i,t){return t.some(e=>i[e]!==void 0)}function sc(i){let t={velocity:z.velocity,stiffness:z.stiffness,damping:z.damping,mass:z.mass,isResolvedFromDuration:!1,...i};if(!cr(i,nc)&&cr(i,ic))if(i.visualDuration){const e=i.visualDuration,n=2*Math.PI/(e*1.2),s=n*n,r=2*It(.05,1,1-(i.bounce||0))*Math.sqrt(s);t={...t,mass:z.mass,stiffness:s,damping:r}}else{const e=Jl(i);t={...t,...e,mass:z.mass},t.isResolvedFromDuration=!0}return t}function Li(i=z.visualDuration,t=z.bounce){const e=typeof i!="object"?{visualDuration:i,keyframes:[0,1],bounce:t}:i;let{restSpeed:n,restDelta:s}=e;const r=e.keyframes[0],o=e.keyframes[e.keyframes.length-1],a={done:!1,value:r},{stiffness:u,damping:l,mass:c,duration:h,velocity:f,isResolvedFromDuration:d}=sc({...e,velocity:-Vt(e.velocity||0)}),m=f||0,p=l/(2*Math.sqrt(u*c)),g=o-r,y=Vt(Math.sqrt(u/c)),x=Math.abs(g)<5;n||(n=x?z.restSpeed.granular:z.restSpeed.default),s||(s=x?z.restDelta.granular:z.restDelta.default);let v;if(p<1){const _=Pn(y,p);v=b=>{const S=Math.exp(-p*y*b);return o-S*((m+p*y*g)/_*Math.sin(_*b)+g*Math.cos(_*b))}}else if(p===1)v=_=>o-Math.exp(-y*_)*(g+(m+y*g)*_);else{const _=y*Math.sqrt(p*p-1);v=b=>{const S=Math.exp(-p*y*b),P=Math.min(_*b,300);return o-S*((m+p*y*g)*Math.sinh(P)+_*g*Math.cosh(P))/_}}const T={calculatedDuration:d&&h||null,next:_=>{const b=v(_);if(d)a.done=_>=h;else{let S=_===0?m:0;p<1&&(S=_===0?Dt(m):oa(v,_,b));const P=Math.abs(S)<=n,w=Math.abs(o-b)<=s;a.done=P&&w}return a.value=a.done?o:b,a},toString:()=>{const _=Math.min(ys(T),Oi),b=ra(S=>T.next(_*S).value,_,30);return _+"ms "+b},toTransition:()=>{}};return T}Li.applyToOptions=i=>{const t=Zl(i,100,Li);return i.ease=t.ease,i.duration=Dt(t.duration),i.type="keyframes",i};function wn({keyframes:i,velocity:t=0,power:e=.8,timeConstant:n=325,bounceDamping:s=10,bounceStiffness:r=500,modifyTarget:o,min:a,max:u,restDelta:l=.5,restSpeed:c}){const h=i[0],f={done:!1,value:h},d=P=>a!==void 0&&P<a||u!==void 0&&P>u,m=P=>a===void 0?u:u===void 0||Math.abs(a-P)<Math.abs(u-P)?a:u;let p=e*t;const g=h+p,y=o===void 0?g:o(g);y!==g&&(p=y-h);const x=P=>-p*Math.exp(-P/n),v=P=>y+x(P),T=P=>{const w=x(P),M=v(P);f.done=Math.abs(w)<=l,f.value=f.done?y:M};let _,b;const S=P=>{d(f.value)&&(_=P,b=Li({keyframes:[f.value,m(f.value)],velocity:oa(v,P,f.value),damping:s,stiffness:r,restDelta:l,restSpeed:c}))};return S(0),{calculatedDuration:null,next:P=>{let w=!1;return!b&&_===void 0&&(w=!0,T(P),S(P)),_!==void 0&&P>=_?b.next(P-_):(!w&&T(P),f)}}}function rc(i,t,e){const n=[],s=e||jt.mix||sa,r=i.length-1;for(let o=0;o<r;o++){let a=s(i[o],i[o+1]);if(t){const u=Array.isArray(t)?t[o]||wt:t;a=gi(u,a)}n.push(a)}return n}function oc(i,t,{clamp:e=!0,ease:n,mixer:s}={}){const r=i.length;if(as(r===t.length),r===1)return()=>t[0];if(r===2&&t[0]===t[1])return()=>t[1];const o=i[0]===i[1];i[0]>i[r-1]&&(i=[...i].reverse(),t=[...t].reverse());const a=rc(t,n,s),u=a.length,l=c=>{if(o&&c<i[0])return t[0];let h=0;if(u>1)for(;h<i.length-2&&!(c<i[h+1]);h++);const f=ii(i[h],i[h+1],c);return a[h](f)};return e?c=>l(It(i[0],i[r-1],c)):l}function ac(i,t){const e=i[i.length-1];for(let n=1;n<=t;n++){const s=ii(0,t,n);i.push(I(e,1,s))}}function uc(i){const t=[0];return ac(t,i.length-1),t}function lc(i,t){return i.map(e=>e*t)}function cc(i,t){return i.map(()=>t||Xo).splice(0,i.length-1)}function qe({duration:i=300,keyframes:t,times:e,ease:n="easeInOut"}){const s=Tl(n)?n.map(sr):sr(n),r={done:!1,value:t[0]},o=lc(e&&e.length===t.length?e:uc(t),i),a=oc(o,t,{ease:Array.isArray(s)?s:cc(t,s)});return{calculatedDuration:i,next:u=>(r.value=a(u),r.done=u>=i,r)}}const hc=i=>i!==null;function _s(i,{repeat:t,repeatType:e="loop"},n,s=1){const r=i.filter(hc),a=s<0||t&&e!=="loop"&&t%2===1?0:r.length-1;return!a||n===void 0?r[a]:n}const fc={decay:wn,inertia:wn,tween:qe,keyframes:qe,spring:Li};function aa(i){typeof i.type=="string"&&(i.type=fc[i.type])}class vs{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}const dc=i=>i/100;class xs extends vs{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{var n,s;const{motionValue:e}=this.options;e&&e.updatedAt!==ot.now()&&this.tick(ot.now()),this.isStopped=!0,this.state!=="idle"&&(this.teardown(),(s=(n=this.options).onStop)==null||s.call(n))},this.options=t,this.initAnimation(),this.play(),t.autoplay===!1&&this.pause()}initAnimation(){const{options:t}=this;aa(t);const{type:e=qe,repeat:n=0,repeatDelay:s=0,repeatType:r,velocity:o=0}=t;let{keyframes:a}=t;const u=e||qe;u!==qe&&typeof a[0]!="number"&&(this.mixKeyframes=gi(dc,sa(a[0],a[1])),a=[0,100]);const l=u({...t,keyframes:a});r==="mirror"&&(this.mirroredGenerator=u({...t,keyframes:[...a].reverse(),velocity:-o})),l.calculatedDuration===null&&(l.calculatedDuration=ys(l));const{calculatedDuration:c}=l;this.calculatedDuration=c,this.resolvedDuration=c+s,this.totalDuration=this.resolvedDuration*(n+1)-s,this.generator=l}updateTime(t){const e=Math.round(t-this.startTime)*this.playbackSpeed;this.holdTime!==null?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){const{generator:n,totalDuration:s,mixKeyframes:r,mirroredGenerator:o,resolvedDuration:a,calculatedDuration:u}=this;if(this.startTime===null)return n.next(0);const{delay:l=0,keyframes:c,repeat:h,repeatType:f,repeatDelay:d,type:m,onUpdate:p,finalKeyframe:g}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-s/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);const y=this.currentTime-l*(this.playbackSpeed>=0?1:-1),x=this.playbackSpeed>=0?y<0:y>s;this.currentTime=Math.max(y,0),this.state==="finished"&&this.holdTime===null&&(this.currentTime=s);let v=this.currentTime,T=n;if(h){const P=Math.min(this.currentTime,s)/a;let w=Math.floor(P),M=P%1;!M&&P>=1&&(M=1),M===1&&w--,w=Math.min(w,h+1),!!(w%2)&&(f==="reverse"?(M=1-M,d&&(M-=d/a)):f==="mirror"&&(T=o)),v=It(0,1,M)*a}const _=x?{done:!1,value:c[0]}:T.next(v);r&&(_.value=r(_.value));let{done:b}=_;!x&&u!==null&&(b=this.playbackSpeed>=0?this.currentTime>=s:this.currentTime<=0);const S=this.holdTime===null&&(this.state==="finished"||this.state==="running"&&b);return S&&m!==wn&&(_.value=_s(c,this.options,g,this.speed)),p&&p(_.value),S&&this.finish(),_}then(t,e){return this.finished.then(t,e)}get duration(){return Vt(this.calculatedDuration)}get time(){return Vt(this.currentTime)}set time(t){var e;t=Dt(t),this.currentTime=t,this.startTime===null||this.holdTime!==null||this.playbackSpeed===0?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),(e=this.driver)==null||e.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(ot.now());const e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=Vt(this.currentTime))}play(){var s,r;if(this.isStopped)return;const{driver:t=Hl,startTime:e}=this.options;this.driver||(this.driver=t(o=>this.tick(o))),(r=(s=this.options).onPlay)==null||r.call(s);const n=this.driver.now();this.state==="finished"?(this.updateFinished(),this.startTime=n):this.holdTime!==null?this.startTime=n-this.holdTime:this.startTime||(this.startTime=e??n),this.state==="finished"&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(ot.now()),this.holdTime=this.currentTime}complete(){this.state!=="running"&&this.play(),this.state="finished",this.holdTime=null}finish(){var t,e;this.notifyFinished(),this.teardown(),this.state="finished",(e=(t=this.options).onComplete)==null||e.call(t)}cancel(){var t,e;this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),(e=(t=this.options).onCancel)==null||e.call(t)}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){var e;return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),(e=this.driver)==null||e.stop(),t.observe(this)}}function pc(i){for(let t=1;t<i.length;t++)i[t]??(i[t]=i[t-1])}const le=i=>i*180/Math.PI,Sn=i=>{const t=le(Math.atan2(i[1],i[0]));return An(t)},mc={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:i=>(Math.abs(i[0])+Math.abs(i[3]))/2,rotate:Sn,rotateZ:Sn,skewX:i=>le(Math.atan(i[1])),skewY:i=>le(Math.atan(i[2])),skew:i=>(Math.abs(i[1])+Math.abs(i[2]))/2},An=i=>(i=i%360,i<0&&(i+=360),i),hr=Sn,fr=i=>Math.sqrt(i[0]*i[0]+i[1]*i[1]),dr=i=>Math.sqrt(i[4]*i[4]+i[5]*i[5]),gc={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:fr,scaleY:dr,scale:i=>(fr(i)+dr(i))/2,rotateX:i=>An(le(Math.atan2(i[6],i[5]))),rotateY:i=>An(le(Math.atan2(-i[2],i[0]))),rotateZ:hr,rotate:hr,skewX:i=>le(Math.atan(i[4])),skewY:i=>le(Math.atan(i[1])),skew:i=>(Math.abs(i[1])+Math.abs(i[4]))/2};function Cn(i){return i.includes("scale")?1:0}function Mn(i,t){if(!i||i==="none")return Cn(t);const e=i.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);let n,s;if(e)n=gc,s=e;else{const a=i.match(/^matrix\(([-\d.e\s,]+)\)$/u);n=mc,s=a}if(!s)return Cn(t);const r=n[t],o=s[1].split(",").map(_c);return typeof r=="function"?r(o):o[r]}const yc=(i,t)=>{const{transform:e="none"}=getComputedStyle(i);return Mn(e,t)};function _c(i){return parseFloat(i.trim())}const je=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],Ne=new Set(je),pr=i=>i===Ie||i===C,vc=new Set(["x","y","z"]),xc=je.filter(i=>!vc.has(i));function Tc(i){const t=[];return xc.forEach(e=>{const n=i.getValue(e);n!==void 0&&(t.push([e,n.get()]),n.set(e.startsWith("scale")?1:0))}),t}const he={width:({x:i},{paddingLeft:t="0",paddingRight:e="0"})=>i.max-i.min-parseFloat(t)-parseFloat(e),height:({y:i},{paddingTop:t="0",paddingBottom:e="0"})=>i.max-i.min-parseFloat(t)-parseFloat(e),top:(i,{top:t})=>parseFloat(t),left:(i,{left:t})=>parseFloat(t),bottom:({y:i},{top:t})=>parseFloat(t)+(i.max-i.min),right:({x:i},{left:t})=>parseFloat(t)+(i.max-i.min),x:(i,{transform:t})=>Mn(t,"x"),y:(i,{transform:t})=>Mn(t,"y")};he.translateX=he.x;he.translateY=he.y;const fe=new Set;let Dn=!1,Vn=!1,Rn=!1;function ua(){if(Vn){const i=Array.from(fe).filter(n=>n.needsMeasurement),t=new Set(i.map(n=>n.element)),e=new Map;t.forEach(n=>{const s=Tc(n);s.length&&(e.set(n,s),n.render())}),i.forEach(n=>n.measureInitialState()),t.forEach(n=>{n.render();const s=e.get(n);s&&s.forEach(([r,o])=>{var a;(a=n.getValue(r))==null||a.set(o)})}),i.forEach(n=>n.measureEndState()),i.forEach(n=>{n.suspendedScrollY!==void 0&&window.scrollTo(0,n.suspendedScrollY)})}Vn=!1,Dn=!1,fe.forEach(i=>i.complete(Rn)),fe.clear()}function la(){fe.forEach(i=>{i.readKeyframes(),i.needsMeasurement&&(Vn=!0)})}function bc(){Rn=!0,la(),ua(),Rn=!1}class Ts{constructor(t,e,n,s,r,o=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=n,this.motionValue=s,this.element=r,this.isAsync=o}scheduleResolve(){this.state="scheduled",this.isAsync?(fe.add(this),Dn||(Dn=!0,N.read(la),N.resolveKeyframes(ua))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:t,name:e,element:n,motionValue:s}=this;if(t[0]===null){const r=s==null?void 0:s.get(),o=t[t.length-1];if(r!==void 0)t[0]=r;else if(n&&e){const a=n.readValue(e,o);a!=null&&(t[0]=a)}t[0]===void 0&&(t[0]=o),s&&r===void 0&&s.set(t[0])}pc(t)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),fe.delete(this)}cancel(){this.state==="scheduled"&&(fe.delete(this),this.state="pending")}resume(){this.state==="pending"&&this.scheduleResolve()}}const Pc=i=>i.startsWith("--");function wc(i,t,e){Pc(t)?i.style.setProperty(t,e):i.style[t]=e}const Sc=us(()=>window.ScrollTimeline!==void 0),Ac={};function Cc(i,t){const e=us(i);return()=>Ac[t]??e()}const ca=Cc(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch{return!1}return!0},"linearEasing"),$e=([i,t,e,n])=>`cubic-bezier(${i}, ${t}, ${e}, ${n})`,mr={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:$e([0,.65,.55,1]),circOut:$e([.55,0,1,.45]),backIn:$e([.31,.01,.66,-.59]),backOut:$e([.33,1.53,.69,.99])};function ha(i,t){if(i)return typeof i=="function"?ca()?ra(i,t):"ease-out":qo(i)?$e(i):Array.isArray(i)?i.map(e=>ha(e,t)||mr.easeOut):mr[i]}function Mc(i,t,e,{delay:n=0,duration:s=300,repeat:r=0,repeatType:o="loop",ease:a="easeOut",times:u}={},l=void 0){const c={[t]:e};u&&(c.offset=u);const h=ha(a,s);Array.isArray(h)&&(c.easing=h);const f={delay:n,duration:s,easing:Array.isArray(h)?"linear":h,fill:"both",iterations:r+1,direction:o==="reverse"?"alternate":"normal"};return l&&(f.pseudoElement=l),i.animate(c,f)}function fa(i){return typeof i=="function"&&"applyToOptions"in i}function Dc({type:i,...t}){return fa(i)&&ca()?i.applyToOptions(t):(t.duration??(t.duration=300),t.ease??(t.ease="easeOut"),t)}class Vc extends vs{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;const{element:e,name:n,keyframes:s,pseudoElement:r,allowFlatten:o=!1,finalKeyframe:a,onComplete:u}=t;this.isPseudoElement=!!r,this.allowFlatten=o,this.options=t,as(typeof t.type!="string");const l=Dc(t);this.animation=Mc(e,n,s,l,r),l.autoplay===!1&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!r){const c=_s(s,this.options,a,this.speed);this.updateMotionValue?this.updateMotionValue(c):wc(e,n,c),this.animation.cancel()}u==null||u(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),this.state==="finished"&&this.updateFinished())}pause(){this.animation.pause()}complete(){var t,e;(e=(t=this.animation).finish)==null||e.call(t)}cancel(){try{this.animation.cancel()}catch{}}stop(){if(this.isStopped)return;this.isStopped=!0;const{state:t}=this;t==="idle"||t==="finished"||(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){var t,e;this.isPseudoElement||(e=(t=this.animation).commitStyles)==null||e.call(t)}get duration(){var e,n;const t=((n=(e=this.animation.effect)==null?void 0:e.getComputedTiming)==null?void 0:n.call(e).duration)||0;return Vt(Number(t))}get time(){return Vt(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=Dt(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return this.finishedTime!==null?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){var n;return this.allowFlatten&&((n=this.animation.effect)==null||n.updateTiming({easing:"linear"})),this.animation.onfinish=null,t&&Sc()?(this.animation.timeline=t,wt):e(this)}}const da={anticipate:$o,backInOut:Ko,circInOut:Go};function Rc(i){return i in da}function Ec(i){typeof i.ease=="string"&&Rc(i.ease)&&(i.ease=da[i.ease])}const gr=10;class kc extends Vc{constructor(t){Ec(t),aa(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){const{motionValue:e,onUpdate:n,onComplete:s,element:r,...o}=this.options;if(!e)return;if(t!==void 0){e.set(t);return}const a=new xs({...o,autoplay:!1}),u=Dt(this.finishedTime??this.time);e.setWithVelocity(a.sample(u-gr).value,a.sample(u).value,gr),a.stop()}}const yr=(i,t)=>t==="zIndex"?!1:!!(typeof i=="number"||Array.isArray(i)||typeof i=="string"&&(Ht.test(i)||i==="0")&&!i.startsWith("url("));function Oc(i){const t=i[0];if(i.length===1)return!0;for(let e=0;e<i.length;e++)if(i[e]!==t)return!0}function Lc(i,t,e,n){const s=i[0];if(s===null)return!1;if(t==="display"||t==="visibility")return!0;const r=i[i.length-1],o=yr(s,t),a=yr(r,t);return!o||!a?!1:Oc(i)||(e==="spring"||fa(e))&&n}function bs(i){return Bo(i)&&"offsetHeight"in i}const Fc=new Set(["opacity","clipPath","filter","transform"]),Bc=us(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));function Ic(i){var l;const{motionValue:t,name:e,repeatDelay:n,repeatType:s,damping:r,type:o}=i;if(!bs((l=t==null?void 0:t.owner)==null?void 0:l.current))return!1;const{onUpdate:a,transformTemplate:u}=t.owner.getProps();return Bc()&&e&&Fc.has(e)&&(e!=="transform"||!u)&&!a&&!n&&s!=="mirror"&&r!==0&&o!=="inertia"}const jc=40;class Nc extends vs{constructor({autoplay:t=!0,delay:e=0,type:n="keyframes",repeat:s=0,repeatDelay:r=0,repeatType:o="loop",keyframes:a,name:u,motionValue:l,element:c,...h}){var m;super(),this.stop=()=>{var p,g;this._animation&&(this._animation.stop(),(p=this.stopTimeline)==null||p.call(this)),(g=this.keyframeResolver)==null||g.cancel()},this.createdAt=ot.now();const f={autoplay:t,delay:e,type:n,repeat:s,repeatDelay:r,repeatType:o,name:u,motionValue:l,element:c,...h},d=(c==null?void 0:c.KeyframeResolver)||Ts;this.keyframeResolver=new d(a,(p,g,y)=>this.onKeyframesResolved(p,g,f,!y),u,l,c),(m=this.keyframeResolver)==null||m.scheduleResolve()}onKeyframesResolved(t,e,n,s){this.keyframeResolver=void 0;const{name:r,type:o,velocity:a,delay:u,isHandoff:l,onUpdate:c}=n;this.resolvedAt=ot.now(),Lc(t,r,o,a)||((jt.instantAnimations||!u)&&(c==null||c(_s(t,n,e))),t[0]=t[t.length-1],n.duration=0,n.repeat=0);const f={startTime:s?this.resolvedAt?this.resolvedAt-this.createdAt>jc?this.resolvedAt:this.createdAt:this.createdAt:void 0,finalKeyframe:e,...n,keyframes:t},d=!l&&Ic(f)?new kc({...f,element:f.motionValue.owner.current}):new xs(f);d.finished.then(()=>this.notifyFinished()).catch(wt),this.pendingTimeline&&(this.stopTimeline=d.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=d}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then(()=>{})}get animation(){var t;return this._animation||((t=this.keyframeResolver)==null||t.resume(),bc()),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){var t;this._animation&&this.animation.cancel(),(t=this.keyframeResolver)==null||t.cancel()}}const zc=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function Uc(i){const t=zc.exec(i);if(!t)return[,];const[,e,n,s]=t;return[`--${e??n}`,s]}function pa(i,t,e=1){const[n,s]=Uc(i);if(!n)return;const r=window.getComputedStyle(t).getPropertyValue(n);if(r){const o=r.trim();return Fo(o)?parseFloat(o):o}return ds(s)?pa(s,t,e+1):s}function Ps(i,t){return(i==null?void 0:i[t])??(i==null?void 0:i.default)??i}const ma=new Set(["width","height","top","left","right","bottom",...je]),Wc={test:i=>i==="auto",parse:i=>i},ga=i=>t=>t.test(i),ya=[Ie,C,Rt,Wt,Ol,kl,Wc],_r=i=>ya.find(ga(i));function Kc(i){return typeof i=="number"?i===0:i!==null?i==="none"||i==="0"||Io(i):!0}const $c=new Set(["brightness","contrast","saturate","opacity"]);function Yc(i){const[t,e]=i.slice(0,-1).split("(");if(t==="drop-shadow")return i;const[n]=e.match(ps)||[];if(!n)return i;const s=e.replace(n,"");let r=$c.has(t)?1:0;return n!==e&&(r*=100),t+"("+r+s+")"}const Gc=/\b([a-z-]*)\(.*?\)/gu,En={...Ht,getAnimatableNone:i=>{const t=i.match(Gc);return t?t.map(Yc).join(" "):i}},vr={...Ie,transform:Math.round},Xc={rotate:Wt,rotateX:Wt,rotateY:Wt,rotateZ:Wt,scale:bi,scaleX:bi,scaleY:bi,scaleZ:bi,skew:Wt,skewX:Wt,skewY:Wt,distance:C,translateX:C,translateY:C,translateZ:C,x:C,y:C,z:C,perspective:C,transformPerspective:C,opacity:ni,originX:or,originY:or,originZ:C},ws={borderWidth:C,borderTopWidth:C,borderRightWidth:C,borderBottomWidth:C,borderLeftWidth:C,borderRadius:C,radius:C,borderTopLeftRadius:C,borderTopRightRadius:C,borderBottomRightRadius:C,borderBottomLeftRadius:C,width:C,maxWidth:C,height:C,maxHeight:C,top:C,right:C,bottom:C,left:C,padding:C,paddingTop:C,paddingRight:C,paddingBottom:C,paddingLeft:C,margin:C,marginTop:C,marginRight:C,marginBottom:C,marginLeft:C,backgroundPositionX:C,backgroundPositionY:C,...Xc,zIndex:vr,fillOpacity:ni,strokeOpacity:ni,numOctaves:vr},qc={...ws,color:X,backgroundColor:X,outlineColor:X,fill:X,stroke:X,borderColor:X,borderTopColor:X,borderRightColor:X,borderBottomColor:X,borderLeftColor:X,filter:En,WebkitFilter:En},_a=i=>qc[i];function va(i,t){let e=_a(i);return e!==En&&(e=Ht),e.getAnimatableNone?e.getAnimatableNone(t):void 0}const Hc=new Set(["auto","none","0"]);function Zc(i,t,e){let n=0,s;for(;n<i.length&&!s;){const r=i[n];typeof r=="string"&&!Hc.has(r)&&si(r).values.length&&(s=i[n]),n++}if(s&&e)for(const r of t)i[r]=va(e,s)}class Qc extends Ts{constructor(t,e,n,s,r){super(t,e,n,s,r,!0)}readKeyframes(){const{unresolvedKeyframes:t,element:e,name:n}=this;if(!e||!e.current)return;super.readKeyframes();for(let u=0;u<t.length;u++){let l=t[u];if(typeof l=="string"&&(l=l.trim(),ds(l))){const c=pa(l,e.current);c!==void 0&&(t[u]=c),u===t.length-1&&(this.finalKeyframe=l)}}if(this.resolveNoneKeyframes(),!ma.has(n)||t.length!==2)return;const[s,r]=t,o=_r(s),a=_r(r);if(o!==a)if(pr(o)&&pr(a))for(let u=0;u<t.length;u++){const l=t[u];typeof l=="string"&&(t[u]=parseFloat(l))}else he[n]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){const{unresolvedKeyframes:t,name:e}=this,n=[];for(let s=0;s<t.length;s++)(t[s]===null||Kc(t[s]))&&n.push(s);n.length&&Zc(t,n,e)}measureInitialState(){const{element:t,unresolvedKeyframes:e,name:n}=this;if(!t||!t.current)return;n==="height"&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=he[n](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;const s=e[e.length-1];s!==void 0&&t.getValue(n,s).jump(s,!1)}measureEndState(){var a;const{element:t,name:e,unresolvedKeyframes:n}=this;if(!t||!t.current)return;const s=t.getValue(e);s&&s.jump(this.measuredOrigin,!1);const r=n.length-1,o=n[r];n[r]=he[e](t.measureViewportBox(),window.getComputedStyle(t.current)),o!==null&&this.finalKeyframe===void 0&&(this.finalKeyframe=o),(a=this.removedTransforms)!=null&&a.length&&this.removedTransforms.forEach(([u,l])=>{t.getValue(u).set(l)}),this.resolveNoneKeyframes()}}function Jc(i,t,e){if(i instanceof EventTarget)return[i];if(typeof i=="string"){let n=document;const s=(e==null?void 0:e[i])??n.querySelectorAll(i);return s?Array.from(s):[]}return Array.from(i)}const xa=(i,t)=>t&&typeof i=="number"?t.transform(i):i,xr=30,th=i=>!isNaN(parseFloat(i));class eh{constructor(t,e={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(n,s=!0)=>{var o,a;const r=ot.now();if(this.updatedAt!==r&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(n),this.current!==this.prev&&((o=this.events.change)==null||o.notify(this.current),this.dependents))for(const u of this.dependents)u.dirty();s&&((a=this.events.renderRequest)==null||a.notify(this.current))},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){this.current=t,this.updatedAt=ot.now(),this.canTrackVelocity===null&&t!==void 0&&(this.canTrackVelocity=th(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new ls);const n=this.events[t].add(e);return t==="change"?()=>{n(),N.read(()=>{this.events.change.getSize()||this.stop()})}:n}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){!e||!this.passiveEffect?this.updateAndNotify(t,e):this.passiveEffect(t,this.updateAndNotify)}setWithVelocity(t,e,n){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-n}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){var t;(t=this.events.change)==null||t.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const t=ot.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||t-this.updatedAt>xr)return 0;const e=Math.min(this.updatedAt-this.prevUpdatedAt,xr);return jo(parseFloat(this.current)-parseFloat(this.prevFrameValue),e)}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){var t,e;(t=this.dependents)==null||t.clear(),(e=this.events.destroy)==null||e.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function Ve(i,t){return new eh(i,t)}const{schedule:Ss}=Ho(queueMicrotask,!1),St={x:!1,y:!1};function Ta(){return St.x||St.y}function ih(i){return i==="x"||i==="y"?St[i]?null:(St[i]=!0,()=>{St[i]=!1}):St.x||St.y?null:(St.x=St.y=!0,()=>{St.x=St.y=!1})}function ba(i,t){const e=Jc(i),n=new AbortController,s={passive:!0,...t,signal:n.signal};return[e,s,()=>n.abort()]}function Tr(i){return!(i.pointerType==="touch"||Ta())}function nh(i,t,e={}){const[n,s,r]=ba(i,e),o=a=>{if(!Tr(a))return;const{target:u}=a,l=t(u,a);if(typeof l!="function"||!u)return;const c=h=>{Tr(h)&&(l(h),u.removeEventListener("pointerleave",c))};u.addEventListener("pointerleave",c,s)};return n.forEach(a=>{a.addEventListener("pointerenter",o,s)}),r}const Pa=(i,t)=>t?i===t?!0:Pa(i,t.parentElement):!1,As=i=>i.pointerType==="mouse"?typeof i.button!="number"||i.button<=0:i.isPrimary!==!1,sh=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);function rh(i){return sh.has(i.tagName)||i.tabIndex!==-1}const Ci=new WeakSet;function br(i){return t=>{t.key==="Enter"&&i(t)}}function rn(i,t){i.dispatchEvent(new PointerEvent("pointer"+t,{isPrimary:!0,bubbles:!0}))}const oh=(i,t)=>{const e=i.currentTarget;if(!e)return;const n=br(()=>{if(Ci.has(e))return;rn(e,"down");const s=br(()=>{rn(e,"up")}),r=()=>rn(e,"cancel");e.addEventListener("keyup",s,t),e.addEventListener("blur",r,t)});e.addEventListener("keydown",n,t),e.addEventListener("blur",()=>e.removeEventListener("keydown",n),t)};function Pr(i){return As(i)&&!Ta()}function ah(i,t,e={}){const[n,s,r]=ba(i,e),o=a=>{const u=a.currentTarget;if(!Pr(a))return;Ci.add(u);const l=t(u,a),c=(d,m)=>{window.removeEventListener("pointerup",h),window.removeEventListener("pointercancel",f),Ci.has(u)&&Ci.delete(u),Pr(d)&&typeof l=="function"&&l(d,{success:m})},h=d=>{c(d,u===window||u===document||e.useGlobalTarget||Pa(u,d.target))},f=d=>{c(d,!1)};window.addEventListener("pointerup",h,s),window.addEventListener("pointercancel",f,s)};return n.forEach(a=>{(e.useGlobalTarget?window:a).addEventListener("pointerdown",o,s),bs(a)&&(a.addEventListener("focus",l=>oh(l,s)),!rh(a)&&!a.hasAttribute("tabindex")&&(a.tabIndex=0))}),r}function wa(i){return Bo(i)&&"ownerSVGElement"in i}function uh(i){return wa(i)&&i.tagName==="svg"}const it=i=>!!(i&&i.getVelocity),lh=[...ya,X,Ht],ch=i=>lh.find(ga(i)),Cs=A.createContext({transformPagePoint:i=>i,isStatic:!1,reducedMotion:"never"});class hh extends A.Component{getSnapshotBeforeUpdate(t){const e=this.props.childRef.current;if(e&&t.isPresent&&!this.props.isPresent){const n=e.offsetParent,s=bs(n)&&n.offsetWidth||0,r=this.props.sizeRef.current;r.height=e.offsetHeight||0,r.width=e.offsetWidth||0,r.top=e.offsetTop,r.left=e.offsetLeft,r.right=s-r.width-r.left}return null}componentDidUpdate(){}render(){return this.props.children}}function fh({children:i,isPresent:t,anchorX:e}){const n=A.useId(),s=A.useRef(null),r=A.useRef({width:0,height:0,top:0,left:0,right:0}),{nonce:o}=A.useContext(Cs);return A.useInsertionEffect(()=>{const{width:a,height:u,top:l,left:c,right:h}=r.current;if(t||!s.current||!a||!u)return;const f=e==="left"?`left: ${c}`:`right: ${h}`;s.current.dataset.motionPopId=n;const d=document.createElement("style");return o&&(d.nonce=o),document.head.appendChild(d),d.sheet&&d.sheet.insertRule(`
          [data-motion-pop-id="${n}"] {
            position: absolute !important;
            width: ${a}px !important;
            height: ${u}px !important;
            ${f}px !important;
            top: ${l}px !important;
          }
        `),()=>{document.head.contains(d)&&document.head.removeChild(d)}},[t]),Bt.jsx(hh,{isPresent:t,childRef:s,sizeRef:r,children:A.cloneElement(i,{ref:s})})}const dh=({children:i,initial:t,isPresent:e,onExitComplete:n,custom:s,presenceAffectsLayout:r,mode:o,anchorX:a})=>{const u=ns(ph),l=A.useId();let c=!0,h=A.useMemo(()=>(c=!1,{id:l,initial:t,isPresent:e,custom:s,onExitComplete:f=>{u.set(f,!0);for(const d of u.values())if(!d)return;n&&n()},register:f=>(u.set(f,!1),()=>u.delete(f))}),[e,u,n]);return r&&c&&(h={...h}),A.useMemo(()=>{u.forEach((f,d)=>u.set(d,!1))},[e]),A.useEffect(()=>{!e&&!u.size&&n&&n()},[e]),o==="popLayout"&&(i=Bt.jsx(fh,{isPresent:e,anchorX:a,children:i})),Bt.jsx($i.Provider,{value:h,children:i})};function ph(){return new Map}function Sa(i=!0){const t=A.useContext($i);if(t===null)return[!0,null];const{isPresent:e,onExitComplete:n,register:s}=t,r=A.useId();A.useEffect(()=>{if(i)return s(r)},[i]);const o=A.useCallback(()=>i&&n&&n(r),[r,n,i]);return!e&&n?[!1,o]:[!0]}const Pi=i=>i.key||"";function wr(i){const t=[];return A.Children.forEach(i,e=>{A.isValidElement(e)&&t.push(e)}),t}const gm=({children:i,custom:t,initial:e=!0,onExitComplete:n,presenceAffectsLayout:s=!0,mode:r="sync",propagate:o=!1,anchorX:a="left"})=>{const[u,l]=Sa(o),c=A.useMemo(()=>wr(i),[i]),h=o&&!u?[]:c.map(Pi),f=A.useRef(!0),d=A.useRef(c),m=ns(()=>new Map),[p,g]=A.useState(c),[y,x]=A.useState(c);Lo(()=>{f.current=!1,d.current=c;for(let _=0;_<y.length;_++){const b=Pi(y[_]);h.includes(b)?m.delete(b):m.get(b)!==!0&&m.set(b,!1)}},[y,h.length,h.join("-")]);const v=[];if(c!==p){let _=[...c];for(let b=0;b<y.length;b++){const S=y[b],P=Pi(S);h.includes(P)||(_.splice(b,0,S),v.push(S))}return r==="wait"&&v.length&&(_=v),x(wr(_)),g(c),null}const{forceRender:T}=A.useContext(is);return Bt.jsx(Bt.Fragment,{children:y.map(_=>{const b=Pi(_),S=o&&!u?!1:c===y||h.includes(b),P=()=>{if(m.has(b))m.set(b,!0);else return;let w=!0;m.forEach(M=>{M||(w=!1)}),w&&(T==null||T(),x(d.current),o&&(l==null||l()),n&&n())};return Bt.jsx(dh,{isPresent:S,initial:!f.current||e?void 0:!1,custom:t,presenceAffectsLayout:s,mode:r,onExitComplete:S?void 0:P,anchorX:a,children:_},b)})})},Aa=A.createContext({strict:!1}),Sr={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},Re={};for(const i in Sr)Re[i]={isEnabled:t=>Sr[i].some(e=>!!t[e])};function mh(i){for(const t in i)Re[t]={...Re[t],...i[t]}}const gh=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function Fi(i){return i.startsWith("while")||i.startsWith("drag")&&i!=="draggable"||i.startsWith("layout")||i.startsWith("onTap")||i.startsWith("onPan")||i.startsWith("onLayout")||gh.has(i)}let Ca=i=>!Fi(i);function yh(i){typeof i=="function"&&(Ca=t=>t.startsWith("on")?!Fi(t):i(t))}try{yh(require("@emotion/is-prop-valid").default)}catch{}function _h(i,t,e){const n={};for(const s in i)s==="values"&&typeof i.values=="object"||(Ca(s)||e===!0&&Fi(s)||!t&&!Fi(s)||i.draggable&&s.startsWith("onDrag"))&&(n[s]=i[s]);return n}function vh(i){if(typeof Proxy>"u")return i;const t=new Map,e=(...n)=>i(...n);return new Proxy(e,{get:(n,s)=>s==="create"?i:(t.has(s)||t.set(s,i(s)),t.get(s))})}const Yi=A.createContext({});function Gi(i){return i!==null&&typeof i=="object"&&typeof i.start=="function"}function ri(i){return typeof i=="string"||Array.isArray(i)}const Ms=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],Ds=["initial",...Ms];function Xi(i){return Gi(i.animate)||Ds.some(t=>ri(i[t]))}function Ma(i){return!!(Xi(i)||i.variants)}function xh(i,t){if(Xi(i)){const{initial:e,animate:n}=i;return{initial:e===!1||ri(e)?e:void 0,animate:ri(n)?n:void 0}}return i.inherit!==!1?t:{}}function Th(i){const{initial:t,animate:e}=xh(i,A.useContext(Yi));return A.useMemo(()=>({initial:t,animate:e}),[Ar(t),Ar(e)])}function Ar(i){return Array.isArray(i)?i.join(" "):i}const bh=Symbol.for("motionComponentSymbol");function Te(i){return i&&typeof i=="object"&&Object.prototype.hasOwnProperty.call(i,"current")}function Ph(i,t,e){return A.useCallback(n=>{n&&i.onMount&&i.onMount(n),t&&(n?t.mount(n):t.unmount()),e&&(typeof e=="function"?e(n):Te(e)&&(e.current=n))},[t])}const Vs=i=>i.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),wh="framerAppearId",Da="data-"+Vs(wh),Va=A.createContext({});function Sh(i,t,e,n,s){var p,g;const{visualElement:r}=A.useContext(Yi),o=A.useContext(Aa),a=A.useContext($i),u=A.useContext(Cs).reducedMotion,l=A.useRef(null);n=n||o.renderer,!l.current&&n&&(l.current=n(i,{visualState:t,parent:r,props:e,presenceContext:a,blockInitialAnimation:a?a.initial===!1:!1,reducedMotionConfig:u}));const c=l.current,h=A.useContext(Va);c&&!c.projection&&s&&(c.type==="html"||c.type==="svg")&&Ah(l.current,e,s,h);const f=A.useRef(!1);A.useInsertionEffect(()=>{c&&f.current&&c.update(e,a)});const d=e[Da],m=A.useRef(!!d&&!((p=window.MotionHandoffIsComplete)!=null&&p.call(window,d))&&((g=window.MotionHasOptimisedAnimation)==null?void 0:g.call(window,d)));return Lo(()=>{c&&(f.current=!0,window.MotionIsMounted=!0,c.updateFeatures(),Ss.render(c.render),m.current&&c.animationState&&c.animationState.animateChanges())}),A.useEffect(()=>{c&&(!m.current&&c.animationState&&c.animationState.animateChanges(),m.current&&(queueMicrotask(()=>{var y;(y=window.MotionHandoffMarkAsComplete)==null||y.call(window,d)}),m.current=!1))}),c}function Ah(i,t,e,n){const{layoutId:s,layout:r,drag:o,dragConstraints:a,layoutScroll:u,layoutRoot:l,layoutCrossfade:c}=t;i.projection=new e(i.latestValues,t["data-framer-portal-id"]?void 0:Ra(i.parent)),i.projection.setOptions({layoutId:s,layout:r,alwaysMeasureLayout:!!o||a&&Te(a),visualElement:i,animationType:typeof r=="string"?r:"both",initialPromotionConfig:n,crossfade:c,layoutScroll:u,layoutRoot:l})}function Ra(i){if(i)return i.options.allowProjection!==!1?i.projection:Ra(i.parent)}function Ch({preloadedFeatures:i,createVisualElement:t,useRender:e,useVisualState:n,Component:s}){i&&mh(i);function r(a,u){let l;const c={...A.useContext(Cs),...a,layoutId:Mh(a)},{isStatic:h}=c,f=Th(a),d=n(a,h);if(!h&&ss){Dh();const m=Vh(c);l=m.MeasureLayout,f.visualElement=Sh(s,d,c,t,m.ProjectionNode)}return Bt.jsxs(Yi.Provider,{value:f,children:[l&&f.visualElement?Bt.jsx(l,{visualElement:f.visualElement,...c}):null,e(s,a,Ph(d,f.visualElement,u),d,h,f.visualElement)]})}r.displayName=`motion.${typeof s=="string"?s:`create(${s.displayName??s.name??""})`}`;const o=A.forwardRef(r);return o[bh]=s,o}function Mh({layoutId:i}){const t=A.useContext(is).id;return t&&i!==void 0?t+"-"+i:i}function Dh(i,t){A.useContext(Aa).strict}function Vh(i){const{drag:t,layout:e}=Re;if(!t&&!e)return{};const n={...t,...e};return{MeasureLayout:t!=null&&t.isEnabled(i)||e!=null&&e.isEnabled(i)?n.MeasureLayout:void 0,ProjectionNode:n.ProjectionNode}}const oi={};function Rh(i){for(const t in i)oi[t]=i[t],fs(t)&&(oi[t].isCSSVariable=!0)}function Ea(i,{layout:t,layoutId:e}){return Ne.has(i)||i.startsWith("origin")||(t||e!==void 0)&&(!!oi[i]||i==="opacity")}const Eh={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},kh=je.length;function Oh(i,t,e){let n="",s=!0;for(let r=0;r<kh;r++){const o=je[r],a=i[o];if(a===void 0)continue;let u=!0;if(typeof a=="number"?u=a===(o.startsWith("scale")?1:0):u=parseFloat(a)===0,!u||e){const l=xa(a,ws[o]);if(!u){s=!1;const c=Eh[o]||o;n+=`${c}(${l}) `}e&&(t[o]=l)}}return n=n.trim(),e?n=e(t,s?"":n):s&&(n="none"),n}function Rs(i,t,e){const{style:n,vars:s,transformOrigin:r}=i;let o=!1,a=!1;for(const u in t){const l=t[u];if(Ne.has(u)){o=!0;continue}else if(fs(u)){s[u]=l;continue}else{const c=xa(l,ws[u]);u.startsWith("origin")?(a=!0,r[u]=c):n[u]=c}}if(t.transform||(o||e?n.transform=Oh(t,i.transform,e):n.transform&&(n.transform="none")),a){const{originX:u="50%",originY:l="50%",originZ:c=0}=r;n.transformOrigin=`${u} ${l} ${c}`}}const Es=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function ka(i,t,e){for(const n in t)!it(t[n])&&!Ea(n,e)&&(i[n]=t[n])}function Lh({transformTemplate:i},t){return A.useMemo(()=>{const e=Es();return Rs(e,t,i),Object.assign({},e.vars,e.style)},[t])}function Fh(i,t){const e=i.style||{},n={};return ka(n,e,i),Object.assign(n,Lh(i,t)),n}function Bh(i,t){const e={},n=Fh(i,t);return i.drag&&i.dragListener!==!1&&(e.draggable=!1,n.userSelect=n.WebkitUserSelect=n.WebkitTouchCallout="none",n.touchAction=i.drag===!0?"none":`pan-${i.drag==="x"?"y":"x"}`),i.tabIndex===void 0&&(i.onTap||i.onTapStart||i.whileTap)&&(e.tabIndex=0),e.style=n,e}const Ih={offset:"stroke-dashoffset",array:"stroke-dasharray"},jh={offset:"strokeDashoffset",array:"strokeDasharray"};function Nh(i,t,e=1,n=0,s=!0){i.pathLength=1;const r=s?Ih:jh;i[r.offset]=C.transform(-n);const o=C.transform(t),a=C.transform(e);i[r.array]=`${o} ${a}`}function Oa(i,{attrX:t,attrY:e,attrScale:n,pathLength:s,pathSpacing:r=1,pathOffset:o=0,...a},u,l,c){if(Rs(i,a,l),u){i.style.viewBox&&(i.attrs.viewBox=i.style.viewBox);return}i.attrs=i.style,i.style={};const{attrs:h,style:f}=i;h.transform&&(f.transform=h.transform,delete h.transform),(f.transform||h.transformOrigin)&&(f.transformOrigin=h.transformOrigin??"50% 50%",delete h.transformOrigin),f.transform&&(f.transformBox=(c==null?void 0:c.transformBox)??"fill-box",delete h.transformBox),t!==void 0&&(h.x=t),e!==void 0&&(h.y=e),n!==void 0&&(h.scale=n),s!==void 0&&Nh(h,s,r,o,!1)}const La=()=>({...Es(),attrs:{}}),Fa=i=>typeof i=="string"&&i.toLowerCase()==="svg";function zh(i,t,e,n){const s=A.useMemo(()=>{const r=La();return Oa(r,t,Fa(n),i.transformTemplate,i.style),{...r.attrs,style:{...r.style}}},[t]);if(i.style){const r={};ka(r,i.style,i),s.style={...r,...s.style}}return s}const Uh=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function ks(i){return typeof i!="string"||i.includes("-")?!1:!!(Uh.indexOf(i)>-1||/[A-Z]/u.test(i))}function Wh(i=!1){return(e,n,s,{latestValues:r},o)=>{const u=(ks(e)?zh:Bh)(n,r,o,e),l=_h(n,typeof e=="string",i),c=e!==A.Fragment?{...l,...u,ref:s}:{},{children:h}=n,f=A.useMemo(()=>it(h)?h.get():h,[h]);return A.createElement(e,{...c,children:f})}}function Cr(i){const t=[{},{}];return i==null||i.values.forEach((e,n)=>{t[0][n]=e.get(),t[1][n]=e.getVelocity()}),t}function Os(i,t,e,n){if(typeof t=="function"){const[s,r]=Cr(n);t=t(e!==void 0?e:i.custom,s,r)}if(typeof t=="string"&&(t=i.variants&&i.variants[t]),typeof t=="function"){const[s,r]=Cr(n);t=t(e!==void 0?e:i.custom,s,r)}return t}function Mi(i){return it(i)?i.get():i}function Kh({scrapeMotionValuesFromProps:i,createRenderState:t},e,n,s){return{latestValues:$h(e,n,s,i),renderState:t()}}const Ba=i=>(t,e)=>{const n=A.useContext(Yi),s=A.useContext($i),r=()=>Kh(i,t,n,s);return e?r():ns(r)};function $h(i,t,e,n){const s={},r=n(i,{});for(const f in r)s[f]=Mi(r[f]);let{initial:o,animate:a}=i;const u=Xi(i),l=Ma(i);t&&l&&!u&&i.inherit!==!1&&(o===void 0&&(o=t.initial),a===void 0&&(a=t.animate));let c=e?e.initial===!1:!1;c=c||o===!1;const h=c?a:o;if(h&&typeof h!="boolean"&&!Gi(h)){const f=Array.isArray(h)?h:[h];for(let d=0;d<f.length;d++){const m=Os(i,f[d]);if(m){const{transitionEnd:p,transition:g,...y}=m;for(const x in y){let v=y[x];if(Array.isArray(v)){const T=c?v.length-1:0;v=v[T]}v!==null&&(s[x]=v)}for(const x in p)s[x]=p[x]}}}return s}function Ls(i,t,e){var r;const{style:n}=i,s={};for(const o in n)(it(n[o])||t.style&&it(t.style[o])||Ea(o,i)||((r=e==null?void 0:e.getValue(o))==null?void 0:r.liveStyle)!==void 0)&&(s[o]=n[o]);return s}const Yh={useVisualState:Ba({scrapeMotionValuesFromProps:Ls,createRenderState:Es})};function Ia(i,t,e){const n=Ls(i,t,e);for(const s in i)if(it(i[s])||it(t[s])){const r=je.indexOf(s)!==-1?"attr"+s.charAt(0).toUpperCase()+s.substring(1):s;n[r]=i[s]}return n}const Gh={useVisualState:Ba({scrapeMotionValuesFromProps:Ia,createRenderState:La})};function Xh(i,t){return function(n,{forwardMotionProps:s}={forwardMotionProps:!1}){const o={...ks(n)?Gh:Yh,preloadedFeatures:i,useRender:Wh(s),createVisualElement:t,Component:n};return Ch(o)}}function ai(i,t,e){const n=i.getProps();return Os(n,t,e!==void 0?e:n.custom,i)}const kn=i=>Array.isArray(i);function qh(i,t,e){i.hasValue(t)?i.getValue(t).set(e):i.addValue(t,Ve(e))}function Hh(i){return kn(i)?i[i.length-1]||0:i}function Zh(i,t){const e=ai(i,t);let{transitionEnd:n={},transition:s={},...r}=e||{};r={...r,...n};for(const o in r){const a=Hh(r[o]);qh(i,o,a)}}function Qh(i){return!!(it(i)&&i.add)}function On(i,t){const e=i.getValue("willChange");if(Qh(e))return e.add(t);if(!e&&jt.WillChange){const n=new jt.WillChange("auto");i.addValue("willChange",n),n.add(t)}}function ja(i){return i.props[Da]}const Jh=i=>i!==null;function tf(i,{repeat:t,repeatType:e="loop"},n){const s=i.filter(Jh),r=t&&e!=="loop"&&t%2===1?0:s.length-1;return s[r]}const ef={type:"spring",stiffness:500,damping:25,restSpeed:10},nf=i=>({type:"spring",stiffness:550,damping:i===0?2*Math.sqrt(550):30,restSpeed:10}),sf={type:"keyframes",duration:.8},rf={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},of=(i,{keyframes:t})=>t.length>2?sf:Ne.has(i)?i.startsWith("scale")?nf(t[1]):ef:rf;function af({when:i,delay:t,delayChildren:e,staggerChildren:n,staggerDirection:s,repeat:r,repeatType:o,repeatDelay:a,from:u,elapsed:l,...c}){return!!Object.keys(c).length}const Fs=(i,t,e,n={},s,r)=>o=>{const a=Ps(n,i)||{},u=a.delay||n.delay||0;let{elapsed:l=0}=n;l=l-Dt(u);const c={keyframes:Array.isArray(e)?e:[null,e],ease:"easeOut",velocity:t.getVelocity(),...a,delay:-l,onUpdate:f=>{t.set(f),a.onUpdate&&a.onUpdate(f)},onComplete:()=>{o(),a.onComplete&&a.onComplete()},name:i,motionValue:t,element:r?void 0:s};af(a)||Object.assign(c,of(i,c)),c.duration&&(c.duration=Dt(c.duration)),c.repeatDelay&&(c.repeatDelay=Dt(c.repeatDelay)),c.from!==void 0&&(c.keyframes[0]=c.from);let h=!1;if((c.type===!1||c.duration===0&&!c.repeatDelay)&&(c.duration=0,c.delay===0&&(h=!0)),(jt.instantAnimations||jt.skipAnimations)&&(h=!0,c.duration=0,c.delay=0),c.allowFlatten=!a.type&&!a.ease,h&&!r&&t.get()!==void 0){const f=tf(c.keyframes,a);if(f!==void 0){N.update(()=>{c.onUpdate(f),c.onComplete()});return}}return a.isSync?new xs(c):new Nc(c)};function uf({protectedKeys:i,needsAnimating:t},e){const n=i.hasOwnProperty(e)&&t[e]!==!0;return t[e]=!1,n}function Na(i,t,{delay:e=0,transitionOverride:n,type:s}={}){let{transition:r=i.getDefaultTransition(),transitionEnd:o,...a}=t;n&&(r=n);const u=[],l=s&&i.animationState&&i.animationState.getState()[s];for(const c in a){const h=i.getValue(c,i.latestValues[c]??null),f=a[c];if(f===void 0||l&&uf(l,c))continue;const d={delay:e,...Ps(r||{},c)},m=h.get();if(m!==void 0&&!h.isAnimating&&!Array.isArray(f)&&f===m&&!d.velocity)continue;let p=!1;if(window.MotionHandoffAnimation){const y=ja(i);if(y){const x=window.MotionHandoffAnimation(y,c,N);x!==null&&(d.startTime=x,p=!0)}}On(i,c),h.start(Fs(c,h,f,i.shouldReduceMotion&&ma.has(c)?{type:!1}:d,i,p));const g=h.animation;g&&u.push(g)}return o&&Promise.all(u).then(()=>{N.update(()=>{o&&Zh(i,o)})}),u}function Ln(i,t,e={}){var u;const n=ai(i,t,e.type==="exit"?(u=i.presenceContext)==null?void 0:u.custom:void 0);let{transition:s=i.getDefaultTransition()||{}}=n||{};e.transitionOverride&&(s=e.transitionOverride);const r=n?()=>Promise.all(Na(i,n,e)):()=>Promise.resolve(),o=i.variantChildren&&i.variantChildren.size?(l=0)=>{const{delayChildren:c=0,staggerChildren:h,staggerDirection:f}=s;return lf(i,t,c+l,h,f,e)}:()=>Promise.resolve(),{when:a}=s;if(a){const[l,c]=a==="beforeChildren"?[r,o]:[o,r];return l().then(()=>c())}else return Promise.all([r(),o(e.delay)])}function lf(i,t,e=0,n=0,s=1,r){const o=[],a=(i.variantChildren.size-1)*n,u=s===1?(l=0)=>l*n:(l=0)=>a-l*n;return Array.from(i.variantChildren).sort(cf).forEach((l,c)=>{l.notify("AnimationStart",t),o.push(Ln(l,t,{...r,delay:e+u(c)}).then(()=>l.notify("AnimationComplete",t)))}),Promise.all(o)}function cf(i,t){return i.sortNodePosition(t)}function hf(i,t,e={}){i.notify("AnimationStart",t);let n;if(Array.isArray(t)){const s=t.map(r=>Ln(i,r,e));n=Promise.all(s)}else if(typeof t=="string")n=Ln(i,t,e);else{const s=typeof t=="function"?ai(i,t,e.custom):t;n=Promise.all(Na(i,s,e))}return n.then(()=>{i.notify("AnimationComplete",t)})}function za(i,t){if(!Array.isArray(t))return!1;const e=t.length;if(e!==i.length)return!1;for(let n=0;n<e;n++)if(t[n]!==i[n])return!1;return!0}const ff=Ds.length;function Ua(i){if(!i)return;if(!i.isControllingVariants){const e=i.parent?Ua(i.parent)||{}:{};return i.props.initial!==void 0&&(e.initial=i.props.initial),e}const t={};for(let e=0;e<ff;e++){const n=Ds[e],s=i.props[n];(ri(s)||s===!1)&&(t[n]=s)}return t}const df=[...Ms].reverse(),pf=Ms.length;function mf(i){return t=>Promise.all(t.map(({animation:e,options:n})=>hf(i,e,n)))}function gf(i){let t=mf(i),e=Mr(),n=!0;const s=u=>(l,c)=>{var f;const h=ai(i,c,u==="exit"?(f=i.presenceContext)==null?void 0:f.custom:void 0);if(h){const{transition:d,transitionEnd:m,...p}=h;l={...l,...p,...m}}return l};function r(u){t=u(i)}function o(u){const{props:l}=i,c=Ua(i.parent)||{},h=[],f=new Set;let d={},m=1/0;for(let g=0;g<pf;g++){const y=df[g],x=e[y],v=l[y]!==void 0?l[y]:c[y],T=ri(v),_=y===u?x.isActive:null;_===!1&&(m=g);let b=v===c[y]&&v!==l[y]&&T;if(b&&n&&i.manuallyAnimateOnMount&&(b=!1),x.protectedKeys={...d},!x.isActive&&_===null||!v&&!x.prevProp||Gi(v)||typeof v=="boolean")continue;const S=yf(x.prevProp,v);let P=S||y===u&&x.isActive&&!b&&T||g>m&&T,w=!1;const M=Array.isArray(v)?v:[v];let R=M.reduce(s(y),{});_===!1&&(R={});const{prevResolvedValues:E={}}=x,k={...E,...R},G=V=>{P=!0,f.has(V)&&(w=!0,f.delete(V)),x.needsAnimating[V]=!0;const K=i.getValue(V);K&&(K.liveStyle=!1)};for(const V in k){const K=R[V],Ut=E[V];if(d.hasOwnProperty(V))continue;let kt=!1;kn(K)&&kn(Ut)?kt=!za(K,Ut):kt=K!==Ut,kt?K!=null?G(V):f.add(V):K!==void 0&&f.has(V)?G(V):x.protectedKeys[V]=!0}x.prevProp=v,x.prevResolvedValues=R,x.isActive&&(d={...d,...R}),n&&i.blockInitialAnimation&&(P=!1),P&&(!(b&&S)||w)&&h.push(...M.map(V=>({animation:V,options:{type:y}})))}if(f.size){const g={};if(typeof l.initial!="boolean"){const y=ai(i,Array.isArray(l.initial)?l.initial[0]:l.initial);y&&y.transition&&(g.transition=y.transition)}f.forEach(y=>{const x=i.getBaseTarget(y),v=i.getValue(y);v&&(v.liveStyle=!0),g[y]=x??null}),h.push({animation:g})}let p=!!h.length;return n&&(l.initial===!1||l.initial===l.animate)&&!i.manuallyAnimateOnMount&&(p=!1),n=!1,p?t(h):Promise.resolve()}function a(u,l){var h;if(e[u].isActive===l)return Promise.resolve();(h=i.variantChildren)==null||h.forEach(f=>{var d;return(d=f.animationState)==null?void 0:d.setActive(u,l)}),e[u].isActive=l;const c=o(u);for(const f in e)e[f].protectedKeys={};return c}return{animateChanges:o,setActive:a,setAnimateFunction:r,getState:()=>e,reset:()=>{e=Mr(),n=!0}}}function yf(i,t){return typeof t=="string"?t!==i:Array.isArray(t)?!za(t,i):!1}function ne(i=!1){return{isActive:i,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function Mr(){return{animate:ne(!0),whileInView:ne(),whileHover:ne(),whileTap:ne(),whileDrag:ne(),whileFocus:ne(),exit:ne()}}class Jt{constructor(t){this.isMounted=!1,this.node=t}update(){}}class _f extends Jt{constructor(t){super(t),t.animationState||(t.animationState=gf(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();Gi(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){var t;this.node.animationState.reset(),(t=this.unmountControls)==null||t.call(this)}}let vf=0;class xf extends Jt{constructor(){super(...arguments),this.id=vf++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:n}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===n)return;const s=this.node.animationState.setActive("exit",!t);e&&!t&&s.then(()=>{e(this.id)})}mount(){const{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}const Tf={animation:{Feature:_f},exit:{Feature:xf}};function ui(i,t,e,n={passive:!0}){return i.addEventListener(t,e,n),()=>i.removeEventListener(t,e)}function vi(i){return{point:{x:i.pageX,y:i.pageY}}}const bf=i=>t=>As(t)&&i(t,vi(t));function He(i,t,e,n){return ui(i,t,bf(e),n)}function Wa({top:i,left:t,right:e,bottom:n}){return{x:{min:t,max:e},y:{min:i,max:n}}}function Pf({x:i,y:t}){return{top:t.min,right:i.max,bottom:t.max,left:i.min}}function wf(i,t){if(!t)return i;const e=t({x:i.left,y:i.top}),n=t({x:i.right,y:i.bottom});return{top:e.y,left:e.x,bottom:n.y,right:n.x}}const Ka=1e-4,Sf=1-Ka,Af=1+Ka,$a=.01,Cf=0-$a,Mf=0+$a;function rt(i){return i.max-i.min}function Df(i,t,e){return Math.abs(i-t)<=e}function Dr(i,t,e,n=.5){i.origin=n,i.originPoint=I(t.min,t.max,i.origin),i.scale=rt(e)/rt(t),i.translate=I(e.min,e.max,i.origin)-i.originPoint,(i.scale>=Sf&&i.scale<=Af||isNaN(i.scale))&&(i.scale=1),(i.translate>=Cf&&i.translate<=Mf||isNaN(i.translate))&&(i.translate=0)}function Ze(i,t,e,n){Dr(i.x,t.x,e.x,n?n.originX:void 0),Dr(i.y,t.y,e.y,n?n.originY:void 0)}function Vr(i,t,e){i.min=e.min+t.min,i.max=i.min+rt(t)}function Vf(i,t,e){Vr(i.x,t.x,e.x),Vr(i.y,t.y,e.y)}function Rr(i,t,e){i.min=t.min-e.min,i.max=i.min+rt(t)}function Qe(i,t,e){Rr(i.x,t.x,e.x),Rr(i.y,t.y,e.y)}const Er=()=>({translate:0,scale:1,origin:0,originPoint:0}),be=()=>({x:Er(),y:Er()}),kr=()=>({min:0,max:0}),$=()=>({x:kr(),y:kr()});function vt(i){return[i("x"),i("y")]}function on(i){return i===void 0||i===1}function Fn({scale:i,scaleX:t,scaleY:e}){return!on(i)||!on(t)||!on(e)}function oe(i){return Fn(i)||Ya(i)||i.z||i.rotate||i.rotateX||i.rotateY||i.skewX||i.skewY}function Ya(i){return Or(i.x)||Or(i.y)}function Or(i){return i&&i!=="0%"}function Bi(i,t,e){const n=i-e,s=t*n;return e+s}function Lr(i,t,e,n,s){return s!==void 0&&(i=Bi(i,s,n)),Bi(i,e,n)+t}function Bn(i,t=0,e=1,n,s){i.min=Lr(i.min,t,e,n,s),i.max=Lr(i.max,t,e,n,s)}function Ga(i,{x:t,y:e}){Bn(i.x,t.translate,t.scale,t.originPoint),Bn(i.y,e.translate,e.scale,e.originPoint)}const Fr=.999999999999,Br=1.0000000000001;function Rf(i,t,e,n=!1){const s=e.length;if(!s)return;t.x=t.y=1;let r,o;for(let a=0;a<s;a++){r=e[a],o=r.projectionDelta;const{visualElement:u}=r.options;u&&u.props.style&&u.props.style.display==="contents"||(n&&r.options.layoutScroll&&r.scroll&&r!==r.root&&we(i,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),o&&(t.x*=o.x.scale,t.y*=o.y.scale,Ga(i,o)),n&&oe(r.latestValues)&&we(i,r.latestValues))}t.x<Br&&t.x>Fr&&(t.x=1),t.y<Br&&t.y>Fr&&(t.y=1)}function Pe(i,t){i.min=i.min+t,i.max=i.max+t}function Ir(i,t,e,n,s=.5){const r=I(i.min,i.max,s);Bn(i,t,e,r,n)}function we(i,t){Ir(i.x,t.x,t.scaleX,t.scale,t.originX),Ir(i.y,t.y,t.scaleY,t.scale,t.originY)}function Xa(i,t){return Wa(wf(i.getBoundingClientRect(),t))}function Ef(i,t,e){const n=Xa(i,e),{scroll:s}=t;return s&&(Pe(n.x,s.offset.x),Pe(n.y,s.offset.y)),n}const qa=({current:i})=>i?i.ownerDocument.defaultView:null,jr=(i,t)=>Math.abs(i-t);function kf(i,t){const e=jr(i.x,t.x),n=jr(i.y,t.y);return Math.sqrt(e**2+n**2)}class Ha{constructor(t,e,{transformPagePoint:n,contextWindow:s,dragSnapToOrigin:r=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const h=un(this.lastMoveEventInfo,this.history),f=this.startEvent!==null,d=kf(h.offset,{x:0,y:0})>=3;if(!f&&!d)return;const{point:m}=h,{timestamp:p}=J;this.history.push({...m,timestamp:p});const{onStart:g,onMove:y}=this.handlers;f||(g&&g(this.lastMoveEvent,h),this.startEvent=this.lastMoveEvent),y&&y(this.lastMoveEvent,h)},this.handlePointerMove=(h,f)=>{this.lastMoveEvent=h,this.lastMoveEventInfo=an(f,this.transformPagePoint),N.update(this.updatePoint,!0)},this.handlePointerUp=(h,f)=>{this.end();const{onEnd:d,onSessionEnd:m,resumeAnimation:p}=this.handlers;if(this.dragSnapToOrigin&&p&&p(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const g=un(h.type==="pointercancel"?this.lastMoveEventInfo:an(f,this.transformPagePoint),this.history);this.startEvent&&d&&d(h,g),m&&m(h,g)},!As(t))return;this.dragSnapToOrigin=r,this.handlers=e,this.transformPagePoint=n,this.contextWindow=s||window;const o=vi(t),a=an(o,this.transformPagePoint),{point:u}=a,{timestamp:l}=J;this.history=[{...u,timestamp:l}];const{onSessionStart:c}=e;c&&c(t,un(a,this.history)),this.removeListeners=gi(He(this.contextWindow,"pointermove",this.handlePointerMove),He(this.contextWindow,"pointerup",this.handlePointerUp),He(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),qt(this.updatePoint)}}function an(i,t){return t?{point:t(i.point)}:i}function Nr(i,t){return{x:i.x-t.x,y:i.y-t.y}}function un({point:i},t){return{point:i,delta:Nr(i,Za(t)),offset:Nr(i,Of(t)),velocity:Lf(t,.1)}}function Of(i){return i[0]}function Za(i){return i[i.length-1]}function Lf(i,t){if(i.length<2)return{x:0,y:0};let e=i.length-1,n=null;const s=Za(i);for(;e>=0&&(n=i[e],!(s.timestamp-n.timestamp>Dt(t)));)e--;if(!n)return{x:0,y:0};const r=Vt(s.timestamp-n.timestamp);if(r===0)return{x:0,y:0};const o={x:(s.x-n.x)/r,y:(s.y-n.y)/r};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}function Ff(i,{min:t,max:e},n){return t!==void 0&&i<t?i=n?I(t,i,n.min):Math.max(i,t):e!==void 0&&i>e&&(i=n?I(e,i,n.max):Math.min(i,e)),i}function zr(i,t,e){return{min:t!==void 0?i.min+t:void 0,max:e!==void 0?i.max+e-(i.max-i.min):void 0}}function Bf(i,{top:t,left:e,bottom:n,right:s}){return{x:zr(i.x,e,s),y:zr(i.y,t,n)}}function Ur(i,t){let e=t.min-i.min,n=t.max-i.max;return t.max-t.min<i.max-i.min&&([e,n]=[n,e]),{min:e,max:n}}function If(i,t){return{x:Ur(i.x,t.x),y:Ur(i.y,t.y)}}function jf(i,t){let e=.5;const n=rt(i),s=rt(t);return s>n?e=ii(t.min,t.max-n,i.min):n>s&&(e=ii(i.min,i.max-s,t.min)),It(0,1,e)}function Nf(i,t){const e={};return t.min!==void 0&&(e.min=t.min-i.min),t.max!==void 0&&(e.max=t.max-i.min),e}const In=.35;function zf(i=In){return i===!1?i=0:i===!0&&(i=In),{x:Wr(i,"left","right"),y:Wr(i,"top","bottom")}}function Wr(i,t,e){return{min:Kr(i,t),max:Kr(i,e)}}function Kr(i,t){return typeof i=="number"?i:i[t]||0}const Uf=new WeakMap;class Wf{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=$(),this.visualElement=t}start(t,{snapToCursor:e=!1}={}){const{presenceContext:n}=this.visualElement;if(n&&n.isPresent===!1)return;const s=c=>{const{dragSnapToOrigin:h}=this.getProps();h?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(vi(c).point)},r=(c,h)=>{const{drag:f,dragPropagation:d,onDragStart:m}=this.getProps();if(f&&!d&&(this.openDragLock&&this.openDragLock(),this.openDragLock=ih(f),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),vt(g=>{let y=this.getAxisMotionValue(g).get()||0;if(Rt.test(y)){const{projection:x}=this.visualElement;if(x&&x.layout){const v=x.layout.layoutBox[g];v&&(y=rt(v)*(parseFloat(y)/100))}}this.originPoint[g]=y}),m&&N.postRender(()=>m(c,h)),On(this.visualElement,"transform");const{animationState:p}=this.visualElement;p&&p.setActive("whileDrag",!0)},o=(c,h)=>{const{dragPropagation:f,dragDirectionLock:d,onDirectionLock:m,onDrag:p}=this.getProps();if(!f&&!this.openDragLock)return;const{offset:g}=h;if(d&&this.currentDirection===null){this.currentDirection=Kf(g),this.currentDirection!==null&&m&&m(this.currentDirection);return}this.updateAxis("x",h.point,g),this.updateAxis("y",h.point,g),this.visualElement.render(),p&&p(c,h)},a=(c,h)=>this.stop(c,h),u=()=>vt(c=>{var h;return this.getAnimationState(c)==="paused"&&((h=this.getAxisMotionValue(c).animation)==null?void 0:h.play())}),{dragSnapToOrigin:l}=this.getProps();this.panSession=new Ha(t,{onSessionStart:s,onStart:r,onMove:o,onSessionEnd:a,resumeAnimation:u},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:l,contextWindow:qa(this.visualElement)})}stop(t,e){const n=this.isDragging;if(this.cancel(),!n)return;const{velocity:s}=e;this.startAnimation(s);const{onDragEnd:r}=this.getProps();r&&N.postRender(()=>r(t,e))}cancel(){this.isDragging=!1;const{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:n}=this.getProps();!n&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,n){const{drag:s}=this.getProps();if(!n||!wi(t,s,this.currentDirection))return;const r=this.getAxisMotionValue(t);let o=this.originPoint[t]+n[t];this.constraints&&this.constraints[t]&&(o=Ff(o,this.constraints[t],this.elastic[t])),r.set(o)}resolveConstraints(){var r;const{dragConstraints:t,dragElastic:e}=this.getProps(),n=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(r=this.visualElement.projection)==null?void 0:r.layout,s=this.constraints;t&&Te(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&n?this.constraints=Bf(n.layoutBox,t):this.constraints=!1,this.elastic=zf(e),s!==this.constraints&&n&&this.constraints&&!this.hasMutatedConstraints&&vt(o=>{this.constraints!==!1&&this.getAxisMotionValue(o)&&(this.constraints[o]=Nf(n.layoutBox[o],this.constraints[o]))})}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:e}=this.getProps();if(!t||!Te(t))return!1;const n=t.current,{projection:s}=this.visualElement;if(!s||!s.layout)return!1;const r=Ef(n,s.root,this.visualElement.getTransformPagePoint());let o=If(s.layout.layoutBox,r);if(e){const a=e(Pf(o));this.hasMutatedConstraints=!!a,a&&(o=Wa(a))}return o}startAnimation(t){const{drag:e,dragMomentum:n,dragElastic:s,dragTransition:r,dragSnapToOrigin:o,onDragTransitionEnd:a}=this.getProps(),u=this.constraints||{},l=vt(c=>{if(!wi(c,e,this.currentDirection))return;let h=u&&u[c]||{};o&&(h={min:0,max:0});const f=s?200:1e6,d=s?40:1e7,m={type:"inertia",velocity:n?t[c]:0,bounceStiffness:f,bounceDamping:d,timeConstant:750,restDelta:1,restSpeed:10,...r,...h};return this.startAxisValueAnimation(c,m)});return Promise.all(l).then(a)}startAxisValueAnimation(t,e){const n=this.getAxisMotionValue(t);return On(this.visualElement,t),n.start(Fs(t,n,0,e,this.visualElement,!1))}stopAnimation(){vt(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){vt(t=>{var e;return(e=this.getAxisMotionValue(t).animation)==null?void 0:e.pause()})}getAnimationState(t){var e;return(e=this.getAxisMotionValue(t).animation)==null?void 0:e.state}getAxisMotionValue(t){const e=`_drag${t.toUpperCase()}`,n=this.visualElement.getProps(),s=n[e];return s||this.visualElement.getValue(t,(n.initial?n.initial[t]:void 0)||0)}snapToCursor(t){vt(e=>{const{drag:n}=this.getProps();if(!wi(e,n,this.currentDirection))return;const{projection:s}=this.visualElement,r=this.getAxisMotionValue(e);if(s&&s.layout){const{min:o,max:a}=s.layout.layoutBox[e];r.set(t[e]-I(o,a,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:e}=this.getProps(),{projection:n}=this.visualElement;if(!Te(e)||!n||!this.constraints)return;this.stopAnimation();const s={x:0,y:0};vt(o=>{const a=this.getAxisMotionValue(o);if(a&&this.constraints!==!1){const u=a.get();s[o]=jf({min:u,max:u},this.constraints[o])}});const{transformTemplate:r}=this.visualElement.getProps();this.visualElement.current.style.transform=r?r({},""):"none",n.root&&n.root.updateScroll(),n.updateLayout(),this.resolveConstraints(),vt(o=>{if(!wi(o,t,null))return;const a=this.getAxisMotionValue(o),{min:u,max:l}=this.constraints[o];a.set(I(u,l,s[o]))})}addListeners(){if(!this.visualElement.current)return;Uf.set(this.visualElement,this);const t=this.visualElement.current,e=He(t,"pointerdown",u=>{const{drag:l,dragListener:c=!0}=this.getProps();l&&c&&this.start(u)}),n=()=>{const{dragConstraints:u}=this.getProps();Te(u)&&u.current&&(this.constraints=this.resolveRefConstraints())},{projection:s}=this.visualElement,r=s.addEventListener("measure",n);s&&!s.layout&&(s.root&&s.root.updateScroll(),s.updateLayout()),N.read(n);const o=ui(window,"resize",()=>this.scalePositionWithinConstraints()),a=s.addEventListener("didUpdate",({delta:u,hasLayoutChanged:l})=>{this.isDragging&&l&&(vt(c=>{const h=this.getAxisMotionValue(c);h&&(this.originPoint[c]+=u[c].translate,h.set(h.get()+u[c].translate))}),this.visualElement.render())});return()=>{o(),e(),r(),a&&a()}}getProps(){const t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:n=!1,dragPropagation:s=!1,dragConstraints:r=!1,dragElastic:o=In,dragMomentum:a=!0}=t;return{...t,drag:e,dragDirectionLock:n,dragPropagation:s,dragConstraints:r,dragElastic:o,dragMomentum:a}}}function wi(i,t,e){return(t===!0||t===i)&&(e===null||e===i)}function Kf(i,t=10){let e=null;return Math.abs(i.y)>t?e="y":Math.abs(i.x)>t&&(e="x"),e}class $f extends Jt{constructor(t){super(t),this.removeGroupControls=wt,this.removeListeners=wt,this.controls=new Wf(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||wt}unmount(){this.removeGroupControls(),this.removeListeners()}}const $r=i=>(t,e)=>{i&&N.postRender(()=>i(t,e))};class Yf extends Jt{constructor(){super(...arguments),this.removePointerDownListener=wt}onPointerDown(t){this.session=new Ha(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:qa(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:e,onPan:n,onPanEnd:s}=this.node.getProps();return{onSessionStart:$r(t),onStart:$r(e),onMove:n,onEnd:(r,o)=>{delete this.session,s&&N.postRender(()=>s(r,o))}}}mount(){this.removePointerDownListener=He(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}const Di={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function Yr(i,t){return t.max===t.min?0:i/(t.max-t.min)*100}const We={correct:(i,t)=>{if(!t.target)return i;if(typeof i=="string")if(C.test(i))i=parseFloat(i);else return i;const e=Yr(i,t.target.x),n=Yr(i,t.target.y);return`${e}% ${n}%`}},Gf={correct:(i,{treeScale:t,projectionDelta:e})=>{const n=i,s=Ht.parse(i);if(s.length>5)return n;const r=Ht.createTransformer(i),o=typeof s[0]!="number"?1:0,a=e.x.scale*t.x,u=e.y.scale*t.y;s[0+o]/=a,s[1+o]/=u;const l=I(a,u,.5);return typeof s[2+o]=="number"&&(s[2+o]/=l),typeof s[3+o]=="number"&&(s[3+o]/=l),r(s)}};class Xf extends A.Component{componentDidMount(){const{visualElement:t,layoutGroup:e,switchLayoutGroup:n,layoutId:s}=this.props,{projection:r}=t;Rh(qf),r&&(e.group&&e.group.add(r),n&&n.register&&s&&n.register(r),r.root.didUpdate(),r.addEventListener("animationComplete",()=>{this.safeToRemove()}),r.setOptions({...r.options,onExitComplete:()=>this.safeToRemove()})),Di.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:e,visualElement:n,drag:s,isPresent:r}=this.props,{projection:o}=n;return o&&(o.isPresent=r,s||t.layoutDependency!==e||e===void 0||t.isPresent!==r?o.willUpdate():this.safeToRemove(),t.isPresent!==r&&(r?o.promote():o.relegate()||N.postRender(()=>{const a=o.getStack();(!a||!a.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),Ss.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:t,layoutGroup:e,switchLayoutGroup:n}=this.props,{projection:s}=t;s&&(s.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(s),n&&n.deregister&&n.deregister(s))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}}function Qa(i){const[t,e]=Sa(),n=A.useContext(is);return Bt.jsx(Xf,{...i,layoutGroup:n,switchLayoutGroup:A.useContext(Va),isPresent:t,safeToRemove:e})}const qf={borderRadius:{...We,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:We,borderTopRightRadius:We,borderBottomLeftRadius:We,borderBottomRightRadius:We,boxShadow:Gf};function Hf(i,t,e){const n=it(i)?i:Ve(i);return n.start(Fs("",n,t,e)),n.animation}const Zf=(i,t)=>i.depth-t.depth;class Qf{constructor(){this.children=[],this.isDirty=!1}add(t){rs(this.children,t),this.isDirty=!0}remove(t){os(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(Zf),this.isDirty=!1,this.children.forEach(t)}}function Jf(i,t){const e=ot.now(),n=({timestamp:s})=>{const r=s-e;r>=t&&(qt(n),i(r-t))};return N.setup(n,!0),()=>qt(n)}const Ja=["TopLeft","TopRight","BottomLeft","BottomRight"],td=Ja.length,Gr=i=>typeof i=="string"?parseFloat(i):i,Xr=i=>typeof i=="number"||C.test(i);function ed(i,t,e,n,s,r){s?(i.opacity=I(0,e.opacity??1,id(n)),i.opacityExit=I(t.opacity??1,0,nd(n))):r&&(i.opacity=I(t.opacity??1,e.opacity??1,n));for(let o=0;o<td;o++){const a=`border${Ja[o]}Radius`;let u=qr(t,a),l=qr(e,a);if(u===void 0&&l===void 0)continue;u||(u=0),l||(l=0),u===0||l===0||Xr(u)===Xr(l)?(i[a]=Math.max(I(Gr(u),Gr(l),n),0),(Rt.test(l)||Rt.test(u))&&(i[a]+="%")):i[a]=l}(t.rotate||e.rotate)&&(i.rotate=I(t.rotate||0,e.rotate||0,n))}function qr(i,t){return i[t]!==void 0?i[t]:i.borderRadius}const id=tu(0,.5,Yo),nd=tu(.5,.95,wt);function tu(i,t,e){return n=>n<i?0:n>t?1:e(ii(i,t,n))}function Hr(i,t){i.min=t.min,i.max=t.max}function _t(i,t){Hr(i.x,t.x),Hr(i.y,t.y)}function Zr(i,t){i.translate=t.translate,i.scale=t.scale,i.originPoint=t.originPoint,i.origin=t.origin}function Qr(i,t,e,n,s){return i-=t,i=Bi(i,1/e,n),s!==void 0&&(i=Bi(i,1/s,n)),i}function sd(i,t=0,e=1,n=.5,s,r=i,o=i){if(Rt.test(t)&&(t=parseFloat(t),t=I(o.min,o.max,t/100)-o.min),typeof t!="number")return;let a=I(r.min,r.max,n);i===r&&(a-=t),i.min=Qr(i.min,t,e,a,s),i.max=Qr(i.max,t,e,a,s)}function Jr(i,t,[e,n,s],r,o){sd(i,t[e],t[n],t[s],t.scale,r,o)}const rd=["x","scaleX","originX"],od=["y","scaleY","originY"];function to(i,t,e,n){Jr(i.x,t,rd,e?e.x:void 0,n?n.x:void 0),Jr(i.y,t,od,e?e.y:void 0,n?n.y:void 0)}function eo(i){return i.translate===0&&i.scale===1}function eu(i){return eo(i.x)&&eo(i.y)}function io(i,t){return i.min===t.min&&i.max===t.max}function ad(i,t){return io(i.x,t.x)&&io(i.y,t.y)}function no(i,t){return Math.round(i.min)===Math.round(t.min)&&Math.round(i.max)===Math.round(t.max)}function iu(i,t){return no(i.x,t.x)&&no(i.y,t.y)}function so(i){return rt(i.x)/rt(i.y)}function ro(i,t){return i.translate===t.translate&&i.scale===t.scale&&i.originPoint===t.originPoint}class ud{constructor(){this.members=[]}add(t){rs(this.members,t),t.scheduleRender()}remove(t){if(os(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const e=this.members[this.members.length-1];e&&this.promote(e)}}relegate(t){const e=this.members.findIndex(s=>t===s);if(e===0)return!1;let n;for(let s=e;s>=0;s--){const r=this.members[s];if(r.isPresent!==!1){n=r;break}}return n?(this.promote(n),!0):!1}promote(t,e){const n=this.lead;if(t!==n&&(this.prevLead=n,this.lead=t,t.show(),n)){n.instance&&n.scheduleRender(),t.scheduleRender(),t.resumeFrom=n,e&&(t.resumeFrom.preserveOpacity=!0),n.snapshot&&(t.snapshot=n.snapshot,t.snapshot.latestValues=n.animationValues||n.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:s}=t.options;s===!1&&n.hide()}}exitAnimationComplete(){this.members.forEach(t=>{const{options:e,resumingFrom:n}=t;e.onExitComplete&&e.onExitComplete(),n&&n.options.onExitComplete&&n.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function ld(i,t,e){let n="";const s=i.x.translate/t.x,r=i.y.translate/t.y,o=(e==null?void 0:e.z)||0;if((s||r||o)&&(n=`translate3d(${s}px, ${r}px, ${o}px) `),(t.x!==1||t.y!==1)&&(n+=`scale(${1/t.x}, ${1/t.y}) `),e){const{transformPerspective:l,rotate:c,rotateX:h,rotateY:f,skewX:d,skewY:m}=e;l&&(n=`perspective(${l}px) ${n}`),c&&(n+=`rotate(${c}deg) `),h&&(n+=`rotateX(${h}deg) `),f&&(n+=`rotateY(${f}deg) `),d&&(n+=`skewX(${d}deg) `),m&&(n+=`skewY(${m}deg) `)}const a=i.x.scale*t.x,u=i.y.scale*t.y;return(a!==1||u!==1)&&(n+=`scale(${a}, ${u})`),n||"none"}const ln=["","X","Y","Z"],cd={visibility:"hidden"},hd=1e3;let fd=0;function cn(i,t,e,n){const{latestValues:s}=t;s[i]&&(e[i]=s[i],t.setStaticValue(i,0),n&&(n[i]=0))}function nu(i){if(i.hasCheckedOptimisedAppear=!0,i.root===i)return;const{visualElement:t}=i.options;if(!t)return;const e=ja(t);if(window.MotionHasOptimisedAnimation(e,"transform")){const{layout:s,layoutId:r}=i.options;window.MotionCancelOptimisedAnimation(e,"transform",N,!(s||r))}const{parent:n}=i;n&&!n.hasCheckedOptimisedAppear&&nu(n)}function su({attachResizeListener:i,defaultParent:t,measureScroll:e,checkIsScrollRoot:n,resetTransform:s}){return class{constructor(o={},a=t==null?void 0:t()){this.id=fd++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,this.nodes.forEach(md),this.nodes.forEach(xd),this.nodes.forEach(Td),this.nodes.forEach(gd)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=o,this.root=a?a.root||a:this,this.path=a?[...a.path,a]:[],this.parent=a,this.depth=a?a.depth+1:0;for(let u=0;u<this.path.length;u++)this.path[u].shouldResetTransform=!0;this.root===this&&(this.nodes=new Qf)}addEventListener(o,a){return this.eventHandlers.has(o)||this.eventHandlers.set(o,new ls),this.eventHandlers.get(o).add(a)}notifyListeners(o,...a){const u=this.eventHandlers.get(o);u&&u.notify(...a)}hasListeners(o){return this.eventHandlers.has(o)}mount(o){if(this.instance)return;this.isSVG=wa(o)&&!uh(o),this.instance=o;const{layoutId:a,layout:u,visualElement:l}=this.options;if(l&&!l.current&&l.mount(o),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(u||a)&&(this.isLayoutDirty=!0),i){let c;const h=()=>this.root.updateBlockedByResize=!1;i(o,()=>{this.root.updateBlockedByResize=!0,c&&c(),c=Jf(h,250),Di.hasAnimatedSinceResize&&(Di.hasAnimatedSinceResize=!1,this.nodes.forEach(ao))})}a&&this.root.registerSharedNode(a,this),this.options.animate!==!1&&l&&(a||u)&&this.addEventListener("didUpdate",({delta:c,hasLayoutChanged:h,hasRelativeLayoutChanged:f,layout:d})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const m=this.options.transition||l.getDefaultTransition()||Ad,{onLayoutAnimationStart:p,onLayoutAnimationComplete:g}=l.getProps(),y=!this.targetLayout||!iu(this.targetLayout,d),x=!h&&f;if(this.options.layoutRoot||this.resumeFrom||x||h&&(y||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);const v={...Ps(m,"layout"),onPlay:p,onComplete:g};(l.shouldReduceMotion||this.options.layoutRoot)&&(v.delay=0,v.type=!1),this.startAnimation(v),this.setAnimationOrigin(c,x)}else h||ao(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=d})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const o=this.getStack();o&&o.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),qt(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(bd),this.animationId++)}getTransformTemplate(){const{visualElement:o}=this.options;return o&&o.getProps().transformTemplate}willUpdate(o=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&nu(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let c=0;c<this.path.length;c++){const h=this.path[c];h.shouldResetTransform=!0,h.updateScroll("snapshot"),h.options.layoutRoot&&h.willUpdate(!1)}const{layoutId:a,layout:u}=this.options;if(a===void 0&&!u)return;const l=this.getTransformTemplate();this.prevTransformTemplateValue=l?l(this.latestValues,""):void 0,this.updateSnapshot(),o&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(oo);return}this.isUpdating||this.nodes.forEach(_d),this.isUpdating=!1,this.nodes.forEach(vd),this.nodes.forEach(dd),this.nodes.forEach(pd),this.clearAllSnapshots();const a=ot.now();J.delta=It(0,1e3/60,a-J.timestamp),J.timestamp=a,J.isProcessing=!0,Ji.update.process(J),Ji.preRender.process(J),Ji.render.process(J),J.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,Ss.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(yd),this.sharedNodes.forEach(Pd)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,N.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){N.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure(),this.snapshot&&!rt(this.snapshot.measuredBox.x)&&!rt(this.snapshot.measuredBox.y)&&(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let u=0;u<this.path.length;u++)this.path[u].updateScroll();const o=this.layout;this.layout=this.measure(!1),this.layoutCorrected=$(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:a}=this.options;a&&a.notify("LayoutMeasure",this.layout.layoutBox,o?o.layoutBox:void 0)}updateScroll(o="measure"){let a=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===o&&(a=!1),a&&this.instance){const u=n(this.instance);this.scroll={animationId:this.root.animationId,phase:o,isRoot:u,offset:e(this.instance),wasRoot:this.scroll?this.scroll.isRoot:u}}}resetTransform(){if(!s)return;const o=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,a=this.projectionDelta&&!eu(this.projectionDelta),u=this.getTransformTemplate(),l=u?u(this.latestValues,""):void 0,c=l!==this.prevTransformTemplateValue;o&&this.instance&&(a||oe(this.latestValues)||c)&&(s(this.instance,l),this.shouldResetTransform=!1,this.scheduleRender())}measure(o=!0){const a=this.measurePageBox();let u=this.removeElementScroll(a);return o&&(u=this.removeTransform(u)),Cd(u),{animationId:this.root.animationId,measuredBox:a,layoutBox:u,latestValues:{},source:this.id}}measurePageBox(){var l;const{visualElement:o}=this.options;if(!o)return $();const a=o.measureViewportBox();if(!(((l=this.scroll)==null?void 0:l.wasRoot)||this.path.some(Md))){const{scroll:c}=this.root;c&&(Pe(a.x,c.offset.x),Pe(a.y,c.offset.y))}return a}removeElementScroll(o){var u;const a=$();if(_t(a,o),(u=this.scroll)!=null&&u.wasRoot)return a;for(let l=0;l<this.path.length;l++){const c=this.path[l],{scroll:h,options:f}=c;c!==this.root&&h&&f.layoutScroll&&(h.wasRoot&&_t(a,o),Pe(a.x,h.offset.x),Pe(a.y,h.offset.y))}return a}applyTransform(o,a=!1){const u=$();_t(u,o);for(let l=0;l<this.path.length;l++){const c=this.path[l];!a&&c.options.layoutScroll&&c.scroll&&c!==c.root&&we(u,{x:-c.scroll.offset.x,y:-c.scroll.offset.y}),oe(c.latestValues)&&we(u,c.latestValues)}return oe(this.latestValues)&&we(u,this.latestValues),u}removeTransform(o){const a=$();_t(a,o);for(let u=0;u<this.path.length;u++){const l=this.path[u];if(!l.instance||!oe(l.latestValues))continue;Fn(l.latestValues)&&l.updateSnapshot();const c=$(),h=l.measurePageBox();_t(c,h),to(a,l.latestValues,l.snapshot?l.snapshot.layoutBox:void 0,c)}return oe(this.latestValues)&&to(a,this.latestValues),a}setTargetDelta(o){this.targetDelta=o,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(o){this.options={...this.options,...o,crossfade:o.crossfade!==void 0?o.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==J.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(o=!1){var f;const a=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=a.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=a.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=a.isSharedProjectionDirty);const u=!!this.resumingFrom||this!==a;if(!(o||u&&this.isSharedProjectionDirty||this.isProjectionDirty||(f=this.parent)!=null&&f.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:c,layoutId:h}=this.options;if(!(!this.layout||!(c||h))){if(this.resolvedRelativeTargetAt=J.timestamp,!this.targetDelta&&!this.relativeTarget){const d=this.getClosestProjectingParent();d&&d.layout&&this.animationProgress!==1?(this.relativeParent=d,this.forceRelativeParentToResolveTarget(),this.relativeTarget=$(),this.relativeTargetOrigin=$(),Qe(this.relativeTargetOrigin,this.layout.layoutBox,d.layout.layoutBox),_t(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)&&(this.target||(this.target=$(),this.targetWithTransforms=$()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),Vf(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):_t(this.target,this.layout.layoutBox),Ga(this.target,this.targetDelta)):_t(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget)){this.attemptToResolveRelativeTarget=!1;const d=this.getClosestProjectingParent();d&&!!d.resumingFrom==!!this.resumingFrom&&!d.options.layoutScroll&&d.target&&this.animationProgress!==1?(this.relativeParent=d,this.forceRelativeParentToResolveTarget(),this.relativeTarget=$(),this.relativeTargetOrigin=$(),Qe(this.relativeTargetOrigin,this.target,d.target),_t(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}}}getClosestProjectingParent(){if(!(!this.parent||Fn(this.parent.latestValues)||Ya(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var m;const o=this.getLead(),a=!!this.resumingFrom||this!==o;let u=!0;if((this.isProjectionDirty||(m=this.parent)!=null&&m.isProjectionDirty)&&(u=!1),a&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(u=!1),this.resolvedRelativeTargetAt===J.timestamp&&(u=!1),u)return;const{layout:l,layoutId:c}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(l||c))return;_t(this.layoutCorrected,this.layout.layoutBox);const h=this.treeScale.x,f=this.treeScale.y;Rf(this.layoutCorrected,this.treeScale,this.path,a),o.layout&&!o.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(o.target=o.layout.layoutBox,o.targetWithTransforms=$());const{target:d}=o;if(!d){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}!this.projectionDelta||!this.prevProjectionDelta?this.createProjectionDeltas():(Zr(this.prevProjectionDelta.x,this.projectionDelta.x),Zr(this.prevProjectionDelta.y,this.projectionDelta.y)),Ze(this.projectionDelta,this.layoutCorrected,d,this.latestValues),(this.treeScale.x!==h||this.treeScale.y!==f||!ro(this.projectionDelta.x,this.prevProjectionDelta.x)||!ro(this.projectionDelta.y,this.prevProjectionDelta.y))&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",d))}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(o=!0){var a;if((a=this.options.visualElement)==null||a.scheduleRender(),o){const u=this.getStack();u&&u.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=be(),this.projectionDelta=be(),this.projectionDeltaWithTransform=be()}setAnimationOrigin(o,a=!1){const u=this.snapshot,l=u?u.latestValues:{},c={...this.latestValues},h=be();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!a;const f=$(),d=u?u.source:void 0,m=this.layout?this.layout.source:void 0,p=d!==m,g=this.getStack(),y=!g||g.members.length<=1,x=!!(p&&!y&&this.options.crossfade===!0&&!this.path.some(Sd));this.animationProgress=0;let v;this.mixTargetDelta=T=>{const _=T/1e3;uo(h.x,o.x,_),uo(h.y,o.y,_),this.setTargetDelta(h),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(Qe(f,this.layout.layoutBox,this.relativeParent.layout.layoutBox),wd(this.relativeTarget,this.relativeTargetOrigin,f,_),v&&ad(this.relativeTarget,v)&&(this.isProjectionDirty=!1),v||(v=$()),_t(v,this.relativeTarget)),p&&(this.animationValues=c,ed(c,l,this.latestValues,_,x,y)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=_},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(o){var a,u,l;this.notifyListeners("animationStart"),(a=this.currentAnimation)==null||a.stop(),(l=(u=this.resumingFrom)==null?void 0:u.currentAnimation)==null||l.stop(),this.pendingAnimation&&(qt(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=N.update(()=>{Di.hasAnimatedSinceResize=!0,this.motionValue||(this.motionValue=Ve(0)),this.currentAnimation=Hf(this.motionValue,[0,1e3],{...o,velocity:0,isSync:!0,onUpdate:c=>{this.mixTargetDelta(c),o.onUpdate&&o.onUpdate(c)},onStop:()=>{},onComplete:()=>{o.onComplete&&o.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const o=this.getStack();o&&o.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(hd),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const o=this.getLead();let{targetWithTransforms:a,target:u,layout:l,latestValues:c}=o;if(!(!a||!u||!l)){if(this!==o&&this.layout&&l&&ru(this.options.animationType,this.layout.layoutBox,l.layoutBox)){u=this.target||$();const h=rt(this.layout.layoutBox.x);u.x.min=o.target.x.min,u.x.max=u.x.min+h;const f=rt(this.layout.layoutBox.y);u.y.min=o.target.y.min,u.y.max=u.y.min+f}_t(a,u),we(a,c),Ze(this.projectionDeltaWithTransform,this.layoutCorrected,a,c)}}registerSharedNode(o,a){this.sharedNodes.has(o)||this.sharedNodes.set(o,new ud),this.sharedNodes.get(o).add(a);const l=a.options.initialPromotionConfig;a.promote({transition:l?l.transition:void 0,preserveFollowOpacity:l&&l.shouldPreserveFollowOpacity?l.shouldPreserveFollowOpacity(a):void 0})}isLead(){const o=this.getStack();return o?o.lead===this:!0}getLead(){var a;const{layoutId:o}=this.options;return o?((a=this.getStack())==null?void 0:a.lead)||this:this}getPrevLead(){var a;const{layoutId:o}=this.options;return o?(a=this.getStack())==null?void 0:a.prevLead:void 0}getStack(){const{layoutId:o}=this.options;if(o)return this.root.sharedNodes.get(o)}promote({needsReset:o,transition:a,preserveFollowOpacity:u}={}){const l=this.getStack();l&&l.promote(this,u),o&&(this.projectionDelta=void 0,this.needsReset=!0),a&&this.setOptions({transition:a})}relegate(){const o=this.getStack();return o?o.relegate(this):!1}resetSkewAndRotation(){const{visualElement:o}=this.options;if(!o)return;let a=!1;const{latestValues:u}=o;if((u.z||u.rotate||u.rotateX||u.rotateY||u.rotateZ||u.skewX||u.skewY)&&(a=!0),!a)return;const l={};u.z&&cn("z",o,l,this.animationValues);for(let c=0;c<ln.length;c++)cn(`rotate${ln[c]}`,o,l,this.animationValues),cn(`skew${ln[c]}`,o,l,this.animationValues);o.render();for(const c in l)o.setStaticValue(c,l[c]),this.animationValues&&(this.animationValues[c]=l[c]);o.scheduleRender()}getProjectionStyles(o){if(!this.instance||this.isSVG)return;if(!this.isVisible)return cd;const a={visibility:""},u=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,a.opacity="",a.pointerEvents=Mi(o==null?void 0:o.pointerEvents)||"",a.transform=u?u(this.latestValues,""):"none",a;const l=this.getLead();if(!this.projectionDelta||!this.layout||!l.target){const d={};return this.options.layoutId&&(d.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,d.pointerEvents=Mi(o==null?void 0:o.pointerEvents)||""),this.hasProjected&&!oe(this.latestValues)&&(d.transform=u?u({},""):"none",this.hasProjected=!1),d}const c=l.animationValues||l.latestValues;this.applyTransformsToTarget(),a.transform=ld(this.projectionDeltaWithTransform,this.treeScale,c),u&&(a.transform=u(c,a.transform));const{x:h,y:f}=this.projectionDelta;a.transformOrigin=`${h.origin*100}% ${f.origin*100}% 0`,l.animationValues?a.opacity=l===this?c.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:c.opacityExit:a.opacity=l===this?c.opacity!==void 0?c.opacity:"":c.opacityExit!==void 0?c.opacityExit:0;for(const d in oi){if(c[d]===void 0)continue;const{correct:m,applyTo:p,isCSSVariable:g}=oi[d],y=a.transform==="none"?c[d]:m(c[d],l);if(p){const x=p.length;for(let v=0;v<x;v++)a[p[v]]=y}else g?this.options.visualElement.renderState.vars[d]=y:a[d]=y}return this.options.layoutId&&(a.pointerEvents=l===this?Mi(o==null?void 0:o.pointerEvents)||"":"none"),a}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(o=>{var a;return(a=o.currentAnimation)==null?void 0:a.stop()}),this.root.nodes.forEach(oo),this.root.sharedNodes.clear()}}}function dd(i){i.updateLayout()}function pd(i){var e;const t=((e=i.resumeFrom)==null?void 0:e.snapshot)||i.snapshot;if(i.isLead()&&i.layout&&t&&i.hasListeners("didUpdate")){const{layoutBox:n,measuredBox:s}=i.layout,{animationType:r}=i.options,o=t.source!==i.layout.source;r==="size"?vt(h=>{const f=o?t.measuredBox[h]:t.layoutBox[h],d=rt(f);f.min=n[h].min,f.max=f.min+d}):ru(r,t.layoutBox,n)&&vt(h=>{const f=o?t.measuredBox[h]:t.layoutBox[h],d=rt(n[h]);f.max=f.min+d,i.relativeTarget&&!i.currentAnimation&&(i.isProjectionDirty=!0,i.relativeTarget[h].max=i.relativeTarget[h].min+d)});const a=be();Ze(a,n,t.layoutBox);const u=be();o?Ze(u,i.applyTransform(s,!0),t.measuredBox):Ze(u,n,t.layoutBox);const l=!eu(a);let c=!1;if(!i.resumeFrom){const h=i.getClosestProjectingParent();if(h&&!h.resumeFrom){const{snapshot:f,layout:d}=h;if(f&&d){const m=$();Qe(m,t.layoutBox,f.layoutBox);const p=$();Qe(p,n,d.layoutBox),iu(m,p)||(c=!0),h.options.layoutRoot&&(i.relativeTarget=p,i.relativeTargetOrigin=m,i.relativeParent=h)}}}i.notifyListeners("didUpdate",{layout:n,snapshot:t,delta:u,layoutDelta:a,hasLayoutChanged:l,hasRelativeLayoutChanged:c})}else if(i.isLead()){const{onExitComplete:n}=i.options;n&&n()}i.options.transition=void 0}function md(i){i.parent&&(i.isProjecting()||(i.isProjectionDirty=i.parent.isProjectionDirty),i.isSharedProjectionDirty||(i.isSharedProjectionDirty=!!(i.isProjectionDirty||i.parent.isProjectionDirty||i.parent.isSharedProjectionDirty)),i.isTransformDirty||(i.isTransformDirty=i.parent.isTransformDirty))}function gd(i){i.isProjectionDirty=i.isSharedProjectionDirty=i.isTransformDirty=!1}function yd(i){i.clearSnapshot()}function oo(i){i.clearMeasurements()}function _d(i){i.isLayoutDirty=!1}function vd(i){const{visualElement:t}=i.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),i.resetTransform()}function ao(i){i.finishAnimation(),i.targetDelta=i.relativeTarget=i.target=void 0,i.isProjectionDirty=!0}function xd(i){i.resolveTargetDelta()}function Td(i){i.calcProjection()}function bd(i){i.resetSkewAndRotation()}function Pd(i){i.removeLeadSnapshot()}function uo(i,t,e){i.translate=I(t.translate,0,e),i.scale=I(t.scale,1,e),i.origin=t.origin,i.originPoint=t.originPoint}function lo(i,t,e,n){i.min=I(t.min,e.min,n),i.max=I(t.max,e.max,n)}function wd(i,t,e,n){lo(i.x,t.x,e.x,n),lo(i.y,t.y,e.y,n)}function Sd(i){return i.animationValues&&i.animationValues.opacityExit!==void 0}const Ad={duration:.45,ease:[.4,0,.1,1]},co=i=>typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(i),ho=co("applewebkit/")&&!co("chrome/")?Math.round:wt;function fo(i){i.min=ho(i.min),i.max=ho(i.max)}function Cd(i){fo(i.x),fo(i.y)}function ru(i,t,e){return i==="position"||i==="preserve-aspect"&&!Df(so(t),so(e),.2)}function Md(i){var t;return i!==i.root&&((t=i.scroll)==null?void 0:t.wasRoot)}const Dd=su({attachResizeListener:(i,t)=>ui(i,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),hn={current:void 0},ou=su({measureScroll:i=>({x:i.scrollLeft,y:i.scrollTop}),defaultParent:()=>{if(!hn.current){const i=new Dd({});i.mount(window),i.setOptions({layoutScroll:!0}),hn.current=i}return hn.current},resetTransform:(i,t)=>{i.style.transform=t!==void 0?t:"none"},checkIsScrollRoot:i=>window.getComputedStyle(i).position==="fixed"}),Vd={pan:{Feature:Yf},drag:{Feature:$f,ProjectionNode:ou,MeasureLayout:Qa}};function po(i,t,e){const{props:n}=i;i.animationState&&n.whileHover&&i.animationState.setActive("whileHover",e==="Start");const s="onHover"+e,r=n[s];r&&N.postRender(()=>r(t,vi(t)))}class Rd extends Jt{mount(){const{current:t}=this.node;t&&(this.unmount=nh(t,(e,n)=>(po(this.node,n,"Start"),s=>po(this.node,s,"End"))))}unmount(){}}class Ed extends Jt{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch{t=!0}!t||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=gi(ui(this.node.current,"focus",()=>this.onFocus()),ui(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function mo(i,t,e){const{props:n}=i;if(i.current instanceof HTMLButtonElement&&i.current.disabled)return;i.animationState&&n.whileTap&&i.animationState.setActive("whileTap",e==="Start");const s="onTap"+(e==="End"?"":e),r=n[s];r&&N.postRender(()=>r(t,vi(t)))}class kd extends Jt{mount(){const{current:t}=this.node;t&&(this.unmount=ah(t,(e,n)=>(mo(this.node,n,"Start"),(s,{success:r})=>mo(this.node,s,r?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}const jn=new WeakMap,fn=new WeakMap,Od=i=>{const t=jn.get(i.target);t&&t(i)},Ld=i=>{i.forEach(Od)};function Fd({root:i,...t}){const e=i||document;fn.has(e)||fn.set(e,{});const n=fn.get(e),s=JSON.stringify(t);return n[s]||(n[s]=new IntersectionObserver(Ld,{root:i,...t})),n[s]}function Bd(i,t,e){const n=Fd(t);return jn.set(i,e),n.observe(i),()=>{jn.delete(i),n.unobserve(i)}}const Id={some:0,all:1};class jd extends Jt{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:e,margin:n,amount:s="some",once:r}=t,o={root:e?e.current:void 0,rootMargin:n,threshold:typeof s=="number"?s:Id[s]},a=u=>{const{isIntersecting:l}=u;if(this.isInView===l||(this.isInView=l,r&&!l&&this.hasEnteredView))return;l&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",l);const{onViewportEnter:c,onViewportLeave:h}=this.node.getProps(),f=l?c:h;f&&f(u)};return Bd(this.node.current,o,a)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:t,prevProps:e}=this.node;["amount","margin","root"].some(Nd(t,e))&&this.startObserver()}unmount(){}}function Nd({viewport:i={}},{viewport:t={}}={}){return e=>i[e]!==t[e]}const zd={inView:{Feature:jd},tap:{Feature:kd},focus:{Feature:Ed},hover:{Feature:Rd}},Ud={layout:{ProjectionNode:ou,MeasureLayout:Qa}},Nn={current:null},au={current:!1};function Wd(){if(au.current=!0,!!ss)if(window.matchMedia){const i=window.matchMedia("(prefers-reduced-motion)"),t=()=>Nn.current=i.matches;i.addListener(t),t()}else Nn.current=!1}const Kd=new WeakMap;function $d(i,t,e){for(const n in t){const s=t[n],r=e[n];if(it(s))i.addValue(n,s);else if(it(r))i.addValue(n,Ve(s,{owner:i}));else if(r!==s)if(i.hasValue(n)){const o=i.getValue(n);o.liveStyle===!0?o.jump(s):o.hasAnimated||o.set(s)}else{const o=i.getStaticValue(n);i.addValue(n,Ve(o!==void 0?o:s,{owner:i}))}}for(const n in e)t[n]===void 0&&i.removeValue(n);return t}const go=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class Yd{scrapeMotionValuesFromProps(t,e,n){return{}}constructor({parent:t,props:e,presenceContext:n,reducedMotionConfig:s,blockInitialAnimation:r,visualState:o},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=Ts,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const f=ot.now();this.renderScheduledAt<f&&(this.renderScheduledAt=f,N.render(this.render,!1,!0))};const{latestValues:u,renderState:l}=o;this.latestValues=u,this.baseTarget={...u},this.initialValues=e.initial?{...u}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=n,this.depth=t?t.depth+1:0,this.reducedMotionConfig=s,this.options=a,this.blockInitialAnimation=!!r,this.isControllingVariants=Xi(e),this.isVariantNode=Ma(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);const{willChange:c,...h}=this.scrapeMotionValuesFromProps(e,{},this);for(const f in h){const d=h[f];u[f]!==void 0&&it(d)&&d.set(u[f],!1)}}mount(t){this.current=t,Kd.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((e,n)=>this.bindToMotionValue(n,e)),au.current||Wd(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:Nn.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){this.projection&&this.projection.unmount(),qt(this.notifyUpdate),qt(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features){const e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();const n=Ne.has(t);n&&this.onBindTransform&&this.onBindTransform();const s=e.on("change",a=>{this.latestValues[t]=a,this.props.onUpdate&&N.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0)}),r=e.on("renderRequest",this.scheduleRender);let o;window.MotionCheckAppearSync&&(o=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{s(),r(),o&&o(),e.owner&&e.stop()})}sortNodePosition(t){return!this.current||!this.sortInstanceNodePosition||this.type!==t.type?0:this.sortInstanceNodePosition(this.current,t.current)}updateFeatures(){let t="animation";for(t in Re){const e=Re[t];if(!e)continue;const{isEnabled:n,Feature:s}=e;if(!this.features[t]&&s&&n(this.props)&&(this.features[t]=new s(this)),this.features[t]){const r=this.features[t];r.isMounted?r.update():(r.mount(),r.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):$()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let n=0;n<go.length;n++){const s=go[n];this.propEventSubscriptions[s]&&(this.propEventSubscriptions[s](),delete this.propEventSubscriptions[s]);const r="on"+s,o=t[r];o&&(this.propEventSubscriptions[s]=this.on(s,o))}this.prevMotionValues=$d(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){const e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){const n=this.values.get(t);e!==n&&(n&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);const e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let n=this.values.get(t);return n===void 0&&e!==void 0&&(n=Ve(e===null?void 0:e,{owner:this}),this.addValue(t,n)),n}readValue(t,e){let n=this.latestValues[t]!==void 0||!this.current?this.latestValues[t]:this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options);return n!=null&&(typeof n=="string"&&(Fo(n)||Io(n))?n=parseFloat(n):!ch(n)&&Ht.test(e)&&(n=va(t,e)),this.setBaseTarget(t,it(n)?n.get():n)),it(n)?n.get():n}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){var r;const{initial:e}=this.props;let n;if(typeof e=="string"||typeof e=="object"){const o=Os(this.props,e,(r=this.presenceContext)==null?void 0:r.custom);o&&(n=o[t])}if(e&&n!==void 0)return n;const s=this.getBaseTargetFromProps(this.props,t);return s!==void 0&&!it(s)?s:this.initialValues[t]!==void 0&&n===void 0?void 0:this.baseTarget[t]}on(t,e){return this.events[t]||(this.events[t]=new ls),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class uu extends Yd{constructor(){super(...arguments),this.KeyframeResolver=Qc}sortInstanceNodePosition(t,e){return t.compareDocumentPosition(e)&2?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:n}){delete e[t],delete n[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;it(t)&&(this.childSubscription=t.on("change",e=>{this.current&&(this.current.textContent=`${e}`)}))}}function lu(i,{style:t,vars:e},n,s){Object.assign(i.style,t,s&&s.getProjectionStyles(n));for(const r in e)i.style.setProperty(r,e[r])}function Gd(i){return window.getComputedStyle(i)}class Xd extends uu{constructor(){super(...arguments),this.type="html",this.renderInstance=lu}readValueFromInstance(t,e){var n;if(Ne.has(e))return(n=this.projection)!=null&&n.isProjecting?Cn(e):yc(t,e);{const s=Gd(t),r=(fs(e)?s.getPropertyValue(e):s[e])||0;return typeof r=="string"?r.trim():r}}measureInstanceViewportBox(t,{transformPagePoint:e}){return Xa(t,e)}build(t,e,n){Rs(t,e,n.transformTemplate)}scrapeMotionValuesFromProps(t,e,n){return Ls(t,e,n)}}const cu=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function qd(i,t,e,n){lu(i,t,void 0,n);for(const s in t.attrs)i.setAttribute(cu.has(s)?s:Vs(s),t.attrs[s])}class Hd extends uu{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=$}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(Ne.has(e)){const n=_a(e);return n&&n.default||0}return e=cu.has(e)?e:Vs(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,n){return Ia(t,e,n)}build(t,e,n){Oa(t,e,this.isSVGTag,n.transformTemplate,n.style)}renderInstance(t,e,n,s){qd(t,e,n,s)}mount(t){this.isSVGTag=Fa(t.tagName),super.mount(t)}}const Zd=(i,t)=>ks(i)?new Hd(t):new Xd(t,{allowProjection:i!==A.Fragment}),Qd=Xh({...Tf,...zd,...Vd,...Ud},Zd),_m=vh(Qd);function Lt(i){if(i===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return i}function hu(i,t){i.prototype=Object.create(t.prototype),i.prototype.constructor=i,i.__proto__=t}/*!
 * GSAP 3.13.0
 * https://gsap.com
 *
 * @license Copyright 2008-2025, GreenSock. All rights reserved.
 * Subject to the terms at https://gsap.com/standard-license
 * @author: Jack Doyle, <EMAIL>
*/var mt={autoSleep:120,force3D:"auto",nullTargetWarn:1,units:{lineHeight:""}},Ee={duration:.5,overwrite:!1,delay:0},Bs,tt,F,Tt=1e8,L=1/Tt,zn=Math.PI*2,Jd=zn/4,tp=0,fu=Math.sqrt,ep=Math.cos,ip=Math.sin,Q=function(t){return typeof t=="string"},U=function(t){return typeof t=="function"},Nt=function(t){return typeof t=="number"},Is=function(t){return typeof t>"u"},Et=function(t){return typeof t=="object"},at=function(t){return t!==!1},js=function(){return typeof window<"u"},Si=function(t){return U(t)||Q(t)},du=typeof ArrayBuffer=="function"&&ArrayBuffer.isView||function(){},nt=Array.isArray,Un=/(?:-?\.?\d|\.)+/gi,pu=/[-+=.]*\d+[.e\-+]*\d*[e\-+]*\d*/g,Se=/[-+=.]*\d+[.e-]*\d*[a-z%]*/g,dn=/[-+=.]*\d+\.?\d*(?:e-|e\+)?\d*/gi,mu=/[+-]=-?[.\d]+/,gu=/[^,'"\[\]\s]+/gi,np=/^[+\-=e\s\d]*\d+[.\d]*([a-z]*|%)\s*$/i,B,At,Wn,Ns,gt={},Ii={},yu,_u=function(t){return(Ii=ke(t,gt))&&ht},zs=function(t,e){return console.warn("Invalid property",t,"set to",e,"Missing plugin? gsap.registerPlugin()")},li=function(t,e){return!e&&console.warn(t)},vu=function(t,e){return t&&(gt[t]=e)&&Ii&&(Ii[t]=e)||gt},ci=function(){return 0},sp={suppressEvents:!0,isStart:!0,kill:!1},Vi={suppressEvents:!0,kill:!1},rp={suppressEvents:!0},Us={},Gt=[],Kn={},xu,ft={},pn={},yo=30,Ri=[],Ws="",Ks=function(t){var e=t[0],n,s;if(Et(e)||U(e)||(t=[t]),!(n=(e._gsap||{}).harness)){for(s=Ri.length;s--&&!Ri[s].targetTest(e););n=Ri[s]}for(s=t.length;s--;)t[s]&&(t[s]._gsap||(t[s]._gsap=new Ku(t[s],n)))||t.splice(s,1);return t},de=function(t){return t._gsap||Ks(bt(t))[0]._gsap},Tu=function(t,e,n){return(n=t[e])&&U(n)?t[e]():Is(n)&&t.getAttribute&&t.getAttribute(e)||n},ut=function(t,e){return(t=t.split(",")).forEach(e)||t},Y=function(t){return Math.round(t*1e5)/1e5||0},H=function(t){return Math.round(t*1e7)/1e7||0},Ce=function(t,e){var n=e.charAt(0),s=parseFloat(e.substr(2));return t=parseFloat(t),n==="+"?t+s:n==="-"?t-s:n==="*"?t*s:t/s},op=function(t,e){for(var n=e.length,s=0;t.indexOf(e[s])<0&&++s<n;);return s<n},ji=function(){var t=Gt.length,e=Gt.slice(0),n,s;for(Kn={},Gt.length=0,n=0;n<t;n++)s=e[n],s&&s._lazy&&(s.render(s._lazy[0],s._lazy[1],!0)._lazy=0)},$s=function(t){return!!(t._initted||t._startAt||t.add)},bu=function(t,e,n,s){Gt.length&&!tt&&ji(),t.render(e,n,!!(tt&&e<0&&$s(t))),Gt.length&&!tt&&ji()},Pu=function(t){var e=parseFloat(t);return(e||e===0)&&(t+"").match(gu).length<2?e:Q(t)?t.trim():t},wu=function(t){return t},yt=function(t,e){for(var n in e)n in t||(t[n]=e[n]);return t},ap=function(t){return function(e,n){for(var s in n)s in e||s==="duration"&&t||s==="ease"||(e[s]=n[s])}},ke=function(t,e){for(var n in e)t[n]=e[n];return t},_o=function i(t,e){for(var n in e)n!=="__proto__"&&n!=="constructor"&&n!=="prototype"&&(t[n]=Et(e[n])?i(t[n]||(t[n]={}),e[n]):e[n]);return t},Ni=function(t,e){var n={},s;for(s in t)s in e||(n[s]=t[s]);return n},Je=function(t){var e=t.parent||B,n=t.keyframes?ap(nt(t.keyframes)):yt;if(at(t.inherit))for(;e;)n(t,e.vars.defaults),e=e.parent||e._dp;return t},up=function(t,e){for(var n=t.length,s=n===e.length;s&&n--&&t[n]===e[n];);return n<0},Su=function(t,e,n,s,r){var o=t[s],a;if(r)for(a=e[r];o&&o[r]>a;)o=o._prev;return o?(e._next=o._next,o._next=e):(e._next=t[n],t[n]=e),e._next?e._next._prev=e:t[s]=e,e._prev=o,e.parent=e._dp=t,e},qi=function(t,e,n,s){n===void 0&&(n="_first"),s===void 0&&(s="_last");var r=e._prev,o=e._next;r?r._next=o:t[n]===e&&(t[n]=o),o?o._prev=r:t[s]===e&&(t[s]=r),e._next=e._prev=e.parent=null},Zt=function(t,e){t.parent&&(!e||t.parent.autoRemoveChildren)&&t.parent.remove&&t.parent.remove(t),t._act=0},pe=function(t,e){if(t&&(!e||e._end>t._dur||e._start<0))for(var n=t;n;)n._dirty=1,n=n.parent;return t},lp=function(t){for(var e=t.parent;e&&e.parent;)e._dirty=1,e.totalDuration(),e=e.parent;return t},$n=function(t,e,n,s){return t._startAt&&(tt?t._startAt.revert(Vi):t.vars.immediateRender&&!t.vars.autoRevert||t._startAt.render(e,!0,s))},cp=function i(t){return!t||t._ts&&i(t.parent)},vo=function(t){return t._repeat?Oe(t._tTime,t=t.duration()+t._rDelay)*t:0},Oe=function(t,e){var n=Math.floor(t=H(t/e));return t&&n===t?n-1:n},zi=function(t,e){return(t-e._start)*e._ts+(e._ts>=0?0:e._dirty?e.totalDuration():e._tDur)},Hi=function(t){return t._end=H(t._start+(t._tDur/Math.abs(t._ts||t._rts||L)||0))},Zi=function(t,e){var n=t._dp;return n&&n.smoothChildTiming&&t._ts&&(t._start=H(n._time-(t._ts>0?e/t._ts:((t._dirty?t.totalDuration():t._tDur)-e)/-t._ts)),Hi(t),n._dirty||pe(n,t)),t},Au=function(t,e){var n;if((e._time||!e._dur&&e._initted||e._start<t._time&&(e._dur||!e.add))&&(n=zi(t.rawTime(),e),(!e._dur||xi(0,e.totalDuration(),n)-e._tTime>L)&&e.render(n,!0)),pe(t,e)._dp&&t._initted&&t._time>=t._dur&&t._ts){if(t._dur<t.duration())for(n=t;n._dp;)n.rawTime()>=0&&n.totalTime(n._tTime),n=n._dp;t._zTime=-L}},Ct=function(t,e,n,s){return e.parent&&Zt(e),e._start=H((Nt(n)?n:n||t!==B?xt(t,n,e):t._time)+e._delay),e._end=H(e._start+(e.totalDuration()/Math.abs(e.timeScale())||0)),Su(t,e,"_first","_last",t._sort?"_start":0),Yn(e)||(t._recent=e),s||Au(t,e),t._ts<0&&Zi(t,t._tTime),t},Cu=function(t,e){return(gt.ScrollTrigger||zs("scrollTrigger",e))&&gt.ScrollTrigger.create(e,t)},Mu=function(t,e,n,s,r){if(Gs(t,e,r),!t._initted)return 1;if(!n&&t._pt&&!tt&&(t._dur&&t.vars.lazy!==!1||!t._dur&&t.vars.lazy)&&xu!==dt.frame)return Gt.push(t),t._lazy=[r,s],1},hp=function i(t){var e=t.parent;return e&&e._ts&&e._initted&&!e._lock&&(e.rawTime()<0||i(e))},Yn=function(t){var e=t.data;return e==="isFromStart"||e==="isStart"},fp=function(t,e,n,s){var r=t.ratio,o=e<0||!e&&(!t._start&&hp(t)&&!(!t._initted&&Yn(t))||(t._ts<0||t._dp._ts<0)&&!Yn(t))?0:1,a=t._rDelay,u=0,l,c,h;if(a&&t._repeat&&(u=xi(0,t._tDur,e),c=Oe(u,a),t._yoyo&&c&1&&(o=1-o),c!==Oe(t._tTime,a)&&(r=1-o,t.vars.repeatRefresh&&t._initted&&t.invalidate())),o!==r||tt||s||t._zTime===L||!e&&t._zTime){if(!t._initted&&Mu(t,e,s,n,u))return;for(h=t._zTime,t._zTime=e||(n?L:0),n||(n=e&&!h),t.ratio=o,t._from&&(o=1-o),t._time=0,t._tTime=u,l=t._pt;l;)l.r(o,l.d),l=l._next;e<0&&$n(t,e,n,!0),t._onUpdate&&!n&&pt(t,"onUpdate"),u&&t._repeat&&!n&&t.parent&&pt(t,"onRepeat"),(e>=t._tDur||e<0)&&t.ratio===o&&(o&&Zt(t,1),!n&&!tt&&(pt(t,o?"onComplete":"onReverseComplete",!0),t._prom&&t._prom()))}else t._zTime||(t._zTime=e)},dp=function(t,e,n){var s;if(n>e)for(s=t._first;s&&s._start<=n;){if(s.data==="isPause"&&s._start>e)return s;s=s._next}else for(s=t._last;s&&s._start>=n;){if(s.data==="isPause"&&s._start<e)return s;s=s._prev}},Le=function(t,e,n,s){var r=t._repeat,o=H(e)||0,a=t._tTime/t._tDur;return a&&!s&&(t._time*=o/t._dur),t._dur=o,t._tDur=r?r<0?1e10:H(o*(r+1)+t._rDelay*r):o,a>0&&!s&&Zi(t,t._tTime=t._tDur*a),t.parent&&Hi(t),n||pe(t.parent,t),t},xo=function(t){return t instanceof st?pe(t):Le(t,t._dur)},pp={_start:0,endTime:ci,totalDuration:ci},xt=function i(t,e,n){var s=t.labels,r=t._recent||pp,o=t.duration()>=Tt?r.endTime(!1):t._dur,a,u,l;return Q(e)&&(isNaN(e)||e in s)?(u=e.charAt(0),l=e.substr(-1)==="%",a=e.indexOf("="),u==="<"||u===">"?(a>=0&&(e=e.replace(/=/,"")),(u==="<"?r._start:r.endTime(r._repeat>=0))+(parseFloat(e.substr(1))||0)*(l?(a<0?r:n).totalDuration()/100:1)):a<0?(e in s||(s[e]=o),s[e]):(u=parseFloat(e.charAt(a-1)+e.substr(a+1)),l&&n&&(u=u/100*(nt(n)?n[0]:n).totalDuration()),a>1?i(t,e.substr(0,a-1),n)+u:o+u)):e==null?o:+e},ti=function(t,e,n){var s=Nt(e[1]),r=(s?2:1)+(t<2?0:1),o=e[r],a,u;if(s&&(o.duration=e[1]),o.parent=n,t){for(a=o,u=n;u&&!("immediateRender"in a);)a=u.vars.defaults||{},u=at(u.vars.inherit)&&u.parent;o.immediateRender=at(a.immediateRender),t<2?o.runBackwards=1:o.startAt=e[r-1]}return new q(e[0],o,e[r+1])},te=function(t,e){return t||t===0?e(t):e},xi=function(t,e,n){return n<t?t:n>e?e:n},et=function(t,e){return!Q(t)||!(e=np.exec(t))?"":e[1]},mp=function(t,e,n){return te(n,function(s){return xi(t,e,s)})},Gn=[].slice,Du=function(t,e){return t&&Et(t)&&"length"in t&&(!e&&!t.length||t.length-1 in t&&Et(t[0]))&&!t.nodeType&&t!==At},gp=function(t,e,n){return n===void 0&&(n=[]),t.forEach(function(s){var r;return Q(s)&&!e||Du(s,1)?(r=n).push.apply(r,bt(s)):n.push(s)})||n},bt=function(t,e,n){return F&&!e&&F.selector?F.selector(t):Q(t)&&!n&&(Wn||!Fe())?Gn.call((e||Ns).querySelectorAll(t),0):nt(t)?gp(t,n):Du(t)?Gn.call(t,0):t?[t]:[]},Xn=function(t){return t=bt(t)[0]||li("Invalid scope")||{},function(e){var n=t.current||t.nativeElement||t;return bt(e,n.querySelectorAll?n:n===t?li("Invalid scope")||Ns.createElement("div"):t)}},Vu=function(t){return t.sort(function(){return .5-Math.random()})},Ru=function(t){if(U(t))return t;var e=Et(t)?t:{each:t},n=me(e.ease),s=e.from||0,r=parseFloat(e.base)||0,o={},a=s>0&&s<1,u=isNaN(s)||a,l=e.axis,c=s,h=s;return Q(s)?c=h={center:.5,edges:.5,end:1}[s]||0:!a&&u&&(c=s[0],h=s[1]),function(f,d,m){var p=(m||e).length,g=o[p],y,x,v,T,_,b,S,P,w;if(!g){if(w=e.grid==="auto"?0:(e.grid||[1,Tt])[1],!w){for(S=-Tt;S<(S=m[w++].getBoundingClientRect().left)&&w<p;);w<p&&w--}for(g=o[p]=[],y=u?Math.min(w,p)*c-.5:s%w,x=w===Tt?0:u?p*h/w-.5:s/w|0,S=0,P=Tt,b=0;b<p;b++)v=b%w-y,T=x-(b/w|0),g[b]=_=l?Math.abs(l==="y"?T:v):fu(v*v+T*T),_>S&&(S=_),_<P&&(P=_);s==="random"&&Vu(g),g.max=S-P,g.min=P,g.v=p=(parseFloat(e.amount)||parseFloat(e.each)*(w>p?p-1:l?l==="y"?p/w:w:Math.max(w,p/w))||0)*(s==="edges"?-1:1),g.b=p<0?r-p:r,g.u=et(e.amount||e.each)||0,n=n&&p<0?zu(n):n}return p=(g[f]-g.min)/g.max||0,H(g.b+(n?n(p):p)*g.v)+g.u}},qn=function(t){var e=Math.pow(10,((t+"").split(".")[1]||"").length);return function(n){var s=H(Math.round(parseFloat(n)/t)*t*e);return(s-s%1)/e+(Nt(n)?0:et(n))}},Eu=function(t,e){var n=nt(t),s,r;return!n&&Et(t)&&(s=n=t.radius||Tt,t.values?(t=bt(t.values),(r=!Nt(t[0]))&&(s*=s)):t=qn(t.increment)),te(e,n?U(t)?function(o){return r=t(o),Math.abs(r-o)<=s?r:o}:function(o){for(var a=parseFloat(r?o.x:o),u=parseFloat(r?o.y:0),l=Tt,c=0,h=t.length,f,d;h--;)r?(f=t[h].x-a,d=t[h].y-u,f=f*f+d*d):f=Math.abs(t[h]-a),f<l&&(l=f,c=h);return c=!s||l<=s?t[c]:o,r||c===o||Nt(o)?c:c+et(o)}:qn(t))},ku=function(t,e,n,s){return te(nt(t)?!e:n===!0?!!(n=0):!s,function(){return nt(t)?t[~~(Math.random()*t.length)]:(n=n||1e-5)&&(s=n<1?Math.pow(10,(n+"").length-2):1)&&Math.floor(Math.round((t-n/2+Math.random()*(e-t+n*.99))/n)*n*s)/s})},yp=function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];return function(s){return e.reduce(function(r,o){return o(r)},s)}},_p=function(t,e){return function(n){return t(parseFloat(n))+(e||et(n))}},vp=function(t,e,n){return Lu(t,e,0,1,n)},Ou=function(t,e,n){return te(n,function(s){return t[~~e(s)]})},xp=function i(t,e,n){var s=e-t;return nt(t)?Ou(t,i(0,t.length),e):te(n,function(r){return(s+(r-t)%s)%s+t})},Tp=function i(t,e,n){var s=e-t,r=s*2;return nt(t)?Ou(t,i(0,t.length-1),e):te(n,function(o){return o=(r+(o-t)%r)%r||0,t+(o>s?r-o:o)})},hi=function(t){for(var e=0,n="",s,r,o,a;~(s=t.indexOf("random(",e));)o=t.indexOf(")",s),a=t.charAt(s+7)==="[",r=t.substr(s+7,o-s-7).match(a?gu:Un),n+=t.substr(e,s-e)+ku(a?r:+r[0],a?0:+r[1],+r[2]||1e-5),e=o+1;return n+t.substr(e,t.length-e)},Lu=function(t,e,n,s,r){var o=e-t,a=s-n;return te(r,function(u){return n+((u-t)/o*a||0)})},bp=function i(t,e,n,s){var r=isNaN(t+e)?0:function(d){return(1-d)*t+d*e};if(!r){var o=Q(t),a={},u,l,c,h,f;if(n===!0&&(s=1)&&(n=null),o)t={p:t},e={p:e};else if(nt(t)&&!nt(e)){for(c=[],h=t.length,f=h-2,l=1;l<h;l++)c.push(i(t[l-1],t[l]));h--,r=function(m){m*=h;var p=Math.min(f,~~m);return c[p](m-p)},n=e}else s||(t=ke(nt(t)?[]:{},t));if(!c){for(u in e)Ys.call(a,t,u,"get",e[u]);r=function(m){return Hs(m,a)||(o?t.p:t)}}}return te(n,r)},To=function(t,e,n){var s=t.labels,r=Tt,o,a,u;for(o in s)a=s[o]-e,a<0==!!n&&a&&r>(a=Math.abs(a))&&(u=o,r=a);return u},pt=function(t,e,n){var s=t.vars,r=s[e],o=F,a=t._ctx,u,l,c;if(r)return u=s[e+"Params"],l=s.callbackScope||t,n&&Gt.length&&ji(),a&&(F=a),c=u?r.apply(l,u):r.call(l),F=o,c},Ye=function(t){return Zt(t),t.scrollTrigger&&t.scrollTrigger.kill(!!tt),t.progress()<1&&pt(t,"onInterrupt"),t},Ae,Fu=[],Bu=function(t){if(t)if(t=!t.name&&t.default||t,js()||t.headless){var e=t.name,n=U(t),s=e&&!n&&t.init?function(){this._props=[]}:t,r={init:ci,render:Hs,add:Ys,kill:Ip,modifier:Bp,rawVars:0},o={targetTest:0,get:0,getSetter:qs,aliases:{},register:0};if(Fe(),t!==s){if(ft[e])return;yt(s,yt(Ni(t,r),o)),ke(s.prototype,ke(r,Ni(t,o))),ft[s.prop=e]=s,t.targetTest&&(Ri.push(s),Us[e]=1),e=(e==="css"?"CSS":e.charAt(0).toUpperCase()+e.substr(1))+"Plugin"}vu(e,s),t.register&&t.register(ht,s,lt)}else Fu.push(t)},O=255,Ge={aqua:[0,O,O],lime:[0,O,0],silver:[192,192,192],black:[0,0,0],maroon:[128,0,0],teal:[0,128,128],blue:[0,0,O],navy:[0,0,128],white:[O,O,O],olive:[128,128,0],yellow:[O,O,0],orange:[O,165,0],gray:[128,128,128],purple:[128,0,128],green:[0,128,0],red:[O,0,0],pink:[O,192,203],cyan:[0,O,O],transparent:[O,O,O,0]},mn=function(t,e,n){return t+=t<0?1:t>1?-1:0,(t*6<1?e+(n-e)*t*6:t<.5?n:t*3<2?e+(n-e)*(2/3-t)*6:e)*O+.5|0},Iu=function(t,e,n){var s=t?Nt(t)?[t>>16,t>>8&O,t&O]:0:Ge.black,r,o,a,u,l,c,h,f,d,m;if(!s){if(t.substr(-1)===","&&(t=t.substr(0,t.length-1)),Ge[t])s=Ge[t];else if(t.charAt(0)==="#"){if(t.length<6&&(r=t.charAt(1),o=t.charAt(2),a=t.charAt(3),t="#"+r+r+o+o+a+a+(t.length===5?t.charAt(4)+t.charAt(4):"")),t.length===9)return s=parseInt(t.substr(1,6),16),[s>>16,s>>8&O,s&O,parseInt(t.substr(7),16)/255];t=parseInt(t.substr(1),16),s=[t>>16,t>>8&O,t&O]}else if(t.substr(0,3)==="hsl"){if(s=m=t.match(Un),!e)u=+s[0]%360/360,l=+s[1]/100,c=+s[2]/100,o=c<=.5?c*(l+1):c+l-c*l,r=c*2-o,s.length>3&&(s[3]*=1),s[0]=mn(u+1/3,r,o),s[1]=mn(u,r,o),s[2]=mn(u-1/3,r,o);else if(~t.indexOf("="))return s=t.match(pu),n&&s.length<4&&(s[3]=1),s}else s=t.match(Un)||Ge.transparent;s=s.map(Number)}return e&&!m&&(r=s[0]/O,o=s[1]/O,a=s[2]/O,h=Math.max(r,o,a),f=Math.min(r,o,a),c=(h+f)/2,h===f?u=l=0:(d=h-f,l=c>.5?d/(2-h-f):d/(h+f),u=h===r?(o-a)/d+(o<a?6:0):h===o?(a-r)/d+2:(r-o)/d+4,u*=60),s[0]=~~(u+.5),s[1]=~~(l*100+.5),s[2]=~~(c*100+.5)),n&&s.length<4&&(s[3]=1),s},ju=function(t){var e=[],n=[],s=-1;return t.split(Xt).forEach(function(r){var o=r.match(Se)||[];e.push.apply(e,o),n.push(s+=o.length+1)}),e.c=n,e},bo=function(t,e,n){var s="",r=(t+s).match(Xt),o=e?"hsla(":"rgba(",a=0,u,l,c,h;if(!r)return t;if(r=r.map(function(f){return(f=Iu(f,e,1))&&o+(e?f[0]+","+f[1]+"%,"+f[2]+"%,"+f[3]:f.join(","))+")"}),n&&(c=ju(t),u=n.c,u.join(s)!==c.c.join(s)))for(l=t.replace(Xt,"1").split(Se),h=l.length-1;a<h;a++)s+=l[a]+(~u.indexOf(a)?r.shift()||o+"0,0,0,0)":(c.length?c:r.length?r:n).shift());if(!l)for(l=t.split(Xt),h=l.length-1;a<h;a++)s+=l[a]+r[a];return s+l[h]},Xt=function(){var i="(?:\\b(?:(?:rgb|rgba|hsl|hsla)\\(.+?\\))|\\B#(?:[0-9a-f]{3,4}){1,2}\\b",t;for(t in Ge)i+="|"+t+"\\b";return new RegExp(i+")","gi")}(),Pp=/hsl[a]?\(/,Nu=function(t){var e=t.join(" "),n;if(Xt.lastIndex=0,Xt.test(e))return n=Pp.test(e),t[1]=bo(t[1],n),t[0]=bo(t[0],n,ju(t[1])),!0},fi,dt=function(){var i=Date.now,t=500,e=33,n=i(),s=n,r=1e3/240,o=r,a=[],u,l,c,h,f,d,m=function p(g){var y=i()-s,x=g===!0,v,T,_,b;if((y>t||y<0)&&(n+=y-e),s+=y,_=s-n,v=_-o,(v>0||x)&&(b=++h.frame,f=_-h.time*1e3,h.time=_=_/1e3,o+=v+(v>=r?4:r-v),T=1),x||(u=l(p)),T)for(d=0;d<a.length;d++)a[d](_,f,b,g)};return h={time:0,frame:0,tick:function(){m(!0)},deltaRatio:function(g){return f/(1e3/(g||60))},wake:function(){yu&&(!Wn&&js()&&(At=Wn=window,Ns=At.document||{},gt.gsap=ht,(At.gsapVersions||(At.gsapVersions=[])).push(ht.version),_u(Ii||At.GreenSockGlobals||!At.gsap&&At||{}),Fu.forEach(Bu)),c=typeof requestAnimationFrame<"u"&&requestAnimationFrame,u&&h.sleep(),l=c||function(g){return setTimeout(g,o-h.time*1e3+1|0)},fi=1,m(2))},sleep:function(){(c?cancelAnimationFrame:clearTimeout)(u),fi=0,l=ci},lagSmoothing:function(g,y){t=g||1/0,e=Math.min(y||33,t)},fps:function(g){r=1e3/(g||240),o=h.time*1e3+r},add:function(g,y,x){var v=y?function(T,_,b,S){g(T,_,b,S),h.remove(v)}:g;return h.remove(g),a[x?"unshift":"push"](v),Fe(),v},remove:function(g,y){~(y=a.indexOf(g))&&a.splice(y,1)&&d>=y&&d--},_listeners:a},h}(),Fe=function(){return!fi&&dt.wake()},D={},wp=/^[\d.\-M][\d.\-,\s]/,Sp=/["']/g,Ap=function(t){for(var e={},n=t.substr(1,t.length-3).split(":"),s=n[0],r=1,o=n.length,a,u,l;r<o;r++)u=n[r],a=r!==o-1?u.lastIndexOf(","):u.length,l=u.substr(0,a),e[s]=isNaN(l)?l.replace(Sp,"").trim():+l,s=u.substr(a+1).trim();return e},Cp=function(t){var e=t.indexOf("(")+1,n=t.indexOf(")"),s=t.indexOf("(",e);return t.substring(e,~s&&s<n?t.indexOf(")",n+1):n)},Mp=function(t){var e=(t+"").split("("),n=D[e[0]];return n&&e.length>1&&n.config?n.config.apply(null,~t.indexOf("{")?[Ap(e[1])]:Cp(t).split(",").map(Pu)):D._CE&&wp.test(t)?D._CE("",t):n},zu=function(t){return function(e){return 1-t(1-e)}},Uu=function i(t,e){for(var n=t._first,s;n;)n instanceof st?i(n,e):n.vars.yoyoEase&&(!n._yoyo||!n._repeat)&&n._yoyo!==e&&(n.timeline?i(n.timeline,e):(s=n._ease,n._ease=n._yEase,n._yEase=s,n._yoyo=e)),n=n._next},me=function(t,e){return t&&(U(t)?t:D[t]||Mp(t))||e},_e=function(t,e,n,s){n===void 0&&(n=function(u){return 1-e(1-u)}),s===void 0&&(s=function(u){return u<.5?e(u*2)/2:1-e((1-u)*2)/2});var r={easeIn:e,easeOut:n,easeInOut:s},o;return ut(t,function(a){D[a]=gt[a]=r,D[o=a.toLowerCase()]=n;for(var u in r)D[o+(u==="easeIn"?".in":u==="easeOut"?".out":".inOut")]=D[a+"."+u]=r[u]}),r},Wu=function(t){return function(e){return e<.5?(1-t(1-e*2))/2:.5+t((e-.5)*2)/2}},gn=function i(t,e,n){var s=e>=1?e:1,r=(n||(t?.3:.45))/(e<1?e:1),o=r/zn*(Math.asin(1/s)||0),a=function(c){return c===1?1:s*Math.pow(2,-10*c)*ip((c-o)*r)+1},u=t==="out"?a:t==="in"?function(l){return 1-a(1-l)}:Wu(a);return r=zn/r,u.config=function(l,c){return i(t,l,c)},u},yn=function i(t,e){e===void 0&&(e=1.70158);var n=function(o){return o?--o*o*((e+1)*o+e)+1:0},s=t==="out"?n:t==="in"?function(r){return 1-n(1-r)}:Wu(n);return s.config=function(r){return i(t,r)},s};ut("Linear,Quad,Cubic,Quart,Quint,Strong",function(i,t){var e=t<5?t+1:t;_e(i+",Power"+(e-1),t?function(n){return Math.pow(n,e)}:function(n){return n},function(n){return 1-Math.pow(1-n,e)},function(n){return n<.5?Math.pow(n*2,e)/2:1-Math.pow((1-n)*2,e)/2})});D.Linear.easeNone=D.none=D.Linear.easeIn;_e("Elastic",gn("in"),gn("out"),gn());(function(i,t){var e=1/t,n=2*e,s=2.5*e,r=function(a){return a<e?i*a*a:a<n?i*Math.pow(a-1.5/t,2)+.75:a<s?i*(a-=2.25/t)*a+.9375:i*Math.pow(a-2.625/t,2)+.984375};_e("Bounce",function(o){return 1-r(1-o)},r)})(7.5625,2.75);_e("Expo",function(i){return Math.pow(2,10*(i-1))*i+i*i*i*i*i*i*(1-i)});_e("Circ",function(i){return-(fu(1-i*i)-1)});_e("Sine",function(i){return i===1?1:-ep(i*Jd)+1});_e("Back",yn("in"),yn("out"),yn());D.SteppedEase=D.steps=gt.SteppedEase={config:function(t,e){t===void 0&&(t=1);var n=1/t,s=t+(e?0:1),r=e?1:0,o=1-L;return function(a){return((s*xi(0,o,a)|0)+r)*n}}};Ee.ease=D["quad.out"];ut("onComplete,onUpdate,onStart,onRepeat,onReverseComplete,onInterrupt",function(i){return Ws+=i+","+i+"Params,"});var Ku=function(t,e){this.id=tp++,t._gsap=this,this.target=t,this.harness=e,this.get=e?e.get:Tu,this.set=e?e.getSetter:qs},di=function(){function i(e){this.vars=e,this._delay=+e.delay||0,(this._repeat=e.repeat===1/0?-2:e.repeat||0)&&(this._rDelay=e.repeatDelay||0,this._yoyo=!!e.yoyo||!!e.yoyoEase),this._ts=1,Le(this,+e.duration,1,1),this.data=e.data,F&&(this._ctx=F,F.data.push(this)),fi||dt.wake()}var t=i.prototype;return t.delay=function(n){return n||n===0?(this.parent&&this.parent.smoothChildTiming&&this.startTime(this._start+n-this._delay),this._delay=n,this):this._delay},t.duration=function(n){return arguments.length?this.totalDuration(this._repeat>0?n+(n+this._rDelay)*this._repeat:n):this.totalDuration()&&this._dur},t.totalDuration=function(n){return arguments.length?(this._dirty=0,Le(this,this._repeat<0?n:(n-this._repeat*this._rDelay)/(this._repeat+1))):this._tDur},t.totalTime=function(n,s){if(Fe(),!arguments.length)return this._tTime;var r=this._dp;if(r&&r.smoothChildTiming&&this._ts){for(Zi(this,n),!r._dp||r.parent||Au(r,this);r&&r.parent;)r.parent._time!==r._start+(r._ts>=0?r._tTime/r._ts:(r.totalDuration()-r._tTime)/-r._ts)&&r.totalTime(r._tTime,!0),r=r.parent;!this.parent&&this._dp.autoRemoveChildren&&(this._ts>0&&n<this._tDur||this._ts<0&&n>0||!this._tDur&&!n)&&Ct(this._dp,this,this._start-this._delay)}return(this._tTime!==n||!this._dur&&!s||this._initted&&Math.abs(this._zTime)===L||!n&&!this._initted&&(this.add||this._ptLookup))&&(this._ts||(this._pTime=n),bu(this,n,s)),this},t.time=function(n,s){return arguments.length?this.totalTime(Math.min(this.totalDuration(),n+vo(this))%(this._dur+this._rDelay)||(n?this._dur:0),s):this._time},t.totalProgress=function(n,s){return arguments.length?this.totalTime(this.totalDuration()*n,s):this.totalDuration()?Math.min(1,this._tTime/this._tDur):this.rawTime()>=0&&this._initted?1:0},t.progress=function(n,s){return arguments.length?this.totalTime(this.duration()*(this._yoyo&&!(this.iteration()&1)?1-n:n)+vo(this),s):this.duration()?Math.min(1,this._time/this._dur):this.rawTime()>0?1:0},t.iteration=function(n,s){var r=this.duration()+this._rDelay;return arguments.length?this.totalTime(this._time+(n-1)*r,s):this._repeat?Oe(this._tTime,r)+1:1},t.timeScale=function(n,s){if(!arguments.length)return this._rts===-L?0:this._rts;if(this._rts===n)return this;var r=this.parent&&this._ts?zi(this.parent._time,this):this._tTime;return this._rts=+n||0,this._ts=this._ps||n===-L?0:this._rts,this.totalTime(xi(-Math.abs(this._delay),this.totalDuration(),r),s!==!1),Hi(this),lp(this)},t.paused=function(n){return arguments.length?(this._ps!==n&&(this._ps=n,n?(this._pTime=this._tTime||Math.max(-this._delay,this.rawTime()),this._ts=this._act=0):(Fe(),this._ts=this._rts,this.totalTime(this.parent&&!this.parent.smoothChildTiming?this.rawTime():this._tTime||this._pTime,this.progress()===1&&Math.abs(this._zTime)!==L&&(this._tTime-=L)))),this):this._ps},t.startTime=function(n){if(arguments.length){this._start=n;var s=this.parent||this._dp;return s&&(s._sort||!this.parent)&&Ct(s,this,n-this._delay),this}return this._start},t.endTime=function(n){return this._start+(at(n)?this.totalDuration():this.duration())/Math.abs(this._ts||1)},t.rawTime=function(n){var s=this.parent||this._dp;return s?n&&(!this._ts||this._repeat&&this._time&&this.totalProgress()<1)?this._tTime%(this._dur+this._rDelay):this._ts?zi(s.rawTime(n),this):this._tTime:this._tTime},t.revert=function(n){n===void 0&&(n=rp);var s=tt;return tt=n,$s(this)&&(this.timeline&&this.timeline.revert(n),this.totalTime(-.01,n.suppressEvents)),this.data!=="nested"&&n.kill!==!1&&this.kill(),tt=s,this},t.globalTime=function(n){for(var s=this,r=arguments.length?n:s.rawTime();s;)r=s._start+r/(Math.abs(s._ts)||1),s=s._dp;return!this.parent&&this._sat?this._sat.globalTime(n):r},t.repeat=function(n){return arguments.length?(this._repeat=n===1/0?-2:n,xo(this)):this._repeat===-2?1/0:this._repeat},t.repeatDelay=function(n){if(arguments.length){var s=this._time;return this._rDelay=n,xo(this),s?this.time(s):this}return this._rDelay},t.yoyo=function(n){return arguments.length?(this._yoyo=n,this):this._yoyo},t.seek=function(n,s){return this.totalTime(xt(this,n),at(s))},t.restart=function(n,s){return this.play().totalTime(n?-this._delay:0,at(s)),this._dur||(this._zTime=-L),this},t.play=function(n,s){return n!=null&&this.seek(n,s),this.reversed(!1).paused(!1)},t.reverse=function(n,s){return n!=null&&this.seek(n||this.totalDuration(),s),this.reversed(!0).paused(!1)},t.pause=function(n,s){return n!=null&&this.seek(n,s),this.paused(!0)},t.resume=function(){return this.paused(!1)},t.reversed=function(n){return arguments.length?(!!n!==this.reversed()&&this.timeScale(-this._rts||(n?-L:0)),this):this._rts<0},t.invalidate=function(){return this._initted=this._act=0,this._zTime=-L,this},t.isActive=function(){var n=this.parent||this._dp,s=this._start,r;return!!(!n||this._ts&&this._initted&&n.isActive()&&(r=n.rawTime(!0))>=s&&r<this.endTime(!0)-L)},t.eventCallback=function(n,s,r){var o=this.vars;return arguments.length>1?(s?(o[n]=s,r&&(o[n+"Params"]=r),n==="onUpdate"&&(this._onUpdate=s)):delete o[n],this):o[n]},t.then=function(n){var s=this;return new Promise(function(r){var o=U(n)?n:wu,a=function(){var l=s.then;s.then=null,U(o)&&(o=o(s))&&(o.then||o===s)&&(s.then=l),r(o),s.then=l};s._initted&&s.totalProgress()===1&&s._ts>=0||!s._tTime&&s._ts<0?a():s._prom=a})},t.kill=function(){Ye(this)},i}();yt(di.prototype,{_time:0,_start:0,_end:0,_tTime:0,_tDur:0,_dirty:0,_repeat:0,_yoyo:!1,parent:null,_initted:!1,_rDelay:0,_ts:1,_dp:0,ratio:0,_zTime:-L,_prom:0,_ps:!1,_rts:1});var st=function(i){hu(t,i);function t(n,s){var r;return n===void 0&&(n={}),r=i.call(this,n)||this,r.labels={},r.smoothChildTiming=!!n.smoothChildTiming,r.autoRemoveChildren=!!n.autoRemoveChildren,r._sort=at(n.sortChildren),B&&Ct(n.parent||B,Lt(r),s),n.reversed&&r.reverse(),n.paused&&r.paused(!0),n.scrollTrigger&&Cu(Lt(r),n.scrollTrigger),r}var e=t.prototype;return e.to=function(s,r,o){return ti(0,arguments,this),this},e.from=function(s,r,o){return ti(1,arguments,this),this},e.fromTo=function(s,r,o,a){return ti(2,arguments,this),this},e.set=function(s,r,o){return r.duration=0,r.parent=this,Je(r).repeatDelay||(r.repeat=0),r.immediateRender=!!r.immediateRender,new q(s,r,xt(this,o),1),this},e.call=function(s,r,o){return Ct(this,q.delayedCall(0,s,r),o)},e.staggerTo=function(s,r,o,a,u,l,c){return o.duration=r,o.stagger=o.stagger||a,o.onComplete=l,o.onCompleteParams=c,o.parent=this,new q(s,o,xt(this,u)),this},e.staggerFrom=function(s,r,o,a,u,l,c){return o.runBackwards=1,Je(o).immediateRender=at(o.immediateRender),this.staggerTo(s,r,o,a,u,l,c)},e.staggerFromTo=function(s,r,o,a,u,l,c,h){return a.startAt=o,Je(a).immediateRender=at(a.immediateRender),this.staggerTo(s,r,a,u,l,c,h)},e.render=function(s,r,o){var a=this._time,u=this._dirty?this.totalDuration():this._tDur,l=this._dur,c=s<=0?0:H(s),h=this._zTime<0!=s<0&&(this._initted||!l),f,d,m,p,g,y,x,v,T,_,b,S;if(this!==B&&c>u&&s>=0&&(c=u),c!==this._tTime||o||h){if(a!==this._time&&l&&(c+=this._time-a,s+=this._time-a),f=c,T=this._start,v=this._ts,y=!v,h&&(l||(a=this._zTime),(s||!r)&&(this._zTime=s)),this._repeat){if(b=this._yoyo,g=l+this._rDelay,this._repeat<-1&&s<0)return this.totalTime(g*100+s,r,o);if(f=H(c%g),c===u?(p=this._repeat,f=l):(_=H(c/g),p=~~_,p&&p===_&&(f=l,p--),f>l&&(f=l)),_=Oe(this._tTime,g),!a&&this._tTime&&_!==p&&this._tTime-_*g-this._dur<=0&&(_=p),b&&p&1&&(f=l-f,S=1),p!==_&&!this._lock){var P=b&&_&1,w=P===(b&&p&1);if(p<_&&(P=!P),a=P?0:c%l?l:c,this._lock=1,this.render(a||(S?0:H(p*g)),r,!l)._lock=0,this._tTime=c,!r&&this.parent&&pt(this,"onRepeat"),this.vars.repeatRefresh&&!S&&(this.invalidate()._lock=1),a&&a!==this._time||y!==!this._ts||this.vars.onRepeat&&!this.parent&&!this._act)return this;if(l=this._dur,u=this._tDur,w&&(this._lock=2,a=P?l:-1e-4,this.render(a,!0),this.vars.repeatRefresh&&!S&&this.invalidate()),this._lock=0,!this._ts&&!y)return this;Uu(this,S)}}if(this._hasPause&&!this._forcing&&this._lock<2&&(x=dp(this,H(a),H(f)),x&&(c-=f-(f=x._start))),this._tTime=c,this._time=f,this._act=!v,this._initted||(this._onUpdate=this.vars.onUpdate,this._initted=1,this._zTime=s,a=0),!a&&c&&!r&&!_&&(pt(this,"onStart"),this._tTime!==c))return this;if(f>=a&&s>=0)for(d=this._first;d;){if(m=d._next,(d._act||f>=d._start)&&d._ts&&x!==d){if(d.parent!==this)return this.render(s,r,o);if(d.render(d._ts>0?(f-d._start)*d._ts:(d._dirty?d.totalDuration():d._tDur)+(f-d._start)*d._ts,r,o),f!==this._time||!this._ts&&!y){x=0,m&&(c+=this._zTime=-L);break}}d=m}else{d=this._last;for(var M=s<0?s:f;d;){if(m=d._prev,(d._act||M<=d._end)&&d._ts&&x!==d){if(d.parent!==this)return this.render(s,r,o);if(d.render(d._ts>0?(M-d._start)*d._ts:(d._dirty?d.totalDuration():d._tDur)+(M-d._start)*d._ts,r,o||tt&&$s(d)),f!==this._time||!this._ts&&!y){x=0,m&&(c+=this._zTime=M?-L:L);break}}d=m}}if(x&&!r&&(this.pause(),x.render(f>=a?0:-L)._zTime=f>=a?1:-1,this._ts))return this._start=T,Hi(this),this.render(s,r,o);this._onUpdate&&!r&&pt(this,"onUpdate",!0),(c===u&&this._tTime>=this.totalDuration()||!c&&a)&&(T===this._start||Math.abs(v)!==Math.abs(this._ts))&&(this._lock||((s||!l)&&(c===u&&this._ts>0||!c&&this._ts<0)&&Zt(this,1),!r&&!(s<0&&!a)&&(c||a||!u)&&(pt(this,c===u&&s>=0?"onComplete":"onReverseComplete",!0),this._prom&&!(c<u&&this.timeScale()>0)&&this._prom())))}return this},e.add=function(s,r){var o=this;if(Nt(r)||(r=xt(this,r,s)),!(s instanceof di)){if(nt(s))return s.forEach(function(a){return o.add(a,r)}),this;if(Q(s))return this.addLabel(s,r);if(U(s))s=q.delayedCall(0,s);else return this}return this!==s?Ct(this,s,r):this},e.getChildren=function(s,r,o,a){s===void 0&&(s=!0),r===void 0&&(r=!0),o===void 0&&(o=!0),a===void 0&&(a=-Tt);for(var u=[],l=this._first;l;)l._start>=a&&(l instanceof q?r&&u.push(l):(o&&u.push(l),s&&u.push.apply(u,l.getChildren(!0,r,o)))),l=l._next;return u},e.getById=function(s){for(var r=this.getChildren(1,1,1),o=r.length;o--;)if(r[o].vars.id===s)return r[o]},e.remove=function(s){return Q(s)?this.removeLabel(s):U(s)?this.killTweensOf(s):(s.parent===this&&qi(this,s),s===this._recent&&(this._recent=this._last),pe(this))},e.totalTime=function(s,r){return arguments.length?(this._forcing=1,!this._dp&&this._ts&&(this._start=H(dt.time-(this._ts>0?s/this._ts:(this.totalDuration()-s)/-this._ts))),i.prototype.totalTime.call(this,s,r),this._forcing=0,this):this._tTime},e.addLabel=function(s,r){return this.labels[s]=xt(this,r),this},e.removeLabel=function(s){return delete this.labels[s],this},e.addPause=function(s,r,o){var a=q.delayedCall(0,r||ci,o);return a.data="isPause",this._hasPause=1,Ct(this,a,xt(this,s))},e.removePause=function(s){var r=this._first;for(s=xt(this,s);r;)r._start===s&&r.data==="isPause"&&Zt(r),r=r._next},e.killTweensOf=function(s,r,o){for(var a=this.getTweensOf(s,o),u=a.length;u--;)Kt!==a[u]&&a[u].kill(s,r);return this},e.getTweensOf=function(s,r){for(var o=[],a=bt(s),u=this._first,l=Nt(r),c;u;)u instanceof q?op(u._targets,a)&&(l?(!Kt||u._initted&&u._ts)&&u.globalTime(0)<=r&&u.globalTime(u.totalDuration())>r:!r||u.isActive())&&o.push(u):(c=u.getTweensOf(a,r)).length&&o.push.apply(o,c),u=u._next;return o},e.tweenTo=function(s,r){r=r||{};var o=this,a=xt(o,s),u=r,l=u.startAt,c=u.onStart,h=u.onStartParams,f=u.immediateRender,d,m=q.to(o,yt({ease:r.ease||"none",lazy:!1,immediateRender:!1,time:a,overwrite:"auto",duration:r.duration||Math.abs((a-(l&&"time"in l?l.time:o._time))/o.timeScale())||L,onStart:function(){if(o.pause(),!d){var g=r.duration||Math.abs((a-(l&&"time"in l?l.time:o._time))/o.timeScale());m._dur!==g&&Le(m,g,0,1).render(m._time,!0,!0),d=1}c&&c.apply(m,h||[])}},r));return f?m.render(0):m},e.tweenFromTo=function(s,r,o){return this.tweenTo(r,yt({startAt:{time:xt(this,s)}},o))},e.recent=function(){return this._recent},e.nextLabel=function(s){return s===void 0&&(s=this._time),To(this,xt(this,s))},e.previousLabel=function(s){return s===void 0&&(s=this._time),To(this,xt(this,s),1)},e.currentLabel=function(s){return arguments.length?this.seek(s,!0):this.previousLabel(this._time+L)},e.shiftChildren=function(s,r,o){o===void 0&&(o=0);for(var a=this._first,u=this.labels,l;a;)a._start>=o&&(a._start+=s,a._end+=s),a=a._next;if(r)for(l in u)u[l]>=o&&(u[l]+=s);return pe(this)},e.invalidate=function(s){var r=this._first;for(this._lock=0;r;)r.invalidate(s),r=r._next;return i.prototype.invalidate.call(this,s)},e.clear=function(s){s===void 0&&(s=!0);for(var r=this._first,o;r;)o=r._next,this.remove(r),r=o;return this._dp&&(this._time=this._tTime=this._pTime=0),s&&(this.labels={}),pe(this)},e.totalDuration=function(s){var r=0,o=this,a=o._last,u=Tt,l,c,h;if(arguments.length)return o.timeScale((o._repeat<0?o.duration():o.totalDuration())/(o.reversed()?-s:s));if(o._dirty){for(h=o.parent;a;)l=a._prev,a._dirty&&a.totalDuration(),c=a._start,c>u&&o._sort&&a._ts&&!o._lock?(o._lock=1,Ct(o,a,c-a._delay,1)._lock=0):u=c,c<0&&a._ts&&(r-=c,(!h&&!o._dp||h&&h.smoothChildTiming)&&(o._start+=c/o._ts,o._time-=c,o._tTime-=c),o.shiftChildren(-c,!1,-1/0),u=0),a._end>r&&a._ts&&(r=a._end),a=l;Le(o,o===B&&o._time>r?o._time:r,1,1),o._dirty=0}return o._tDur},t.updateRoot=function(s){if(B._ts&&(bu(B,zi(s,B)),xu=dt.frame),dt.frame>=yo){yo+=mt.autoSleep||120;var r=B._first;if((!r||!r._ts)&&mt.autoSleep&&dt._listeners.length<2){for(;r&&!r._ts;)r=r._next;r||dt.sleep()}}},t}(di);yt(st.prototype,{_lock:0,_hasPause:0,_forcing:0});var Dp=function(t,e,n,s,r,o,a){var u=new lt(this._pt,t,e,0,1,Hu,null,r),l=0,c=0,h,f,d,m,p,g,y,x;for(u.b=n,u.e=s,n+="",s+="",(y=~s.indexOf("random("))&&(s=hi(s)),o&&(x=[n,s],o(x,t,e),n=x[0],s=x[1]),f=n.match(dn)||[];h=dn.exec(s);)m=h[0],p=s.substring(l,h.index),d?d=(d+1)%5:p.substr(-5)==="rgba("&&(d=1),m!==f[c++]&&(g=parseFloat(f[c-1])||0,u._pt={_next:u._pt,p:p||c===1?p:",",s:g,c:m.charAt(1)==="="?Ce(g,m)-g:parseFloat(m)-g,m:d&&d<4?Math.round:0},l=dn.lastIndex);return u.c=l<s.length?s.substring(l,s.length):"",u.fp=a,(mu.test(s)||y)&&(u.e=0),this._pt=u,u},Ys=function(t,e,n,s,r,o,a,u,l,c){U(s)&&(s=s(r||0,t,o));var h=t[e],f=n!=="get"?n:U(h)?l?t[e.indexOf("set")||!U(t["get"+e.substr(3)])?e:"get"+e.substr(3)](l):t[e]():h,d=U(h)?l?Op:Xu:Xs,m;if(Q(s)&&(~s.indexOf("random(")&&(s=hi(s)),s.charAt(1)==="="&&(m=Ce(f,s)+(et(f)||0),(m||m===0)&&(s=m))),!c||f!==s||Hn)return!isNaN(f*s)&&s!==""?(m=new lt(this._pt,t,e,+f||0,s-(f||0),typeof h=="boolean"?Fp:qu,0,d),l&&(m.fp=l),a&&m.modifier(a,this,t),this._pt=m):(!h&&!(e in t)&&zs(e,s),Dp.call(this,t,e,f,s,d,u||mt.stringFilter,l))},Vp=function(t,e,n,s,r){if(U(t)&&(t=ei(t,r,e,n,s)),!Et(t)||t.style&&t.nodeType||nt(t)||du(t))return Q(t)?ei(t,r,e,n,s):t;var o={},a;for(a in t)o[a]=ei(t[a],r,e,n,s);return o},$u=function(t,e,n,s,r,o){var a,u,l,c;if(ft[t]&&(a=new ft[t]).init(r,a.rawVars?e[t]:Vp(e[t],s,r,o,n),n,s,o)!==!1&&(n._pt=u=new lt(n._pt,r,t,0,1,a.render,a,0,a.priority),n!==Ae))for(l=n._ptLookup[n._targets.indexOf(r)],c=a._props.length;c--;)l[a._props[c]]=u;return a},Kt,Hn,Gs=function i(t,e,n){var s=t.vars,r=s.ease,o=s.startAt,a=s.immediateRender,u=s.lazy,l=s.onUpdate,c=s.runBackwards,h=s.yoyoEase,f=s.keyframes,d=s.autoRevert,m=t._dur,p=t._startAt,g=t._targets,y=t.parent,x=y&&y.data==="nested"?y.vars.targets:g,v=t._overwrite==="auto"&&!Bs,T=t.timeline,_,b,S,P,w,M,R,E,k,G,Z,W,V;if(T&&(!f||!r)&&(r="none"),t._ease=me(r,Ee.ease),t._yEase=h?zu(me(h===!0?r:h,Ee.ease)):0,h&&t._yoyo&&!t._repeat&&(h=t._yEase,t._yEase=t._ease,t._ease=h),t._from=!T&&!!s.runBackwards,!T||f&&!s.stagger){if(E=g[0]?de(g[0]).harness:0,W=E&&s[E.prop],_=Ni(s,Us),p&&(p._zTime<0&&p.progress(1),e<0&&c&&a&&!d?p.render(-1,!0):p.revert(c&&m?Vi:sp),p._lazy=0),o){if(Zt(t._startAt=q.set(g,yt({data:"isStart",overwrite:!1,parent:y,immediateRender:!0,lazy:!p&&at(u),startAt:null,delay:0,onUpdate:l&&function(){return pt(t,"onUpdate")},stagger:0},o))),t._startAt._dp=0,t._startAt._sat=t,e<0&&(tt||!a&&!d)&&t._startAt.revert(Vi),a&&m&&e<=0&&n<=0){e&&(t._zTime=e);return}}else if(c&&m&&!p){if(e&&(a=!1),S=yt({overwrite:!1,data:"isFromStart",lazy:a&&!p&&at(u),immediateRender:a,stagger:0,parent:y},_),W&&(S[E.prop]=W),Zt(t._startAt=q.set(g,S)),t._startAt._dp=0,t._startAt._sat=t,e<0&&(tt?t._startAt.revert(Vi):t._startAt.render(-1,!0)),t._zTime=e,!a)i(t._startAt,L,L);else if(!e)return}for(t._pt=t._ptCache=0,u=m&&at(u)||u&&!m,b=0;b<g.length;b++){if(w=g[b],R=w._gsap||Ks(g)[b]._gsap,t._ptLookup[b]=G={},Kn[R.id]&&Gt.length&&ji(),Z=x===g?b:x.indexOf(w),E&&(k=new E).init(w,W||_,t,Z,x)!==!1&&(t._pt=P=new lt(t._pt,w,k.name,0,1,k.render,k,0,k.priority),k._props.forEach(function(K){G[K]=P}),k.priority&&(M=1)),!E||W)for(S in _)ft[S]&&(k=$u(S,_,t,Z,w,x))?k.priority&&(M=1):G[S]=P=Ys.call(t,w,S,"get",_[S],Z,x,0,s.stringFilter);t._op&&t._op[b]&&t.kill(w,t._op[b]),v&&t._pt&&(Kt=t,B.killTweensOf(w,G,t.globalTime(e)),V=!t.parent,Kt=0),t._pt&&u&&(Kn[R.id]=1)}M&&Zu(t),t._onInit&&t._onInit(t)}t._onUpdate=l,t._initted=(!t._op||t._pt)&&!V,f&&e<=0&&T.render(Tt,!0,!0)},Rp=function(t,e,n,s,r,o,a,u){var l=(t._pt&&t._ptCache||(t._ptCache={}))[e],c,h,f,d;if(!l)for(l=t._ptCache[e]=[],f=t._ptLookup,d=t._targets.length;d--;){if(c=f[d][e],c&&c.d&&c.d._pt)for(c=c.d._pt;c&&c.p!==e&&c.fp!==e;)c=c._next;if(!c)return Hn=1,t.vars[e]="+=0",Gs(t,a),Hn=0,u?li(e+" not eligible for reset"):1;l.push(c)}for(d=l.length;d--;)h=l[d],c=h._pt||h,c.s=(s||s===0)&&!r?s:c.s+(s||0)+o*c.c,c.c=n-c.s,h.e&&(h.e=Y(n)+et(h.e)),h.b&&(h.b=c.s+et(h.b))},Ep=function(t,e){var n=t[0]?de(t[0]).harness:0,s=n&&n.aliases,r,o,a,u;if(!s)return e;r=ke({},e);for(o in s)if(o in r)for(u=s[o].split(","),a=u.length;a--;)r[u[a]]=r[o];return r},kp=function(t,e,n,s){var r=e.ease||s||"power1.inOut",o,a;if(nt(e))a=n[t]||(n[t]=[]),e.forEach(function(u,l){return a.push({t:l/(e.length-1)*100,v:u,e:r})});else for(o in e)a=n[o]||(n[o]=[]),o==="ease"||a.push({t:parseFloat(t),v:e[o],e:r})},ei=function(t,e,n,s,r){return U(t)?t.call(e,n,s,r):Q(t)&&~t.indexOf("random(")?hi(t):t},Yu=Ws+"repeat,repeatDelay,yoyo,repeatRefresh,yoyoEase,autoRevert",Gu={};ut(Yu+",id,stagger,delay,duration,paused,scrollTrigger",function(i){return Gu[i]=1});var q=function(i){hu(t,i);function t(n,s,r,o){var a;typeof s=="number"&&(r.duration=s,s=r,r=null),a=i.call(this,o?s:Je(s))||this;var u=a.vars,l=u.duration,c=u.delay,h=u.immediateRender,f=u.stagger,d=u.overwrite,m=u.keyframes,p=u.defaults,g=u.scrollTrigger,y=u.yoyoEase,x=s.parent||B,v=(nt(n)||du(n)?Nt(n[0]):"length"in s)?[n]:bt(n),T,_,b,S,P,w,M,R;if(a._targets=v.length?Ks(v):li("GSAP target "+n+" not found. https://gsap.com",!mt.nullTargetWarn)||[],a._ptLookup=[],a._overwrite=d,m||f||Si(l)||Si(c)){if(s=a.vars,T=a.timeline=new st({data:"nested",defaults:p||{},targets:x&&x.data==="nested"?x.vars.targets:v}),T.kill(),T.parent=T._dp=Lt(a),T._start=0,f||Si(l)||Si(c)){if(S=v.length,M=f&&Ru(f),Et(f))for(P in f)~Yu.indexOf(P)&&(R||(R={}),R[P]=f[P]);for(_=0;_<S;_++)b=Ni(s,Gu),b.stagger=0,y&&(b.yoyoEase=y),R&&ke(b,R),w=v[_],b.duration=+ei(l,Lt(a),_,w,v),b.delay=(+ei(c,Lt(a),_,w,v)||0)-a._delay,!f&&S===1&&b.delay&&(a._delay=c=b.delay,a._start+=c,b.delay=0),T.to(w,b,M?M(_,w,v):0),T._ease=D.none;T.duration()?l=c=0:a.timeline=0}else if(m){Je(yt(T.vars.defaults,{ease:"none"})),T._ease=me(m.ease||s.ease||"none");var E=0,k,G,Z;if(nt(m))m.forEach(function(W){return T.to(v,W,">")}),T.duration();else{b={};for(P in m)P==="ease"||P==="easeEach"||kp(P,m[P],b,m.easeEach);for(P in b)for(k=b[P].sort(function(W,V){return W.t-V.t}),E=0,_=0;_<k.length;_++)G=k[_],Z={ease:G.e,duration:(G.t-(_?k[_-1].t:0))/100*l},Z[P]=G.v,T.to(v,Z,E),E+=Z.duration;T.duration()<l&&T.to({},{duration:l-T.duration()})}}l||a.duration(l=T.duration())}else a.timeline=0;return d===!0&&!Bs&&(Kt=Lt(a),B.killTweensOf(v),Kt=0),Ct(x,Lt(a),r),s.reversed&&a.reverse(),s.paused&&a.paused(!0),(h||!l&&!m&&a._start===H(x._time)&&at(h)&&cp(Lt(a))&&x.data!=="nested")&&(a._tTime=-L,a.render(Math.max(0,-c)||0)),g&&Cu(Lt(a),g),a}var e=t.prototype;return e.render=function(s,r,o){var a=this._time,u=this._tDur,l=this._dur,c=s<0,h=s>u-L&&!c?u:s<L?0:s,f,d,m,p,g,y,x,v,T;if(!l)fp(this,s,r,o);else if(h!==this._tTime||!s||o||!this._initted&&this._tTime||this._startAt&&this._zTime<0!==c||this._lazy){if(f=h,v=this.timeline,this._repeat){if(p=l+this._rDelay,this._repeat<-1&&c)return this.totalTime(p*100+s,r,o);if(f=H(h%p),h===u?(m=this._repeat,f=l):(g=H(h/p),m=~~g,m&&m===g?(f=l,m--):f>l&&(f=l)),y=this._yoyo&&m&1,y&&(T=this._yEase,f=l-f),g=Oe(this._tTime,p),f===a&&!o&&this._initted&&m===g)return this._tTime=h,this;m!==g&&(v&&this._yEase&&Uu(v,y),this.vars.repeatRefresh&&!y&&!this._lock&&f!==p&&this._initted&&(this._lock=o=1,this.render(H(p*m),!0).invalidate()._lock=0))}if(!this._initted){if(Mu(this,c?s:f,o,r,h))return this._tTime=0,this;if(a!==this._time&&!(o&&this.vars.repeatRefresh&&m!==g))return this;if(l!==this._dur)return this.render(s,r,o)}if(this._tTime=h,this._time=f,!this._act&&this._ts&&(this._act=1,this._lazy=0),this.ratio=x=(T||this._ease)(f/l),this._from&&(this.ratio=x=1-x),!a&&h&&!r&&!g&&(pt(this,"onStart"),this._tTime!==h))return this;for(d=this._pt;d;)d.r(x,d.d),d=d._next;v&&v.render(s<0?s:v._dur*v._ease(f/this._dur),r,o)||this._startAt&&(this._zTime=s),this._onUpdate&&!r&&(c&&$n(this,s,r,o),pt(this,"onUpdate")),this._repeat&&m!==g&&this.vars.onRepeat&&!r&&this.parent&&pt(this,"onRepeat"),(h===this._tDur||!h)&&this._tTime===h&&(c&&!this._onUpdate&&$n(this,s,!0,!0),(s||!l)&&(h===this._tDur&&this._ts>0||!h&&this._ts<0)&&Zt(this,1),!r&&!(c&&!a)&&(h||a||y)&&(pt(this,h===u?"onComplete":"onReverseComplete",!0),this._prom&&!(h<u&&this.timeScale()>0)&&this._prom()))}return this},e.targets=function(){return this._targets},e.invalidate=function(s){return(!s||!this.vars.runBackwards)&&(this._startAt=0),this._pt=this._op=this._onUpdate=this._lazy=this.ratio=0,this._ptLookup=[],this.timeline&&this.timeline.invalidate(s),i.prototype.invalidate.call(this,s)},e.resetTo=function(s,r,o,a,u){fi||dt.wake(),this._ts||this.play();var l=Math.min(this._dur,(this._dp._time-this._start)*this._ts),c;return this._initted||Gs(this,l),c=this._ease(l/this._dur),Rp(this,s,r,o,a,c,l,u)?this.resetTo(s,r,o,a,1):(Zi(this,0),this.parent||Su(this._dp,this,"_first","_last",this._dp._sort?"_start":0),this.render(0))},e.kill=function(s,r){if(r===void 0&&(r="all"),!s&&(!r||r==="all"))return this._lazy=this._pt=0,this.parent?Ye(this):this.scrollTrigger&&this.scrollTrigger.kill(!!tt),this;if(this.timeline){var o=this.timeline.totalDuration();return this.timeline.killTweensOf(s,r,Kt&&Kt.vars.overwrite!==!0)._first||Ye(this),this.parent&&o!==this.timeline.totalDuration()&&Le(this,this._dur*this.timeline._tDur/o,0,1),this}var a=this._targets,u=s?bt(s):a,l=this._ptLookup,c=this._pt,h,f,d,m,p,g,y;if((!r||r==="all")&&up(a,u))return r==="all"&&(this._pt=0),Ye(this);for(h=this._op=this._op||[],r!=="all"&&(Q(r)&&(p={},ut(r,function(x){return p[x]=1}),r=p),r=Ep(a,r)),y=a.length;y--;)if(~u.indexOf(a[y])){f=l[y],r==="all"?(h[y]=r,m=f,d={}):(d=h[y]=h[y]||{},m=r);for(p in m)g=f&&f[p],g&&((!("kill"in g.d)||g.d.kill(p)===!0)&&qi(this,g,"_pt"),delete f[p]),d!=="all"&&(d[p]=1)}return this._initted&&!this._pt&&c&&Ye(this),this},t.to=function(s,r){return new t(s,r,arguments[2])},t.from=function(s,r){return ti(1,arguments)},t.delayedCall=function(s,r,o,a){return new t(r,0,{immediateRender:!1,lazy:!1,overwrite:!1,delay:s,onComplete:r,onReverseComplete:r,onCompleteParams:o,onReverseCompleteParams:o,callbackScope:a})},t.fromTo=function(s,r,o){return ti(2,arguments)},t.set=function(s,r){return r.duration=0,r.repeatDelay||(r.repeat=0),new t(s,r)},t.killTweensOf=function(s,r,o){return B.killTweensOf(s,r,o)},t}(di);yt(q.prototype,{_targets:[],_lazy:0,_startAt:0,_op:0,_onInit:0});ut("staggerTo,staggerFrom,staggerFromTo",function(i){q[i]=function(){var t=new st,e=Gn.call(arguments,0);return e.splice(i==="staggerFromTo"?5:4,0,0),t[i].apply(t,e)}});var Xs=function(t,e,n){return t[e]=n},Xu=function(t,e,n){return t[e](n)},Op=function(t,e,n,s){return t[e](s.fp,n)},Lp=function(t,e,n){return t.setAttribute(e,n)},qs=function(t,e){return U(t[e])?Xu:Is(t[e])&&t.setAttribute?Lp:Xs},qu=function(t,e){return e.set(e.t,e.p,Math.round((e.s+e.c*t)*1e6)/1e6,e)},Fp=function(t,e){return e.set(e.t,e.p,!!(e.s+e.c*t),e)},Hu=function(t,e){var n=e._pt,s="";if(!t&&e.b)s=e.b;else if(t===1&&e.e)s=e.e;else{for(;n;)s=n.p+(n.m?n.m(n.s+n.c*t):Math.round((n.s+n.c*t)*1e4)/1e4)+s,n=n._next;s+=e.c}e.set(e.t,e.p,s,e)},Hs=function(t,e){for(var n=e._pt;n;)n.r(t,n.d),n=n._next},Bp=function(t,e,n,s){for(var r=this._pt,o;r;)o=r._next,r.p===s&&r.modifier(t,e,n),r=o},Ip=function(t){for(var e=this._pt,n,s;e;)s=e._next,e.p===t&&!e.op||e.op===t?qi(this,e,"_pt"):e.dep||(n=1),e=s;return!n},jp=function(t,e,n,s){s.mSet(t,e,s.m.call(s.tween,n,s.mt),s)},Zu=function(t){for(var e=t._pt,n,s,r,o;e;){for(n=e._next,s=r;s&&s.pr>e.pr;)s=s._next;(e._prev=s?s._prev:o)?e._prev._next=e:r=e,(e._next=s)?s._prev=e:o=e,e=n}t._pt=r},lt=function(){function i(e,n,s,r,o,a,u,l,c){this.t=n,this.s=r,this.c=o,this.p=s,this.r=a||qu,this.d=u||this,this.set=l||Xs,this.pr=c||0,this._next=e,e&&(e._prev=this)}var t=i.prototype;return t.modifier=function(n,s,r){this.mSet=this.mSet||this.set,this.set=jp,this.m=n,this.mt=r,this.tween=s},i}();ut(Ws+"parent,duration,ease,delay,overwrite,runBackwards,startAt,yoyo,immediateRender,repeat,repeatDelay,data,paused,reversed,lazy,callbackScope,stringFilter,id,yoyoEase,stagger,inherit,repeatRefresh,keyframes,autoRevert,scrollTrigger",function(i){return Us[i]=1});gt.TweenMax=gt.TweenLite=q;gt.TimelineLite=gt.TimelineMax=st;B=new st({sortChildren:!1,defaults:Ee,autoRemoveChildren:!0,id:"root",smoothChildTiming:!0});mt.stringFilter=Nu;var ge=[],Ei={},Np=[],Po=0,zp=0,_n=function(t){return(Ei[t]||Np).map(function(e){return e()})},Zn=function(){var t=Date.now(),e=[];t-Po>2&&(_n("matchMediaInit"),ge.forEach(function(n){var s=n.queries,r=n.conditions,o,a,u,l;for(a in s)o=At.matchMedia(s[a]).matches,o&&(u=1),o!==r[a]&&(r[a]=o,l=1);l&&(n.revert(),u&&e.push(n))}),_n("matchMediaRevert"),e.forEach(function(n){return n.onMatch(n,function(s){return n.add(null,s)})}),Po=t,_n("matchMedia"))},Qu=function(){function i(e,n){this.selector=n&&Xn(n),this.data=[],this._r=[],this.isReverted=!1,this.id=zp++,e&&this.add(e)}var t=i.prototype;return t.add=function(n,s,r){U(n)&&(r=s,s=n,n=U);var o=this,a=function(){var l=F,c=o.selector,h;return l&&l!==o&&l.data.push(o),r&&(o.selector=Xn(r)),F=o,h=s.apply(o,arguments),U(h)&&o._r.push(h),F=l,o.selector=c,o.isReverted=!1,h};return o.last=a,n===U?a(o,function(u){return o.add(null,u)}):n?o[n]=a:a},t.ignore=function(n){var s=F;F=null,n(this),F=s},t.getTweens=function(){var n=[];return this.data.forEach(function(s){return s instanceof i?n.push.apply(n,s.getTweens()):s instanceof q&&!(s.parent&&s.parent.data==="nested")&&n.push(s)}),n},t.clear=function(){this._r.length=this.data.length=0},t.kill=function(n,s){var r=this;if(n?function(){for(var a=r.getTweens(),u=r.data.length,l;u--;)l=r.data[u],l.data==="isFlip"&&(l.revert(),l.getChildren(!0,!0,!1).forEach(function(c){return a.splice(a.indexOf(c),1)}));for(a.map(function(c){return{g:c._dur||c._delay||c._sat&&!c._sat.vars.immediateRender?c.globalTime(0):-1/0,t:c}}).sort(function(c,h){return h.g-c.g||-1/0}).forEach(function(c){return c.t.revert(n)}),u=r.data.length;u--;)l=r.data[u],l instanceof st?l.data!=="nested"&&(l.scrollTrigger&&l.scrollTrigger.revert(),l.kill()):!(l instanceof q)&&l.revert&&l.revert(n);r._r.forEach(function(c){return c(n,r)}),r.isReverted=!0}():this.data.forEach(function(a){return a.kill&&a.kill()}),this.clear(),s)for(var o=ge.length;o--;)ge[o].id===this.id&&ge.splice(o,1)},t.revert=function(n){this.kill(n||{})},i}(),Up=function(){function i(e){this.contexts=[],this.scope=e,F&&F.data.push(this)}var t=i.prototype;return t.add=function(n,s,r){Et(n)||(n={matches:n});var o=new Qu(0,r||this.scope),a=o.conditions={},u,l,c;F&&!o.selector&&(o.selector=F.selector),this.contexts.push(o),s=o.add("onMatch",s),o.queries=n;for(l in n)l==="all"?c=1:(u=At.matchMedia(n[l]),u&&(ge.indexOf(o)<0&&ge.push(o),(a[l]=u.matches)&&(c=1),u.addListener?u.addListener(Zn):u.addEventListener("change",Zn)));return c&&s(o,function(h){return o.add(null,h)}),this},t.revert=function(n){this.kill(n||{})},t.kill=function(n){this.contexts.forEach(function(s){return s.kill(n,!0)})},i}(),Ui={registerPlugin:function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];e.forEach(function(s){return Bu(s)})},timeline:function(t){return new st(t)},getTweensOf:function(t,e){return B.getTweensOf(t,e)},getProperty:function(t,e,n,s){Q(t)&&(t=bt(t)[0]);var r=de(t||{}).get,o=n?wu:Pu;return n==="native"&&(n=""),t&&(e?o((ft[e]&&ft[e].get||r)(t,e,n,s)):function(a,u,l){return o((ft[a]&&ft[a].get||r)(t,a,u,l))})},quickSetter:function(t,e,n){if(t=bt(t),t.length>1){var s=t.map(function(c){return ht.quickSetter(c,e,n)}),r=s.length;return function(c){for(var h=r;h--;)s[h](c)}}t=t[0]||{};var o=ft[e],a=de(t),u=a.harness&&(a.harness.aliases||{})[e]||e,l=o?function(c){var h=new o;Ae._pt=0,h.init(t,n?c+n:c,Ae,0,[t]),h.render(1,h),Ae._pt&&Hs(1,Ae)}:a.set(t,u);return o?l:function(c){return l(t,u,n?c+n:c,a,1)}},quickTo:function(t,e,n){var s,r=ht.to(t,yt((s={},s[e]="+=0.1",s.paused=!0,s.stagger=0,s),n||{})),o=function(u,l,c){return r.resetTo(e,u,l,c)};return o.tween=r,o},isTweening:function(t){return B.getTweensOf(t,!0).length>0},defaults:function(t){return t&&t.ease&&(t.ease=me(t.ease,Ee.ease)),_o(Ee,t||{})},config:function(t){return _o(mt,t||{})},registerEffect:function(t){var e=t.name,n=t.effect,s=t.plugins,r=t.defaults,o=t.extendTimeline;(s||"").split(",").forEach(function(a){return a&&!ft[a]&&!gt[a]&&li(e+" effect requires "+a+" plugin.")}),pn[e]=function(a,u,l){return n(bt(a),yt(u||{},r),l)},o&&(st.prototype[e]=function(a,u,l){return this.add(pn[e](a,Et(u)?u:(l=u)&&{},this),l)})},registerEase:function(t,e){D[t]=me(e)},parseEase:function(t,e){return arguments.length?me(t,e):D},getById:function(t){return B.getById(t)},exportRoot:function(t,e){t===void 0&&(t={});var n=new st(t),s,r;for(n.smoothChildTiming=at(t.smoothChildTiming),B.remove(n),n._dp=0,n._time=n._tTime=B._time,s=B._first;s;)r=s._next,(e||!(!s._dur&&s instanceof q&&s.vars.onComplete===s._targets[0]))&&Ct(n,s,s._start-s._delay),s=r;return Ct(B,n,0),n},context:function(t,e){return t?new Qu(t,e):F},matchMedia:function(t){return new Up(t)},matchMediaRefresh:function(){return ge.forEach(function(t){var e=t.conditions,n,s;for(s in e)e[s]&&(e[s]=!1,n=1);n&&t.revert()})||Zn()},addEventListener:function(t,e){var n=Ei[t]||(Ei[t]=[]);~n.indexOf(e)||n.push(e)},removeEventListener:function(t,e){var n=Ei[t],s=n&&n.indexOf(e);s>=0&&n.splice(s,1)},utils:{wrap:xp,wrapYoyo:Tp,distribute:Ru,random:ku,snap:Eu,normalize:vp,getUnit:et,clamp:mp,splitColor:Iu,toArray:bt,selector:Xn,mapRange:Lu,pipe:yp,unitize:_p,interpolate:bp,shuffle:Vu},install:_u,effects:pn,ticker:dt,updateRoot:st.updateRoot,plugins:ft,globalTimeline:B,core:{PropTween:lt,globals:vu,Tween:q,Timeline:st,Animation:di,getCache:de,_removeLinkedListItem:qi,reverting:function(){return tt},context:function(t){return t&&F&&(F.data.push(t),t._ctx=F),F},suppressOverwrites:function(t){return Bs=t}}};ut("to,from,fromTo,delayedCall,set,killTweensOf",function(i){return Ui[i]=q[i]});dt.add(st.updateRoot);Ae=Ui.to({},{duration:0});var Wp=function(t,e){for(var n=t._pt;n&&n.p!==e&&n.op!==e&&n.fp!==e;)n=n._next;return n},Kp=function(t,e){var n=t._targets,s,r,o;for(s in e)for(r=n.length;r--;)o=t._ptLookup[r][s],o&&(o=o.d)&&(o._pt&&(o=Wp(o,s)),o&&o.modifier&&o.modifier(e[s],t,n[r],s))},vn=function(t,e){return{name:t,headless:1,rawVars:1,init:function(s,r,o){o._onInit=function(a){var u,l;if(Q(r)&&(u={},ut(r,function(c){return u[c]=1}),r=u),e){u={};for(l in r)u[l]=e(r[l]);r=u}Kp(a,r)}}}},ht=Ui.registerPlugin({name:"attr",init:function(t,e,n,s,r){var o,a,u;this.tween=n;for(o in e)u=t.getAttribute(o)||"",a=this.add(t,"setAttribute",(u||0)+"",e[o],s,r,0,0,o),a.op=o,a.b=u,this._props.push(o)},render:function(t,e){for(var n=e._pt;n;)tt?n.set(n.t,n.p,n.b,n):n.r(t,n.d),n=n._next}},{name:"endArray",headless:1,init:function(t,e){for(var n=e.length;n--;)this.add(t,n,t[n]||0,e[n],0,0,0,0,0,1)}},vn("roundProps",qn),vn("modifiers"),vn("snap",Eu))||Ui;q.version=st.version=ht.version="3.13.0";yu=1;js()&&Fe();D.Power0;D.Power1;D.Power2;D.Power3;D.Power4;D.Linear;D.Quad;D.Cubic;D.Quart;D.Quint;D.Strong;D.Elastic;D.Back;D.SteppedEase;D.Bounce;D.Sine;D.Expo;D.Circ;/*!
 * CSSPlugin 3.13.0
 * https://gsap.com
 *
 * Copyright 2008-2025, GreenSock. All rights reserved.
 * Subject to the terms at https://gsap.com/standard-license
 * @author: Jack Doyle, <EMAIL>
*/var wo,$t,Me,Zs,ce,So,Qs,$p=function(){return typeof window<"u"},zt={},ae=180/Math.PI,De=Math.PI/180,ve=Math.atan2,Ao=1e8,Js=/([A-Z])/g,Yp=/(left|right|width|margin|padding|x)/i,Gp=/[\s,\(]\S/,Mt={autoAlpha:"opacity,visibility",scale:"scaleX,scaleY",alpha:"opacity"},Qn=function(t,e){return e.set(e.t,e.p,Math.round((e.s+e.c*t)*1e4)/1e4+e.u,e)},Xp=function(t,e){return e.set(e.t,e.p,t===1?e.e:Math.round((e.s+e.c*t)*1e4)/1e4+e.u,e)},qp=function(t,e){return e.set(e.t,e.p,t?Math.round((e.s+e.c*t)*1e4)/1e4+e.u:e.b,e)},Hp=function(t,e){var n=e.s+e.c*t;e.set(e.t,e.p,~~(n+(n<0?-.5:.5))+e.u,e)},Ju=function(t,e){return e.set(e.t,e.p,t?e.e:e.b,e)},tl=function(t,e){return e.set(e.t,e.p,t!==1?e.b:e.e,e)},Zp=function(t,e,n){return t.style[e]=n},Qp=function(t,e,n){return t.style.setProperty(e,n)},Jp=function(t,e,n){return t._gsap[e]=n},tm=function(t,e,n){return t._gsap.scaleX=t._gsap.scaleY=n},em=function(t,e,n,s,r){var o=t._gsap;o.scaleX=o.scaleY=n,o.renderTransform(r,o)},im=function(t,e,n,s,r){var o=t._gsap;o[e]=n,o.renderTransform(r,o)},j="transform",ct=j+"Origin",nm=function i(t,e){var n=this,s=this.target,r=s.style,o=s._gsap;if(t in zt&&r){if(this.tfm=this.tfm||{},t!=="transform")t=Mt[t]||t,~t.indexOf(",")?t.split(",").forEach(function(a){return n.tfm[a]=Ft(s,a)}):this.tfm[t]=o.x?o[t]:Ft(s,t),t===ct&&(this.tfm.zOrigin=o.zOrigin);else return Mt.transform.split(",").forEach(function(a){return i.call(n,a,e)});if(this.props.indexOf(j)>=0)return;o.svg&&(this.svgo=s.getAttribute("data-svg-origin"),this.props.push(ct,e,"")),t=j}(r||e)&&this.props.push(t,e,r[t])},el=function(t){t.translate&&(t.removeProperty("translate"),t.removeProperty("scale"),t.removeProperty("rotate"))},sm=function(){var t=this.props,e=this.target,n=e.style,s=e._gsap,r,o;for(r=0;r<t.length;r+=3)t[r+1]?t[r+1]===2?e[t[r]](t[r+2]):e[t[r]]=t[r+2]:t[r+2]?n[t[r]]=t[r+2]:n.removeProperty(t[r].substr(0,2)==="--"?t[r]:t[r].replace(Js,"-$1").toLowerCase());if(this.tfm){for(o in this.tfm)s[o]=this.tfm[o];s.svg&&(s.renderTransform(),e.setAttribute("data-svg-origin",this.svgo||"")),r=Qs(),(!r||!r.isStart)&&!n[j]&&(el(n),s.zOrigin&&n[ct]&&(n[ct]+=" "+s.zOrigin+"px",s.zOrigin=0,s.renderTransform()),s.uncache=1)}},il=function(t,e){var n={target:t,props:[],revert:sm,save:nm};return t._gsap||ht.core.getCache(t),e&&t.style&&t.nodeType&&e.split(",").forEach(function(s){return n.save(s)}),n},nl,Jn=function(t,e){var n=$t.createElementNS?$t.createElementNS((e||"http://www.w3.org/1999/xhtml").replace(/^https/,"http"),t):$t.createElement(t);return n&&n.style?n:$t.createElement(t)},Pt=function i(t,e,n){var s=getComputedStyle(t);return s[e]||s.getPropertyValue(e.replace(Js,"-$1").toLowerCase())||s.getPropertyValue(e)||!n&&i(t,Be(e)||e,1)||""},Co="O,Moz,ms,Ms,Webkit".split(","),Be=function(t,e,n){var s=e||ce,r=s.style,o=5;if(t in r&&!n)return t;for(t=t.charAt(0).toUpperCase()+t.substr(1);o--&&!(Co[o]+t in r););return o<0?null:(o===3?"ms":o>=0?Co[o]:"")+t},ts=function(){$p()&&window.document&&(wo=window,$t=wo.document,Me=$t.documentElement,ce=Jn("div")||{style:{}},Jn("div"),j=Be(j),ct=j+"Origin",ce.style.cssText="border-width:0;line-height:0;position:absolute;padding:0",nl=!!Be("perspective"),Qs=ht.core.reverting,Zs=1)},Mo=function(t){var e=t.ownerSVGElement,n=Jn("svg",e&&e.getAttribute("xmlns")||"http://www.w3.org/2000/svg"),s=t.cloneNode(!0),r;s.style.display="block",n.appendChild(s),Me.appendChild(n);try{r=s.getBBox()}catch{}return n.removeChild(s),Me.removeChild(n),r},Do=function(t,e){for(var n=e.length;n--;)if(t.hasAttribute(e[n]))return t.getAttribute(e[n])},sl=function(t){var e,n;try{e=t.getBBox()}catch{e=Mo(t),n=1}return e&&(e.width||e.height)||n||(e=Mo(t)),e&&!e.width&&!e.x&&!e.y?{x:+Do(t,["x","cx","x1"])||0,y:+Do(t,["y","cy","y1"])||0,width:0,height:0}:e},rl=function(t){return!!(t.getCTM&&(!t.parentNode||t.ownerSVGElement)&&sl(t))},ye=function(t,e){if(e){var n=t.style,s;e in zt&&e!==ct&&(e=j),n.removeProperty?(s=e.substr(0,2),(s==="ms"||e.substr(0,6)==="webkit")&&(e="-"+e),n.removeProperty(s==="--"?e:e.replace(Js,"-$1").toLowerCase())):n.removeAttribute(e)}},Yt=function(t,e,n,s,r,o){var a=new lt(t._pt,e,n,0,1,o?tl:Ju);return t._pt=a,a.b=s,a.e=r,t._props.push(n),a},Vo={deg:1,rad:1,turn:1},rm={grid:1,flex:1},Qt=function i(t,e,n,s){var r=parseFloat(n)||0,o=(n+"").trim().substr((r+"").length)||"px",a=ce.style,u=Yp.test(e),l=t.tagName.toLowerCase()==="svg",c=(l?"client":"offset")+(u?"Width":"Height"),h=100,f=s==="px",d=s==="%",m,p,g,y;if(s===o||!r||Vo[s]||Vo[o])return r;if(o!=="px"&&!f&&(r=i(t,e,n,"px")),y=t.getCTM&&rl(t),(d||o==="%")&&(zt[e]||~e.indexOf("adius")))return m=y?t.getBBox()[u?"width":"height"]:t[c],Y(d?r/m*h:r/100*m);if(a[u?"width":"height"]=h+(f?o:s),p=s!=="rem"&&~e.indexOf("adius")||s==="em"&&t.appendChild&&!l?t:t.parentNode,y&&(p=(t.ownerSVGElement||{}).parentNode),(!p||p===$t||!p.appendChild)&&(p=$t.body),g=p._gsap,g&&d&&g.width&&u&&g.time===dt.time&&!g.uncache)return Y(r/g.width*h);if(d&&(e==="height"||e==="width")){var x=t.style[e];t.style[e]=h+s,m=t[c],x?t.style[e]=x:ye(t,e)}else(d||o==="%")&&!rm[Pt(p,"display")]&&(a.position=Pt(t,"position")),p===t&&(a.position="static"),p.appendChild(ce),m=ce[c],p.removeChild(ce),a.position="absolute";return u&&d&&(g=de(p),g.time=dt.time,g.width=p[c]),Y(f?m*r/h:m&&r?h/m*r:0)},Ft=function(t,e,n,s){var r;return Zs||ts(),e in Mt&&e!=="transform"&&(e=Mt[e],~e.indexOf(",")&&(e=e.split(",")[0])),zt[e]&&e!=="transform"?(r=mi(t,s),r=e!=="transformOrigin"?r[e]:r.svg?r.origin:Ki(Pt(t,ct))+" "+r.zOrigin+"px"):(r=t.style[e],(!r||r==="auto"||s||~(r+"").indexOf("calc("))&&(r=Wi[e]&&Wi[e](t,e,n)||Pt(t,e)||Tu(t,e)||(e==="opacity"?1:0))),n&&!~(r+"").trim().indexOf(" ")?Qt(t,e,r,n)+n:r},om=function(t,e,n,s){if(!n||n==="none"){var r=Be(e,t,1),o=r&&Pt(t,r,1);o&&o!==n?(e=r,n=o):e==="borderColor"&&(n=Pt(t,"borderTopColor"))}var a=new lt(this._pt,t.style,e,0,1,Hu),u=0,l=0,c,h,f,d,m,p,g,y,x,v,T,_;if(a.b=n,a.e=s,n+="",s+="",s.substring(0,6)==="var(--"&&(s=Pt(t,s.substring(4,s.indexOf(")")))),s==="auto"&&(p=t.style[e],t.style[e]=s,s=Pt(t,e)||s,p?t.style[e]=p:ye(t,e)),c=[n,s],Nu(c),n=c[0],s=c[1],f=n.match(Se)||[],_=s.match(Se)||[],_.length){for(;h=Se.exec(s);)g=h[0],x=s.substring(u,h.index),m?m=(m+1)%5:(x.substr(-5)==="rgba("||x.substr(-5)==="hsla(")&&(m=1),g!==(p=f[l++]||"")&&(d=parseFloat(p)||0,T=p.substr((d+"").length),g.charAt(1)==="="&&(g=Ce(d,g)+T),y=parseFloat(g),v=g.substr((y+"").length),u=Se.lastIndex-v.length,v||(v=v||mt.units[e]||T,u===s.length&&(s+=v,a.e+=v)),T!==v&&(d=Qt(t,e,p,v)||0),a._pt={_next:a._pt,p:x||l===1?x:",",s:d,c:y-d,m:m&&m<4||e==="zIndex"?Math.round:0});a.c=u<s.length?s.substring(u,s.length):""}else a.r=e==="display"&&s==="none"?tl:Ju;return mu.test(s)&&(a.e=0),this._pt=a,a},Ro={top:"0%",bottom:"100%",left:"0%",right:"100%",center:"50%"},am=function(t){var e=t.split(" "),n=e[0],s=e[1]||"50%";return(n==="top"||n==="bottom"||s==="left"||s==="right")&&(t=n,n=s,s=t),e[0]=Ro[n]||n,e[1]=Ro[s]||s,e.join(" ")},um=function(t,e){if(e.tween&&e.tween._time===e.tween._dur){var n=e.t,s=n.style,r=e.u,o=n._gsap,a,u,l;if(r==="all"||r===!0)s.cssText="",u=1;else for(r=r.split(","),l=r.length;--l>-1;)a=r[l],zt[a]&&(u=1,a=a==="transformOrigin"?ct:j),ye(n,a);u&&(ye(n,j),o&&(o.svg&&n.removeAttribute("transform"),s.scale=s.rotate=s.translate="none",mi(n,1),o.uncache=1,el(s)))}},Wi={clearProps:function(t,e,n,s,r){if(r.data!=="isFromStart"){var o=t._pt=new lt(t._pt,e,n,0,0,um);return o.u=s,o.pr=-10,o.tween=r,t._props.push(n),1}}},pi=[1,0,0,1,0,0],ol={},al=function(t){return t==="matrix(1, 0, 0, 1, 0, 0)"||t==="none"||!t},Eo=function(t){var e=Pt(t,j);return al(e)?pi:e.substr(7).match(pu).map(Y)},tr=function(t,e){var n=t._gsap||de(t),s=t.style,r=Eo(t),o,a,u,l;return n.svg&&t.getAttribute("transform")?(u=t.transform.baseVal.consolidate().matrix,r=[u.a,u.b,u.c,u.d,u.e,u.f],r.join(",")==="1,0,0,1,0,0"?pi:r):(r===pi&&!t.offsetParent&&t!==Me&&!n.svg&&(u=s.display,s.display="block",o=t.parentNode,(!o||!t.offsetParent&&!t.getBoundingClientRect().width)&&(l=1,a=t.nextElementSibling,Me.appendChild(t)),r=Eo(t),u?s.display=u:ye(t,"display"),l&&(a?o.insertBefore(t,a):o?o.appendChild(t):Me.removeChild(t))),e&&r.length>6?[r[0],r[1],r[4],r[5],r[12],r[13]]:r)},es=function(t,e,n,s,r,o){var a=t._gsap,u=r||tr(t,!0),l=a.xOrigin||0,c=a.yOrigin||0,h=a.xOffset||0,f=a.yOffset||0,d=u[0],m=u[1],p=u[2],g=u[3],y=u[4],x=u[5],v=e.split(" "),T=parseFloat(v[0])||0,_=parseFloat(v[1])||0,b,S,P,w;n?u!==pi&&(S=d*g-m*p)&&(P=T*(g/S)+_*(-p/S)+(p*x-g*y)/S,w=T*(-m/S)+_*(d/S)-(d*x-m*y)/S,T=P,_=w):(b=sl(t),T=b.x+(~v[0].indexOf("%")?T/100*b.width:T),_=b.y+(~(v[1]||v[0]).indexOf("%")?_/100*b.height:_)),s||s!==!1&&a.smooth?(y=T-l,x=_-c,a.xOffset=h+(y*d+x*p)-y,a.yOffset=f+(y*m+x*g)-x):a.xOffset=a.yOffset=0,a.xOrigin=T,a.yOrigin=_,a.smooth=!!s,a.origin=e,a.originIsAbsolute=!!n,t.style[ct]="0px 0px",o&&(Yt(o,a,"xOrigin",l,T),Yt(o,a,"yOrigin",c,_),Yt(o,a,"xOffset",h,a.xOffset),Yt(o,a,"yOffset",f,a.yOffset)),t.setAttribute("data-svg-origin",T+" "+_)},mi=function(t,e){var n=t._gsap||new Ku(t);if("x"in n&&!e&&!n.uncache)return n;var s=t.style,r=n.scaleX<0,o="px",a="deg",u=getComputedStyle(t),l=Pt(t,ct)||"0",c,h,f,d,m,p,g,y,x,v,T,_,b,S,P,w,M,R,E,k,G,Z,W,V,K,Ut,kt,ze,ee,er,Ot,ie;return c=h=f=p=g=y=x=v=T=0,d=m=1,n.svg=!!(t.getCTM&&rl(t)),u.translate&&((u.translate!=="none"||u.scale!=="none"||u.rotate!=="none")&&(s[j]=(u.translate!=="none"?"translate3d("+(u.translate+" 0 0").split(" ").slice(0,3).join(", ")+") ":"")+(u.rotate!=="none"?"rotate("+u.rotate+") ":"")+(u.scale!=="none"?"scale("+u.scale.split(" ").join(",")+") ":"")+(u[j]!=="none"?u[j]:"")),s.scale=s.rotate=s.translate="none"),S=tr(t,n.svg),n.svg&&(n.uncache?(K=t.getBBox(),l=n.xOrigin-K.x+"px "+(n.yOrigin-K.y)+"px",V=""):V=!e&&t.getAttribute("data-svg-origin"),es(t,V||l,!!V||n.originIsAbsolute,n.smooth!==!1,S)),_=n.xOrigin||0,b=n.yOrigin||0,S!==pi&&(R=S[0],E=S[1],k=S[2],G=S[3],c=Z=S[4],h=W=S[5],S.length===6?(d=Math.sqrt(R*R+E*E),m=Math.sqrt(G*G+k*k),p=R||E?ve(E,R)*ae:0,x=k||G?ve(k,G)*ae+p:0,x&&(m*=Math.abs(Math.cos(x*De))),n.svg&&(c-=_-(_*R+b*k),h-=b-(_*E+b*G))):(ie=S[6],er=S[7],kt=S[8],ze=S[9],ee=S[10],Ot=S[11],c=S[12],h=S[13],f=S[14],P=ve(ie,ee),g=P*ae,P&&(w=Math.cos(-P),M=Math.sin(-P),V=Z*w+kt*M,K=W*w+ze*M,Ut=ie*w+ee*M,kt=Z*-M+kt*w,ze=W*-M+ze*w,ee=ie*-M+ee*w,Ot=er*-M+Ot*w,Z=V,W=K,ie=Ut),P=ve(-k,ee),y=P*ae,P&&(w=Math.cos(-P),M=Math.sin(-P),V=R*w-kt*M,K=E*w-ze*M,Ut=k*w-ee*M,Ot=G*M+Ot*w,R=V,E=K,k=Ut),P=ve(E,R),p=P*ae,P&&(w=Math.cos(P),M=Math.sin(P),V=R*w+E*M,K=Z*w+W*M,E=E*w-R*M,W=W*w-Z*M,R=V,Z=K),g&&Math.abs(g)+Math.abs(p)>359.9&&(g=p=0,y=180-y),d=Y(Math.sqrt(R*R+E*E+k*k)),m=Y(Math.sqrt(W*W+ie*ie)),P=ve(Z,W),x=Math.abs(P)>2e-4?P*ae:0,T=Ot?1/(Ot<0?-Ot:Ot):0),n.svg&&(V=t.getAttribute("transform"),n.forceCSS=t.setAttribute("transform","")||!al(Pt(t,j)),V&&t.setAttribute("transform",V))),Math.abs(x)>90&&Math.abs(x)<270&&(r?(d*=-1,x+=p<=0?180:-180,p+=p<=0?180:-180):(m*=-1,x+=x<=0?180:-180)),e=e||n.uncache,n.x=c-((n.xPercent=c&&(!e&&n.xPercent||(Math.round(t.offsetWidth/2)===Math.round(-c)?-50:0)))?t.offsetWidth*n.xPercent/100:0)+o,n.y=h-((n.yPercent=h&&(!e&&n.yPercent||(Math.round(t.offsetHeight/2)===Math.round(-h)?-50:0)))?t.offsetHeight*n.yPercent/100:0)+o,n.z=f+o,n.scaleX=Y(d),n.scaleY=Y(m),n.rotation=Y(p)+a,n.rotationX=Y(g)+a,n.rotationY=Y(y)+a,n.skewX=x+a,n.skewY=v+a,n.transformPerspective=T+o,(n.zOrigin=parseFloat(l.split(" ")[2])||!e&&n.zOrigin||0)&&(s[ct]=Ki(l)),n.xOffset=n.yOffset=0,n.force3D=mt.force3D,n.renderTransform=n.svg?cm:nl?ul:lm,n.uncache=0,n},Ki=function(t){return(t=t.split(" "))[0]+" "+t[1]},xn=function(t,e,n){var s=et(e);return Y(parseFloat(e)+parseFloat(Qt(t,"x",n+"px",s)))+s},lm=function(t,e){e.z="0px",e.rotationY=e.rotationX="0deg",e.force3D=0,ul(t,e)},se="0deg",Ke="0px",re=") ",ul=function(t,e){var n=e||this,s=n.xPercent,r=n.yPercent,o=n.x,a=n.y,u=n.z,l=n.rotation,c=n.rotationY,h=n.rotationX,f=n.skewX,d=n.skewY,m=n.scaleX,p=n.scaleY,g=n.transformPerspective,y=n.force3D,x=n.target,v=n.zOrigin,T="",_=y==="auto"&&t&&t!==1||y===!0;if(v&&(h!==se||c!==se)){var b=parseFloat(c)*De,S=Math.sin(b),P=Math.cos(b),w;b=parseFloat(h)*De,w=Math.cos(b),o=xn(x,o,S*w*-v),a=xn(x,a,-Math.sin(b)*-v),u=xn(x,u,P*w*-v+v)}g!==Ke&&(T+="perspective("+g+re),(s||r)&&(T+="translate("+s+"%, "+r+"%) "),(_||o!==Ke||a!==Ke||u!==Ke)&&(T+=u!==Ke||_?"translate3d("+o+", "+a+", "+u+") ":"translate("+o+", "+a+re),l!==se&&(T+="rotate("+l+re),c!==se&&(T+="rotateY("+c+re),h!==se&&(T+="rotateX("+h+re),(f!==se||d!==se)&&(T+="skew("+f+", "+d+re),(m!==1||p!==1)&&(T+="scale("+m+", "+p+re),x.style[j]=T||"translate(0, 0)"},cm=function(t,e){var n=e||this,s=n.xPercent,r=n.yPercent,o=n.x,a=n.y,u=n.rotation,l=n.skewX,c=n.skewY,h=n.scaleX,f=n.scaleY,d=n.target,m=n.xOrigin,p=n.yOrigin,g=n.xOffset,y=n.yOffset,x=n.forceCSS,v=parseFloat(o),T=parseFloat(a),_,b,S,P,w;u=parseFloat(u),l=parseFloat(l),c=parseFloat(c),c&&(c=parseFloat(c),l+=c,u+=c),u||l?(u*=De,l*=De,_=Math.cos(u)*h,b=Math.sin(u)*h,S=Math.sin(u-l)*-f,P=Math.cos(u-l)*f,l&&(c*=De,w=Math.tan(l-c),w=Math.sqrt(1+w*w),S*=w,P*=w,c&&(w=Math.tan(c),w=Math.sqrt(1+w*w),_*=w,b*=w)),_=Y(_),b=Y(b),S=Y(S),P=Y(P)):(_=h,P=f,b=S=0),(v&&!~(o+"").indexOf("px")||T&&!~(a+"").indexOf("px"))&&(v=Qt(d,"x",o,"px"),T=Qt(d,"y",a,"px")),(m||p||g||y)&&(v=Y(v+m-(m*_+p*S)+g),T=Y(T+p-(m*b+p*P)+y)),(s||r)&&(w=d.getBBox(),v=Y(v+s/100*w.width),T=Y(T+r/100*w.height)),w="matrix("+_+","+b+","+S+","+P+","+v+","+T+")",d.setAttribute("transform",w),x&&(d.style[j]=w)},hm=function(t,e,n,s,r){var o=360,a=Q(r),u=parseFloat(r)*(a&&~r.indexOf("rad")?ae:1),l=u-s,c=s+l+"deg",h,f;return a&&(h=r.split("_")[1],h==="short"&&(l%=o,l!==l%(o/2)&&(l+=l<0?o:-o)),h==="cw"&&l<0?l=(l+o*Ao)%o-~~(l/o)*o:h==="ccw"&&l>0&&(l=(l-o*Ao)%o-~~(l/o)*o)),t._pt=f=new lt(t._pt,e,n,s,l,Xp),f.e=c,f.u="deg",t._props.push(n),f},ko=function(t,e){for(var n in e)t[n]=e[n];return t},fm=function(t,e,n){var s=ko({},n._gsap),r="perspective,force3D,transformOrigin,svgOrigin",o=n.style,a,u,l,c,h,f,d,m;s.svg?(l=n.getAttribute("transform"),n.setAttribute("transform",""),o[j]=e,a=mi(n,1),ye(n,j),n.setAttribute("transform",l)):(l=getComputedStyle(n)[j],o[j]=e,a=mi(n,1),o[j]=l);for(u in zt)l=s[u],c=a[u],l!==c&&r.indexOf(u)<0&&(d=et(l),m=et(c),h=d!==m?Qt(n,u,l,m):parseFloat(l),f=parseFloat(c),t._pt=new lt(t._pt,a,u,h,f-h,Qn),t._pt.u=m||0,t._props.push(u));ko(a,s)};ut("padding,margin,Width,Radius",function(i,t){var e="Top",n="Right",s="Bottom",r="Left",o=(t<3?[e,n,s,r]:[e+r,e+n,s+n,s+r]).map(function(a){return t<2?i+a:"border"+a+i});Wi[t>1?"border"+i:i]=function(a,u,l,c,h){var f,d;if(arguments.length<4)return f=o.map(function(m){return Ft(a,m,l)}),d=f.join(" "),d.split(f[0]).length===5?f[0]:d;f=(c+"").split(" "),d={},o.forEach(function(m,p){return d[m]=f[p]=f[p]||f[(p-1)/2|0]}),a.init(u,d,h)}});var ll={name:"css",register:ts,targetTest:function(t){return t.style&&t.nodeType},init:function(t,e,n,s,r){var o=this._props,a=t.style,u=n.vars.startAt,l,c,h,f,d,m,p,g,y,x,v,T,_,b,S,P;Zs||ts(),this.styles=this.styles||il(t),P=this.styles.props,this.tween=n;for(p in e)if(p!=="autoRound"&&(c=e[p],!(ft[p]&&$u(p,e,n,s,t,r)))){if(d=typeof c,m=Wi[p],d==="function"&&(c=c.call(n,s,t,r),d=typeof c),d==="string"&&~c.indexOf("random(")&&(c=hi(c)),m)m(this,t,p,c,n)&&(S=1);else if(p.substr(0,2)==="--")l=(getComputedStyle(t).getPropertyValue(p)+"").trim(),c+="",Xt.lastIndex=0,Xt.test(l)||(g=et(l),y=et(c)),y?g!==y&&(l=Qt(t,p,l,y)+y):g&&(c+=g),this.add(a,"setProperty",l,c,s,r,0,0,p),o.push(p),P.push(p,0,a[p]);else if(d!=="undefined"){if(u&&p in u?(l=typeof u[p]=="function"?u[p].call(n,s,t,r):u[p],Q(l)&&~l.indexOf("random(")&&(l=hi(l)),et(l+"")||l==="auto"||(l+=mt.units[p]||et(Ft(t,p))||""),(l+"").charAt(1)==="="&&(l=Ft(t,p))):l=Ft(t,p),f=parseFloat(l),x=d==="string"&&c.charAt(1)==="="&&c.substr(0,2),x&&(c=c.substr(2)),h=parseFloat(c),p in Mt&&(p==="autoAlpha"&&(f===1&&Ft(t,"visibility")==="hidden"&&h&&(f=0),P.push("visibility",0,a.visibility),Yt(this,a,"visibility",f?"inherit":"hidden",h?"inherit":"hidden",!h)),p!=="scale"&&p!=="transform"&&(p=Mt[p],~p.indexOf(",")&&(p=p.split(",")[0]))),v=p in zt,v){if(this.styles.save(p),d==="string"&&c.substring(0,6)==="var(--"&&(c=Pt(t,c.substring(4,c.indexOf(")"))),h=parseFloat(c)),T||(_=t._gsap,_.renderTransform&&!e.parseTransform||mi(t,e.parseTransform),b=e.smoothOrigin!==!1&&_.smooth,T=this._pt=new lt(this._pt,a,j,0,1,_.renderTransform,_,0,-1),T.dep=1),p==="scale")this._pt=new lt(this._pt,_,"scaleY",_.scaleY,(x?Ce(_.scaleY,x+h):h)-_.scaleY||0,Qn),this._pt.u=0,o.push("scaleY",p),p+="X";else if(p==="transformOrigin"){P.push(ct,0,a[ct]),c=am(c),_.svg?es(t,c,0,b,0,this):(y=parseFloat(c.split(" ")[2])||0,y!==_.zOrigin&&Yt(this,_,"zOrigin",_.zOrigin,y),Yt(this,a,p,Ki(l),Ki(c)));continue}else if(p==="svgOrigin"){es(t,c,1,b,0,this);continue}else if(p in ol){hm(this,_,p,f,x?Ce(f,x+c):c);continue}else if(p==="smoothOrigin"){Yt(this,_,"smooth",_.smooth,c);continue}else if(p==="force3D"){_[p]=c;continue}else if(p==="transform"){fm(this,c,t);continue}}else p in a||(p=Be(p)||p);if(v||(h||h===0)&&(f||f===0)&&!Gp.test(c)&&p in a)g=(l+"").substr((f+"").length),h||(h=0),y=et(c)||(p in mt.units?mt.units[p]:g),g!==y&&(f=Qt(t,p,l,y)),this._pt=new lt(this._pt,v?_:a,p,f,(x?Ce(f,x+h):h)-f,!v&&(y==="px"||p==="zIndex")&&e.autoRound!==!1?Hp:Qn),this._pt.u=y||0,g!==y&&y!=="%"&&(this._pt.b=l,this._pt.r=qp);else if(p in a)om.call(this,t,p,l,x?x+c:c);else if(p in t)this.add(t,p,l||t[p],x?x+c:c,s,r);else if(p!=="parseTransform"){zs(p,c);continue}v||(p in a?P.push(p,0,a[p]):typeof t[p]=="function"?P.push(p,2,t[p]()):P.push(p,1,l||t[p])),o.push(p)}}S&&Zu(this)},render:function(t,e){if(e.tween._time||!Qs())for(var n=e._pt;n;)n.r(t,n.d),n=n._next;else e.styles.revert()},get:Ft,aliases:Mt,getSetter:function(t,e,n){var s=Mt[e];return s&&s.indexOf(",")<0&&(e=s),e in zt&&e!==ct&&(t._gsap.x||Ft(t,"x"))?n&&So===n?e==="scale"?tm:Jp:(So=n||{})&&(e==="scale"?em:im):t.style&&!Is(t.style[e])?Zp:~e.indexOf("-")?Qp:qs(t,e)},core:{_removeProperty:ye,_getMatrix:tr}};ht.utils.checkPrefix=Be;ht.core.getStyleSaver=il;(function(i,t,e,n){var s=ut(i+","+t+","+e,function(r){zt[r]=1});ut(t,function(r){mt.units[r]="deg",ol[r]=1}),Mt[s[13]]=i+","+t,ut(n,function(r){var o=r.split(":");Mt[o[1]]=s[o[0]]})})("x,y,z,scale,scaleX,scaleY,xPercent,yPercent","rotation,rotationX,rotationY,skewX,skewY","transform,transformOrigin,svgOrigin,force3D,smoothOrigin,transformPerspective","0:translateX,1:translateY,2:translateZ,8:rotate,8:rotationZ,8:rotateZ,9:rotateX,10:rotateY");ut("x,y,z,top,right,bottom,left,width,height,fontSize,padding,margin,perspective",function(i){mt.units[i]="px"});ht.registerPlugin(ll);var dm=ht.registerPlugin(ll)||ht;dm.core.Tween;export{gm as A,mm as R,pl as a,dm as g,Bt as j,_m as m,A as r};
