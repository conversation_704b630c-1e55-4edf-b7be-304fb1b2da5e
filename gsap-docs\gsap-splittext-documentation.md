# GSAP SplitText Plugin Documentation

## Overview
SplitText is a GSAP plugin that allows you to split HTML text elements into characters, words, or lines for animated effects. It's now free for everyone to use.

## Key Features
- Split text into characters, words, or lines
- Responsive line splitting with autosplit
- Accessibility features
- Masking for reveal effects
- Custom text preparation
- Font loading awareness

## Basic Usage

### Registration
```javascript
// Register the plugin with GSAP
gsap.registerPlugin(SplitText);
```

### Creating a SplitText Instance
```javascript
// Create a new SplitText instance
const split = new SplitText("#yourElement", {
  type: "lines,words,chars", // Default behavior
  charsClass: "char", // Class for character spans
  wordsClass: "word", // Class for word spans
  linesClass: "line" // Class for line spans
});
```

### Animating Split Text
```javascript
// Animate characters with a stagger
gsap.from(split.chars, {
  duration: 0.5,
  opacity: 0,
  y: 80,
  stagger: 0.05
});
```

## Advanced Features

### Stagger Options
```javascript
// Advanced stagger configuration
gsap.from(split.chars, {
  duration: 0.5,
  opacity: 0,
  y: 80,
  stagger: {
    each: 0.05, // Time between each animation
    from: "random", // Start direction (start, end, center, random)
    repeat: 1, // Number of repeats
    yoyo: true // Should it reverse on repeat
  }
});
```

### Random Values
```javascript
// Using GSAP's random utility for varied animations
gsap.from(split.chars, {
  duration: 0.5,
  y: gsap.utils.random([30, -30]), // Random positive or negative Y value
  rotation: gsap.utils.random(-30, 30), // Random rotation between -30 and 30
  stagger: 0.05
});
```

### Font Loading
```javascript
// Wait for fonts to load before splitting
document.fonts.ready.then(() => {
  const split = new SplitText("#yourElement", { 
    type: "chars,words" 
  });
  
  // Your animation code here
});
```

### OnSplit Callback
```javascript
// New in version 3.13
const split = new SplitText("#yourElement", {
  type: "chars",
  onSplit: () => {
    // Animation code that runs after text is split
    gsap.from(split.chars, {
      duration: 0.5,
      opacity: 0,
      stagger: 0.05
    });
  }
});
```

## Responsive Design

### Autosplit for Responsive Lines
```javascript
// Enable responsive line splitting
const split = new SplitText("#yourElement", {
  type: "lines",
  autoSplit: true // Automatically re-splits text on resize
});
```

### Reverting the Split
```javascript
// Revert to original HTML after animation
gsap.from(split.chars, {
  duration: 0.5,
  opacity: 0,
  stagger: 0.05,
  onComplete: () => {
    split.revert(); // Clean up DOM after animation completes
  }
});
```

## Accessibility

### Default Accessibility Behavior
- Adds aria-label to parent element
- Hides child elements from screen readers using aria-hidden
- Preserves semantic meaning while allowing visual animation

### Complex Cases with Links
For text containing links or other semantic elements:
1. Create a duplicate element for screen readers
2. Apply screen-reader-only styles to the duplicate
3. Add aria-hidden to the split text element

```html
<!-- Screen reader accessible version -->
<h1 class="screen-reader-only">Hello <a href="#">World</a></h1>

<!-- Visually animated version -->
h1 id="split-text" aria-hidden="true">Hello <a href="#">World</a></h1>
```

## Text Preparation

### Custom Text Processing
```javascript
// Custom prepareText function for special cases
const split = new SplitText("#chineseText", {
  type: "words",
  prepareText: (text) => {
    // Custom logic for Chinese text segmentation
    return segmentChineseText(text);
  }
});
```

## Best Practices

### Performance Tips
- Only split what you need (characters OR words OR lines)
- Use revert() to clean up DOM after animations complete
- Be cautious with large amounts of text
- Use screen-reader-only elements for semantic content

### Troubleshooting
- Check console for font loading warnings
- Ensure proper CSS styling for split elements
- Test responsiveness with autoSplit enabled
- Verify accessibility with screen readers