# Deployment Guide

This guide covers deployment options and configurations for the Sri Lanka Tourist Guide website.

## Quick Start

The application is optimized for static hosting platforms and can be deployed to various services.

### Build for Production

```bash
npm run build
```

This creates an optimized production build in the `dist` folder.

## Deployment Platforms

### 1. Netlify (Recommended)

#### Automatic Deployment
1. Connect your GitHub repository to Netlify
2. Configure build settings:
   - **Build command**: `npm run build`
   - **Publish directory**: `dist`
   - **Node version**: 18 or higher

#### Manual Deployment
```bash
npm run build
npx netlify deploy --prod --dir=dist
```

#### Netlify Configuration
Create `netlify.toml` in the project root:

```toml
[build]
  publish = "dist"
  command = "npm run build"

[build.environment]
  NODE_VERSION = "18"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "*.js"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "*.css"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"
```

### 2. Vercel

#### Automatic Deployment
1. Connect your GitHub repository to Vercel
2. Vercel automatically detects Vite configuration
3. Deploy with default settings

#### Manual Deployment
```bash
npm run build
npx vercel --prod
```

#### Vercel Configuration
Create `vercel.json`:

```json
{
  "buildCommand": "npm run build",
  "outputDirectory": "dist",
  "framework": "vite",
  "rewrites": [
    {
      "source": "/(.*)",
      "destination": "/index.html"
    }
  ],
  "headers": [
    {
      "source": "/assets/(.*)",
      "headers": [
        {
          "key": "Cache-Control",
          "value": "public, max-age=31536000, immutable"
        }
      ]
    }
  ]
}
```

### 3. GitHub Pages

#### Setup
1. Install gh-pages: `npm install --save-dev gh-pages`
2. Add to package.json scripts:
   ```json
   {
     "scripts": {
       "deploy": "npm run build && gh-pages -d dist"
     }
   }
   ```
3. Update `vite.config.ts` for GitHub Pages:
   ```typescript
   export default defineConfig({
     base: '/repository-name/',
     // ... other config
   })
   ```

#### Deploy
```bash
npm run deploy
```

### 4. Firebase Hosting

#### Setup
```bash
npm install -g firebase-tools
firebase login
firebase init hosting
```

#### Configuration (`firebase.json`)
```json
{
  "hosting": {
    "public": "dist",
    "ignore": [
      "firebase.json",
      "**/.*",
      "**/node_modules/**"
    ],
    "rewrites": [
      {
        "source": "**",
        "destination": "/index.html"
      }
    ],
    "headers": [
      {
        "source": "/assets/**",
        "headers": [
          {
            "key": "Cache-Control",
            "value": "public, max-age=31536000, immutable"
          }
        ]
      }
    ]
  }
}
```

#### Deploy
```bash
npm run build
firebase deploy
```

## Environment Variables

### Development
Create `.env.local`:
```
VITE_API_URL=http://localhost:3000
VITE_ANALYTICS_ID=your-analytics-id
```

### Production
Set environment variables in your hosting platform:
- `VITE_API_URL`: Production API URL
- `VITE_ANALYTICS_ID`: Google Analytics ID
- `VITE_SENTRY_DSN`: Error tracking DSN

## Performance Optimization

### 1. Build Analysis
```bash
npm run build:analyze
```

### 2. Bundle Size Optimization
- Code splitting is configured in `vite.config.ts`
- Lazy loading for route components
- Tree shaking for unused code

### 3. Asset Optimization
- Images are optimized for web
- CSS is minified and purged
- JavaScript is minified with Terser

## CDN Configuration

### Cloudflare
1. Add your domain to Cloudflare
2. Enable these optimizations:
   - Auto Minify (CSS, JS, HTML)
   - Brotli compression
   - Browser Cache TTL: 1 year for assets

### AWS CloudFront
1. Create CloudFront distribution
2. Configure caching behaviors:
   - `/assets/*`: Cache for 1 year
   - `/index.html`: No cache
   - Default: Cache for 1 day

## Monitoring and Analytics

### 1. Google Analytics
Add to `index.html`:
```html
<!-- Google Analytics -->
<script async src="https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'GA_MEASUREMENT_ID');
</script>
```

### 2. Error Tracking
Install Sentry:
```bash
npm install @sentry/react @sentry/tracing
```

Configure in `main.tsx`:
```typescript
import * as Sentry from "@sentry/react";

Sentry.init({
  dsn: "YOUR_SENTRY_DSN",
  environment: import.meta.env.MODE,
});
```

### 3. Performance Monitoring
- Core Web Vitals tracking
- Real User Monitoring (RUM)
- Performance budgets

## Security

### 1. Content Security Policy
Add to `index.html`:
```html
<meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' https://www.googletagmanager.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:;">
```

### 2. Security Headers
Configure in your hosting platform:
- `X-Frame-Options: DENY`
- `X-Content-Type-Options: nosniff`
- `Referrer-Policy: strict-origin-when-cross-origin`

## SEO Optimization

### 1. Meta Tags
- Implemented with React Helmet
- Open Graph tags for social sharing
- Twitter Card support

### 2. Sitemap
Generate sitemap for better SEO:
```bash
npm install --save-dev vite-plugin-sitemap
```

### 3. Robots.txt
Create `public/robots.txt`:
```
User-agent: *
Allow: /
Sitemap: https://yourdomain.com/sitemap.xml
```

## Troubleshooting

### Common Issues

1. **Blank page after deployment**
   - Check browser console for errors
   - Verify base URL configuration
   - Ensure all assets are loading correctly

2. **404 errors on refresh**
   - Configure SPA redirects
   - Ensure server returns index.html for all routes

3. **Slow loading**
   - Check bundle size with analyzer
   - Optimize images and assets
   - Enable compression

### Debug Mode
Enable debug mode in development:
```typescript
// In vite.config.ts
export default defineConfig({
  define: {
    __DEBUG__: JSON.stringify(process.env.NODE_ENV === 'development')
  }
})
```

## Maintenance

### 1. Dependency Updates
```bash
npm audit
npm update
```

### 2. Performance Monitoring
- Monitor Core Web Vitals
- Check bundle size regularly
- Review error logs

### 3. Content Updates
- Update destination information
- Add new activities and attractions
- Refresh images and content

## Support

For deployment issues:
1. Check this documentation
2. Review hosting platform docs
3. Check GitHub issues
4. Contact support team

## Checklist

Before deploying:
- [ ] Run `npm run build` successfully
- [ ] Test production build locally with `npm run preview`
- [ ] Verify all environment variables are set
- [ ] Check bundle size with analyzer
- [ ] Test on different devices and browsers
- [ ] Verify SEO meta tags
- [ ] Test accessibility compliance
- [ ] Configure monitoring and analytics
- [ ] Set up error tracking
- [ ] Configure security headers
- [ ] Test performance metrics
