{"name": "sri-lanka-tourist-guide", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "build:analyze": "npm run build && npx vite-bundle-analyzer dist", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "type-check": "tsc --noEmit", "test": "echo \"No tests specified\" && exit 0"}, "dependencies": {"@gsap/react": "^2.1.2", "@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.1.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.18.1", "gsap": "^3.13.0", "leaflet": "^1.9.4", "lottie-react": "^2.4.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.58.1", "react-leaflet": "^4.2.1", "react-router-dom": "^6.28.1", "zod": "^3.25.67"}, "devDependencies": {"@types/leaflet": "^1.9.18", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@typescript-eslint/eslint-plugin": "^8.15.0", "@typescript-eslint/parser": "^8.15.0", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "eslint": "^9.15.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "postcss": "^8.5.1", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.13", "tailwindcss": "^3.4.15", "terser": "^5.43.1", "typescript": "^5.6.3", "vite": "^6.0.1"}}